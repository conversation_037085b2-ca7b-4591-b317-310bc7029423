import { ref } from 'vue';

// Storage key for sidebar state
const SIDEBAR_STORAGE_KEY = 'flowcamp_sidebar_open';

// Create a reactive reference for sidebar state
const isSidebarOpen = ref(false);

// Initialize from localStorage if available
if (typeof window !== 'undefined') {
  const storedState = localStorage.getItem(SIDEBAR_STORAGE_KEY);
  if (storedState !== null) {
    isSidebarOpen.value = storedState === 'true';
  }
}

// Function to toggle sidebar state
const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value;

  // Save to localStorage
  if (typeof window !== 'undefined') {
    localStorage.setItem(SIDEBAR_STORAGE_KEY, isSidebarOpen.value.toString());
  }
};

// Function to close sidebar
const closeSidebar = () => {
  isSidebarOpen.value = false;

  // Save to localStorage
  if (typeof window !== 'undefined') {
    localStorage.setItem(SIDEBAR_STORAGE_KEY, 'false');
  }
};

// Function to open sidebar
const openSidebar = () => {
  isSidebarOpen.value = true;

  // Save to localStorage
  if (typeof window !== 'undefined') {
    localStorage.setItem(SIDEBAR_STORAGE_KEY, 'true');
  }
};

// Export the state and functions
export { isSidebarOpen, toggleSidebar, closeSidebar, openSidebar };
