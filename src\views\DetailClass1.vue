<template>
  <StudentLayoutWrapper>
    <StudentBreadcrumb :items="breadcrumbItems" />

    <!-- Feedback Dialog -->
    <FeedbackDialog
      :show="showFeedbackDialog"
      :classId="currentClass?.id"
      @close="closeFeedbackDialog"
      @feedback-sent="handleFeedbackSent"
    />

    <!-- Feedback Sent Dialog -->
    <DialogBox
      :show="showFeedbackSentDialog"
      type="feedbackSent"
      @close="closeFeedbackSentDialog"
    />

    <!-- Certificate View -->
    <CertificateView
      :show="showCertificateView"
      :classId="currentClass?.id"
      :classTitle="currentClass?.title || 'Class Title'"
      :studentName="'Student Name'"
      :completionDate="currentClass?.completionDate"
      @close="closeCertificateView"
      @download="handleCertificateDownload"
    />

    <!-- Certificate Downloaded Dialog -->
    <DialogBox
      :show="showCertificateDownloadedDialog"
      type="certificateDownloaded"
      @close="closeCertificateDownloadedDialog"
    />

    <div class="max-w-7xl mx-auto">
        <!-- Title & Posted Date -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6">
          <h1 class="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-2 sm:mb-0 break-words">{{ currentClass.title }}</h1>
          <span class="text-gray-500 text-xs sm:text-sm whitespace-nowrap">Posted {{ formatDate(currentClass.postedDate) }} {{ currentClass.postedTime }}</span>
        </div>

        <!-- Congratulations Message for Completed Class -->
        <div v-if="displayAsCompleted" class="bg-white mb-6 sm:mb-8 p-4 sm:p-6 border border-gray-200 rounded-lg shadow-sm">
          <div class="flex items-center mb-3">
            <div class="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 border-2 border-orange rounded-full mr-3 flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6 text-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h2 class="text-lg sm:text-xl font-bold text-gray-900">Congratulations, you passed this class!</h2>
          </div>
          <p class="text-sm sm:text-base text-gray-600 ml-2">
            Well done on reaching the finish line! Your success is well-deserved.
          </p>
        </div>

        <!-- Description -->
        <div v-if="!displayAsCompleted" class="bg-white mb-6 sm:mb-8">
          <p class="text-sm sm:text-base md:text-lg text-gray-700 leading-relaxed">
            {{ currentClass.description }}
          </p>
        </div>

        <!-- Learning Materials Button - Only show for non-completed classes -->
        <div v-if="!displayAsCompleted" class="flex justify-center sm:justify-end mb-4 sm:mb-6">
          <button
            @click="navigateToMaterials"
            class="bg-orange text-white w-full sm:w-auto px-4 sm:px-6 py-2 sm:py-2.5 rounded-md text-sm sm:text-base font-semibold hover:bg-orange-dark transition-colors shadow-sm flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
            Learning Materials
          </button>
        </div>

        <!-- Learning Progress Box (only show if class is not completed) -->
        <div v-if="!displayAsCompleted" class="max-w-7xl mx-auto p-4 sm:p-6 mb-6 sm:mb-8 border border-gray-200 rounded-lg bg-white shadow-sm">
          <div class="flex items-center mb-3 sm:mb-4">
            <div
              class="flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8 border-2 border-orange rounded-full mr-2 sm:mr-3 flex-shrink-0">
              <svg class="h-3 w-3 sm:h-5 sm:w-5 text-orange" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M17 11l4-4m0 0l4 4m-4-4v12m-4-4l-4 4m4-4H3" />
              </svg>
            </div>
            <span class="ml-2 sm:ml-4 text-base sm:text-lg text-gray-700 font-medium">Learning Progress</span>
          </div>

          <!-- Progress Bar -->
          <div class="w-full bg-gray-200 h-2 sm:h-3 rounded-full mb-1 sm:mb-2 overflow-hidden">
            <div class="bg-orange h-2 sm:h-3 rounded-full transition-all duration-500 ease-in-out" :style="{ width: progressPercentage + '%' }"></div>
          </div>

          <!-- Progress Percentage -->
          <div class="flex justify-between items-center mt-1 sm:mt-2 text-gray-500 text-xs sm:text-sm">
            <span>Progress</span>
            <span class="font-medium">{{ progressPercentage }}%</span>
          </div>
        </div>

        <!-- Flex Container for Task History and Submission History -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 lg:gap-8 mb-6 sm:mb-8">
          <!-- Task History (for non-completed classes) / Submission History (for completed classes) -->
          <div class="w-full">
            <div class="border border-gray-200 rounded-lg bg-white shadow-sm p-4 sm:p-6 h-full transition-all hover:shadow-md">
              <!-- Task/Submission History Icon -->
              <div class="flex items-center mb-3 sm:mb-4">
                <div
                  class="flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8 border-2 border-orange rounded-full mr-2 sm:mr-3 flex-shrink-0">
                  <img :src="displayAsCompleted ? '/submission.png' : '/task.png'"
                       :alt="displayAsCompleted ? 'Submission Icon' : 'Task Icon'"
                       class="h-3 w-3 sm:h-5 sm:w-5" />
                </div>
                <h2 class="text-base sm:text-xl font-semibold text-gray-700">
                  {{ displayAsCompleted ? 'Submission History' : 'Task History' }}
                </h2>
              </div>

              <!-- Description text -->
              <div v-if="!displayAsCompleted">
                <p class="ml-2 text-gray-500 text-xs sm:text-sm mb-4 sm:mb-5">
                  Complete your tasks and track your learning progress effectively.
                </p>
              </div>

              <!-- For completed classes, show submission with marks -->
              <div v-if="displayAsCompleted" class="grid grid-cols-[1fr,auto] gap-2 sm:gap-4 mb-3 sm:mb-4 px-2">
                <h3 class="text-sm sm:text-base font-medium text-gray-700">Submission</h3>
                <p class="text-sm sm:text-base font-medium text-gray-700 text-center min-w-[90px] sm:min-w-[115px]">Mark</p>
              </div>

              <!-- List of tasks (for non-completed classes) -->
              <div v-if="!displayAsCompleted" class="space-y-3 sm:space-y-4 mb-4 sm:mb-5">
                <div v-for="material in taskMaterials" :key="material.id"
                    class="rounded-lg transition-all cursor-pointer hover:bg-gray-50 border border-gray-100"
                    @click="navigateToTask(material.id)">
                  <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center p-3 group">
                    <div class="mb-2 sm:mb-0 w-full sm:w-auto pr-2">
                      <h3 class="text-orange font-medium text-sm sm:text-base group-hover:translate-x-1 transition-transform">
                        {{ material.title }}
                      </h3>
                      <p class="text-gray-500 text-xs sm:text-sm group-hover:translate-x-1 transition-transform flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        Due {{ formatDate(material.dueDate) }} {{ material.dueTime }}
                      </p>
                    </div>
                    <span class="text-xs sm:text-sm font-medium px-2 sm:px-3 py-1 rounded-full shadow-sm whitespace-nowrap"
                          :class="getStatusClass(material.taskStatus)">
                      {{ formatTaskStatus(material.taskStatus) }}
                    </span>
                  </div>
                </div>
                <div v-if="!taskMaterials.length"
                    class="bg-gray-50 rounded-lg p-4 text-center text-gray-500 text-xs sm:text-sm border border-gray-100">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mx-auto mb-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  You have no task history yet.
                </div>
              </div>

              <!-- List of submissions with marks (for completed classes) -->
              <div v-if="displayAsCompleted" class="space-y-3 sm:space-y-4 mb-4 sm:mb-5">
                <div v-for="submission in submissionHistory" :key="submission.id"
                     class="rounded-lg transition-all cursor-pointer hover:bg-gray-50 border border-gray-100"
                     @click="navigateToSubmission(submission.id)">
                  <div class="grid grid-cols-[1fr,auto] gap-2 sm:gap-4 items-center p-3 group">
                    <div class="pl-1">
                      <h3 class="text-gray-800 font-medium text-sm sm:text-base group-hover:translate-x-1 transition-transform">{{ submission.title }}</h3>
                      <p class="text-gray-500 text-xs sm:text-sm group-hover:translate-x-1 transition-transform">
                        Returned {{ formatDate(submission.submittedDate) }} {{ formatTime(submission.submittedTime) }}
                      </p>
                    </div>
                    <span class="text-xs sm:text-sm font-medium text-center min-w-[80px] sm:min-w-[100px] whitespace-nowrap">
                      {{ submission.taskScore || (submission.taskStatus === 'reviewed' ? (submission.id % 10 === 0 ? 100 : submission.id % 10 === 1 ? 95 : submission.id % 10 === 2 ? 90 : 95) : 100) }}
                    </span>
                  </div>
                </div>
                <div v-if="!submissionHistory.length"
                    class="bg-gray-50 rounded-lg p-4 text-center text-gray-500 text-xs sm:text-sm border border-gray-100">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mx-auto mb-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  You have no submission history yet.
                </div>
              </div>

              <!-- See More button -->
              <div v-if="(!displayAsCompleted && totalTaskMaterialsCount > 3) ||
                         (displayAsCompleted && totalSubmissionHistoryCount > 3)"
                   class="flex justify-center sm:justify-end">
                <button
                  @click="displayAsCompleted ? showSubmissionHistory = true : showTaskHistory = true"
                  class="text-white bg-orange hover:bg-orange-dark font-medium text-xs sm:text-sm py-2 px-4 sm:px-6 rounded-md shadow-sm flex items-center transition-colors">
                  <span>See More</span>
                </button>
              </div>
            </div>
          </div>

          <!-- Task History Dialog -->
          <TaskHistoryDialog
            :show="showTaskHistory"
            :classId="currentClass?.id"
            @close="showTaskHistory = false"
            @task-submitted="handleTaskSubmitted"
          />

          <!-- Submission History Dialog -->
          <SubmissionHistoryDialog
            :show="showSubmissionHistory"
            :classId="currentClass?.id"
            @close="showSubmissionHistory = false"
          />

          <!-- Submission History (for non-completed classes) / Feedback (for completed classes) -->
          <div class="w-full">
            <div class="border border-gray-200 rounded-lg bg-white shadow-sm p-4 sm:p-6 h-full transition-all hover:shadow-md">
              <!-- Submission History / Feedback Icon -->
              <div class="flex items-center mb-3 sm:mb-4">
                <div
                  class="flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8 border-2 border-orange rounded-full mr-2 sm:mr-3 flex-shrink-0">
                  <template v-if="displayAsCompleted">
                    <!-- E-Certificate Icon (when feedback has been provided) -->
                    <template v-if="hasFeedback">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-5 sm:w-5 text-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </template>
                    <!-- Feedback SVG Icon (when feedback has not been provided) -->
                    <template v-else>
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-5 sm:w-5 text-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                      </svg>
                    </template>
                  </template>
                  <template v-else>
                    <img src="/submission.png" alt="Submission Icon" class="h-3 w-3 sm:h-5 sm:w-5" />
                  </template>
                </div>
                <h2 class="text-base sm:text-xl font-semibold text-gray-700">
                  {{ displayAsCompleted ? (hasFeedback ? 'E-Certificate' : 'Feedback') : 'Submission History' }}
                </h2>
              </div>

              <!-- For non-completed classes: Submission History -->
              <template v-if="!displayAsCompleted">
                <div class="grid grid-cols-[1fr,auto] gap-2 sm:gap-4 mb-3 sm:mb-4 px-2">
                  <h3 class="text-sm sm:text-base font-medium text-gray-700">Submission</h3>
                  <p class="text-sm sm:text-base font-medium text-gray-700 text-center min-w-[90px] sm:min-w-[115px]">Status</p>
                </div>

                <!-- Submission content -->
                <div>
                  <!-- Show empty state message when there are no submissions -->
                  <div v-if="submissionHistory.length === 0" class="bg-gray-50 rounded-lg p-4 text-center text-gray-500 text-xs sm:text-sm border border-gray-100">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mx-auto mb-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    You have no submission history yet.
                  </div>

                  <!-- List of submissions -->
                  <div v-else class="space-y-3 sm:space-y-4 mb-4 sm:mb-5">
                    <div v-for="submission in submissionHistory" :key="submission.id"
                        class="rounded-lg transition-all cursor-pointer hover:bg-gray-50 border border-gray-100"
                        @click="navigateToSubmission(submission.id)">
                      <div class="grid grid-cols-[1fr,auto] gap-2 sm:gap-4 items-center p-3 group">
                        <div class="pl-1">
                          <h3 class="text-gray-800 font-medium text-sm sm:text-base group-hover:translate-x-1 transition-transform">{{ submission.title }}</h3>
                          <p v-if="submission.taskStatus === 'past_due'" class="text-red-500 text-xs sm:text-sm group-hover:translate-x-1 transition-transform flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span class="font-medium">Due date passed: </span> {{ formatDate(submission.dueDate) }} {{ submission.dueTime }}
                          </p>
                          <p v-else class="text-gray-500 text-xs sm:text-sm group-hover:translate-x-1 transition-transform flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            Submitted at {{ formatDate(submission.submittedDate) }} {{ submission.submittedTime }}
                          </p>
                        </div>
                        <span class="text-xs sm:text-sm font-medium px-2 sm:px-3 py-1 rounded-full shadow-sm text-center min-w-[80px] sm:min-w-[100px] whitespace-nowrap"
                              :class="getStatusClass(submission.taskStatus)">
                          {{ formatTaskStatus(submission.taskStatus) }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                  <!-- only show if there are more than 3 submissions -->
                  <div v-if="totalSubmissionHistoryCount > 3" class="flex justify-center sm:justify-end">
                    <button
                      @click="showSubmissionHistory = true"
                      class="text-white bg-orange hover:bg-orange-dark font-medium text-xs sm:text-sm py-2 px-4 sm:px-6 rounded-md shadow-sm flex items-center transition-colors">
                      <span>See More</span>
                    </button>
                  </div>
              </template>

              <!-- For completed classes: Feedback section or E-Certificate section -->
              <template v-else>
                <!-- Show Feedback section if feedback has not been provided -->
                <template v-if="!hasFeedback">
                  <p class="text-sm sm:text-base text-gray-600 mb-6">
                    Share your thoughts and help us improve your learning experience.
                  </p>

                  <div class="flex justify-center">
                    <button
                      @click="provideFeedback"
                      class="text-white bg-orange hover:bg-orange-dark font-medium text-sm sm:text-base py-2 px-6 sm:px-8 rounded-md shadow-sm flex items-center transition-colors">
                      <span>Feedback</span>
                    </button>
                  </div>
                </template>

                <!-- Show E-Certificate section if feedback has been provided -->
                <template v-else>
                  <div class="flex flex-col items-center">
                    <!-- Certificate Icon -->
                    <div class="mb-4">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </div>

                    <h3 class="text-lg sm:text-xl font-semibold text-gray-800 mb-2">E-Certificate</h3>
                    <p class="text-sm sm:text-base text-gray-600 mb-6 text-center">
                      Receive official recognition for your learning achievements and skills.
                    </p>

                    <button
                      @click="viewCertificate"
                      class="text-white bg-orange hover:bg-orange-dark font-medium text-sm sm:text-base py-2 px-6 sm:px-8 rounded-md shadow-sm flex items-center transition-colors">
                      <span>View</span>
                    </button>
                  </div>
                </template>
              </template>
            </div>
          </div>
        </div>

        <!-- Mentoring Platform & Student Leaderboard (for non-completed classes) / Upcoming Sessions (for completed classes) -->
        <div v-if="!displayAsCompleted" class="grid grid-cols-1 md:grid-cols-2 gap-6 lg:gap-8 mb-6 sm:mb-8">
          <!-- Mentoring Platform -->
          <div class="w-full">
            <div class="border border-gray-200 rounded-lg bg-white shadow-sm p-4 sm:p-6 h-full transition-all hover:shadow-md flex flex-col">
              <div class="flex items-center mb-3 sm:mb-4">
                <div class="flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8 border-2 border-orange rounded-full mr-2 sm:mr-3 flex-shrink-0">
                  <img src="/mentoring.png" alt="Mentoring Icon" class="h-3 w-3 sm:h-5 sm:w-5" />
                </div>
                <h2 class="text-base sm:text-xl font-semibold text-gray-700">Mentoring Platform</h2>
              </div>

              <p class="text-sm sm:text-base text-gray-600 ml-2">
                Consult directly with experts to solve your difficulties.
              </p>

              <div class="flex-grow"></div>

              <div class="flex justify-end mt-4 sm:mt-6">
                <button
                  @click="navigateToMentoring"
                  class="flex items-center text-orange hover:text-orange-dark font-medium text-xs sm:text-sm transition-colors group">
                  <span>To the Mentoring Platform</span>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1 transform transition-transform duration-200 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- Student Leaderboard -->
          <div class="w-full">
            <div class="border border-gray-200 rounded-lg bg-white shadow-sm p-4 sm:p-6 h-full transition-all hover:shadow-md">
              <div class="flex justify-between items-center mb-4 sm:mb-5">
                <div class="flex items-center">
                  <!-- Leaderboard Icon -->
                  <div
                    class="flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8 border-2 border-orange rounded-full mr-2 sm:mr-3 flex-shrink-0">
                    <img src="/leaderboard.png" alt="Leaderboard Icon" class="h-3 w-3 sm:h-5 sm:w-5 text-orange" />
                  </div>
                  <h2 class="text-base sm:text-xl font-semibold text-gray-700">
                    <span>Student Leaderboard</span>
                  </h2>
                </div>
                <button
                  @click="navigateToLeaderboard"
                  class="flex items-center text-xs sm:text-sm text-orange hover:text-orange-dark font-medium transition-colors group"
                  title="View Leaderboard">
                  <span>View All</span>
                  <img src="/leaderboardNextPage.png" alt="Next Page" class="h-4 w-4 ml-2 transform transition-transform duration-200 group-hover:translate-x-1" />
                </button>
              </div>

              <!-- Leaderboard List -->
              <div class="space-y-2 sm:space-y-3">
                <div v-for="(student, index) in topStudents" :key="student.id"
                     class="flex items-center justify-between p-2 rounded-lg transition-colors">
                  <div class="flex items-center">
                    <!-- Rank Badge with different colors for top 3 -->
                    <div
                      class="w-6 h-6 sm:w-7 sm:h-7 rounded-full flex items-center justify-center mr-2 sm:mr-3 flex-shrink-0 text-white font-medium"
                      :class="{
                        'bg-yellow-500': index === 0,
                        'bg-gray-400': index === 1,
                        'bg-amber-600': index === 2,
                      }">
                      <span class="text-xs sm:text-sm">{{ index + 1 }}</span>
                    </div>
                    <img
                      :src="student.profilePicture"
                      :alt="student.name"
                      @error="handleStudentImageError"
                      class="w-7 h-7 sm:w-9 sm:h-9 rounded-full mr-2 sm:mr-3 border border-gray-200 object-cover flex-shrink-0"
                    />
                    <div class="min-w-0">
                      <span class="text-gray-800 font-medium text-xs sm:text-sm truncate block">{{ student.name }}</span>
                    </div>
                  </div>
                  <div class="flex items-center">
                    <span class="text-xs text-white font-medium px-2.5 py-0.5 rounded-full"
                          :class="{
                            'bg-yellow-500': index === 0,
                            'bg-gray-400': index === 1,
                            'bg-amber-600': index === 2,
                        }">
                        {{ student.mark }}</span>
                  </div>
                </div>

                <!-- Empty state if no students -->
                <div v-if="!topStudents.length" class="text-center py-4 text-gray-500 text-xs sm:text-sm">
                  No student data available yet.
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Upcoming Sessions (for completed classes) -->
        <div v-if="displayAsCompleted" class="mb-6 sm:mb-8">
          <div class="border border-gray-200 rounded-lg bg-white shadow-sm p-4 sm:p-6 transition-all hover:shadow-md">
            <!-- Upcoming Sessions Icon -->
            <div class="flex items-center mb-4 sm:mb-5">
              <div class="flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8 border-2 border-orange rounded-full mr-2 sm:mr-3 flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-5 sm:w-5 text-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <h2 class="text-base sm:text-xl font-semibold text-gray-700">Upcoming Classes Being Studied</h2>
            </div>

            <!-- Upcoming Session Content -->
            <div class="mb-4" v-if="nextClass">
              <h3 class="text-lg sm:text-xl font-semibold text-gray-800 mb-2">{{ nextClass.title }}</h3>

              <!-- Stats -->
              <div class="flex flex-wrap gap-4 sm:gap-6 mb-3 sm:mb-4">
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                  </svg>
                  <span class="text-sm text-gray-600">{{ nextClass.rating }}</span>
                </div>
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                  <span class="text-sm text-gray-600">{{ nextClass.studentsEnrolled }} Students Enrolled</span>
                </div>
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  <span class="text-sm text-gray-600">{{ nextClass.modules }} Modules</span>
                </div>
              </div>

              <!-- Description -->
              <p class="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6">
                {{ nextClass.description }}
              </p>

              <!-- View Class Button -->
              <div class="flex justify-end">
                <button
                  @click="navigateToNextClass"
                  class="text-white bg-orange hover:bg-orange-dark font-medium text-sm sm:text-base py-2 px-6 sm:px-8 rounded-md shadow-sm transition-colors">
                  View Class
                </button>
              </div>
            </div>

            <!-- No upcoming classes message -->
            <div v-else class="text-center py-4 text-gray-500 text-sm sm:text-base">
              No classes currently being studied are available.
            </div>
          </div>
        </div>

      </div>
  </StudentLayoutWrapper>
</template>

<script>
import StudentLayoutWrapper from '@/components/@student/StudentLayoutWrapper.vue';
import StudentBreadcrumb from '@/components/@student/StudentBreadcrumb.vue';
import TaskHistoryDialog from '@/components/@student/TaskHistoryDialog.vue';
import SubmissionHistoryDialog from '@/components/@student/SubmissionHistoryDialog.vue';
import FeedbackDialog from '@/components/@student/FeedbackDialog.vue';
import DialogBox from '@/components/@student/DialogBox.vue';
import CertificateView from '@/components/@student/CertificateView.vue';
import { useClassStore, STORAGE_KEYS } from '@/data/availableClasses';
import { useRouter, useRoute } from 'vue-router';
import { computed, ref, watch, onMounted } from 'vue';
import { formatDate, formatTime, getStatusClass, formatTaskStatus, handleStudentImageError } from '@/utils/studentUtils';

export default {
  components: {
    StudentLayoutWrapper,
    StudentBreadcrumb,
    TaskHistoryDialog,
    SubmissionHistoryDialog,
    FeedbackDialog,
    DialogBox,
    CertificateView
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const classStore = useClassStore();
    const currentClass = classStore.currentClass;
    const showTaskHistory = ref(false);
    const showSubmissionHistory = ref(false);

    // Check if we're coming from a reload with a specific class source
    onMounted(() => {
      console.log('=== COMPONENT MOUNTED ===');

      // Get the current class ID from the route
      const currentClassId = parseInt(route.params.classId);
      console.log('Current class ID from route:', currentClassId);

      // Check localStorage values
      const storedClassId = localStorage.getItem(STORAGE_KEYS.CURRENT_CLASS_ID);
      const source = localStorage.getItem(STORAGE_KEYS.CLASS_SOURCE);
      console.log('Current class ID in localStorage:', storedClassId);
      console.log('Current class source in localStorage:', source);

      // If we have a reload parameter in the query, this is a forced reload
      if (route.query.reload) {
        console.log('Page loaded with reload parameter - this is a forced reload');

        // Make sure the current class is set correctly
        if (currentClassId) {
          // Force set the current class again to ensure it's loaded correctly
          const success = classStore.setCurrentClass(currentClassId);
          console.log('Setting current class successful:', success);
          console.log('Current class set to:', classStore.currentClass.value?.title);

          // Verify the class data is loaded correctly
          if (classStore.currentClass.value) {
            console.log('Class materials count:', classStore.currentClass.value.materials.length);
            console.log('Class actual status:', classStore.currentClass.value.status);

            // Calculate and log progress
            const progress = classStore.calculateProgress(currentClassId);
            console.log('Class progress:', progress + '%');

            // Check if class status needs to be updated based on progress
            if (progress === 100 && classStore.currentClass.value.status !== 'completed') {
              console.log('Class progress is 100%, updating status to completed');
              classStore.updateClassStatus(currentClassId);
            }
          } else {
            console.error('Failed to load class data!');
          }
        }

        // If we're coming from the "View Class" button in upcoming sessions,
        // the class source should be 'studied'
        if (source === 'studied') {
          console.log('Confirming class will be displayed as non-completed');
        }
      } else {
        console.log('Normal page load (not a forced reload)');

        // Still ensure the current class is set correctly
        if (currentClassId && (!classStore.currentClass.value || classStore.currentClass.value.id !== currentClassId)) {
          console.log('Current class not set or different from route, setting it now');
          const success = classStore.setCurrentClass(currentClassId);
          console.log('Setting current class successful:', success);

          // Verify the class data is loaded correctly
          if (classStore.currentClass.value) {
            console.log('Class materials count:', classStore.currentClass.value.materials.length);
            console.log('Class actual status:', classStore.currentClass.value.status);

            // Calculate and log progress
            const progress = classStore.calculateProgress(currentClassId);
            console.log('Class progress:', progress + '%');

            // Check if class status needs to be updated based on progress
            if (progress === 100 && classStore.currentClass.value.status !== 'completed') {
              console.log('Class progress is 100%, updating status to completed');
              classStore.updateClassStatus(currentClassId);
            }
          } else {
            console.error('Failed to load class data!');
          }
        }
      }

      // Check and update task status for all materials in the class
      if (currentClassId) {
        console.log('Checking and updating task status for all materials');
        classStore.checkAndUpdateTaskStatus(currentClassId);
      }
    });

    // We'll read the class source directly from localStorage in the computed property
    // This ensures we always have the latest value

    // Determine if the class should be displayed as completed or not
    // This allows us to override the actual class status for display purposes
    const displayAsCompleted = computed(() => {
      // CRITICAL: Always check localStorage directly to ensure we have the latest value
      // We need to force this to be reactive to localStorage changes
      const currentSource = localStorage.getItem(STORAGE_KEYS.CLASS_SOURCE);
      const reloadParam = route.query.reload;

      console.log('=== DETERMINING CLASS DISPLAY MODE ===');
      console.log('Current class source from localStorage:', currentSource);
      console.log('Current class actual status:', currentClass.value?.status);
      console.log('Reload parameter present:', reloadParam ? 'Yes' : 'No');

      // Check if we're coming from the upcoming sessions section
      // This is indicated by the 'studied' source in localStorage and a reload parameter
      if (currentSource === 'studied' && reloadParam) {
        console.log('Coming from upcoming sessions, forcing display as non-completed');
        return false;
      }

      // If the class source is explicitly set to 'completed', display as completed
      if (currentSource === 'completed') {
        console.log('Displaying class as completed (from localStorage)');
        return true;
      }
      // If the class source is explicitly set to 'studied', display as non-completed
      else if (currentSource === 'studied') {
        console.log('Displaying class as non-completed (from localStorage)');
        return false;
      }
      // If no source is specified, use the actual class status
      else {
        const isCompleted = currentClass.value?.status === 'completed';
        console.log('Displaying class based on actual status:', isCompleted ? 'completed' : 'non-completed');

        // Calculate and log progress
        if (currentClass.value?.id) {
          const progress = classStore.calculateProgress(currentClass.value.id);
          console.log('Current class progress:', progress + '%');
        }

        return isCompleted;
      }
    });

    watch(() => route.query.showTaskHistory, (newValue) => {
      if (newValue === 'true') {
        showTaskHistory.value = true;
      }
    }, { immediate: true });

    watch(() => route.query.showSubmissionHistory, (newValue) => {
      if (newValue === 'true') {
        showSubmissionHistory.value = true;
      }
    }, { immediate: true });

    // This line is removed to avoid duplicate declaration

    const breadcrumbItems = computed(() => {
      let firstBreadcrumbTitle = 'Classes Studied';
      let firstBreadcrumbPath = '/student/academy';

      // If class is displayed as completed, show "Completed Classes"
      if (displayAsCompleted.value) {
        firstBreadcrumbTitle = 'Completed Classes';
        firstBreadcrumbPath = {
          path: '/student/academy',
          query: { tab: 'completed' }
        };
      }

      return [
        {
          title: firstBreadcrumbTitle,
          path: firstBreadcrumbPath
        },
        {
          title: currentClass.value.title,
          active: true
        }
      ];
    });

    const navigateToLeaderboard = () => {
      router.push({
        name: 'StudentLeaderboard',
        params: { classId: currentClass.value?.id }
      });
    };

    const navigateToMaterials = () => {
      router.push({
        name: 'LearningMaterials',
        params: { classId: currentClass.value?.id }
      });
    };

    const navigateToMentoring = () => {
      router.push({
        name: 'MentoringPlatform'
      });
    };

    // handleStudentImageError is now imported from studentUtils.js

    const progressPercentage = computed(() => {
      // Make sure we have a valid class ID
      if (!currentClass.value?.id) {
        console.warn('Cannot calculate progress: No current class ID');
        return 0;
      }

      // Log the current class ID for debugging
      console.log('Calculating progress for class ID:', currentClass.value.id);

      // Use the updated calculation from classStore
      const progress = classStore.calculateProgress(currentClass.value.id);

      // Log the calculated progress
      console.log('Progress calculated:', progress);

      // Ensure progress is always a valid number between 0 and 100
      if (isNaN(progress) || progress < 0) {
        console.warn('Invalid progress value:', progress);
        return 0;
      } else if (progress > 100) {
        console.warn('Progress exceeds 100%:', progress);
        return 100;
      }

      // Round to nearest integer for cleaner display
      return Math.round(progress);
    });

    const topStudents = computed(() => {
      if (!currentClass.value?.students) return [];
      return [...currentClass.value.students]
        .sort((a, b) => b.mark - a.mark)
        .slice(0, 3);
    });

    // Get all task materials (materials with tasks that have been read and are pending)
    const allTaskMaterials = computed(() => {
      if (!currentClass.value?.materials) return [];

      // Get current date to check for past due tasks
      const currentDate = new Date();

      // Ensure we only include materials that explicitly have hasTask=true, are read, and have status 'pending' only
      return currentClass.value.materials.filter(m => {
        // Only include materials with hasTask=true and that are read
        if (m.hasTask !== true || !m.isRead) return false;

        // Check if the task is past due
        if (m.taskStatus === 'pending') {
          const dueDate = new Date(`${m.dueDate}T${m.dueTime}`);
          if (currentDate > dueDate) {
            // Auto update status to 'past_due' if past due date and still pending
            m.taskStatus = 'past_due';
            return true;
          }
          return true;
        }

        return m.taskStatus === 'pending';
      });
    });

    // Get the total count of task materials
    const totalTaskMaterialsCount = computed(() => {
      return allTaskMaterials.value.length;
    });

    const taskMaterials = computed(() => {
      // First, separate past_due tasks from other tasks
      const pastDueTasks = allTaskMaterials.value.filter(task => task.taskStatus === 'past_due');
      const pendingTasks = allTaskMaterials.value.filter(task => task.taskStatus === 'pending');

      // Sort past_due tasks by due date (most overdue first)
      const sortedPastDueTasks = pastDueTasks.sort((a, b) => {
        const dateA = new Date(`${a.dueDate}T${a.dueTime}`);
        const dateB = new Date(`${b.dueDate}T${b.dueTime}`);
        return dateA - dateB; // Ascending order - oldest due date first (most overdue)
      });

      // Sort pending tasks by due date (closest first)
      const sortedPendingTasks = pendingTasks.sort((a, b) => {
        const dateA = new Date(`${a.dueDate}T${a.dueTime}`);
        const dateB = new Date(`${b.dueDate}T${b.dueTime}`);
        return dateA - dateB;
      });

      // Combine the arrays with past_due tasks first, then limit to 3 items
      return [...sortedPastDueTasks, ...sortedPendingTasks].slice(0, 3);
    });

    // Get all submission history items (materials with tasks that have been read and have been submitted, under review, or reviewed)
    const allSubmissionHistory = computed(() => {
      // Get all tasks from the class
      const classId = currentClass.value?.id;
      if (!classId) return [];

      // Get all tasks from the class store
      const tasks = classStore.getAllTaskHistory(classId);

      // Get current date to check for past due tasks
      const currentDate = new Date();

      // For completed classes, we'll prepare the data but filter later in submissionHistory computed
      // This way we have all the data available for other computations if needed

      // Process each task to ensure data consistency
      return tasks
        // Only include tasks that are not pending AND have been read (isRead === true)
        .filter(task => task.taskStatus !== 'pending' && task.isRead === true)
        .map(task => {
          // Check if the task is past due but hasn't been marked as such
          if (task.taskStatus !== 'past_due') {
            const dueDate = new Date(`${task.dueDate}T${task.dueTime}`);
            if (currentDate > dueDate && task.taskStatus === 'pending') {
              task.taskStatus = 'past_due';
            }
          }

          // Ensure submission date and time are set correctly
          let submittedDate = task.submissionDate;
          let submittedTime = task.submissionTime;

          // If submission date/time are not set but task is submitted/under_review/reviewed,
          // set them to ensure data consistency
          if (!submittedDate && ['submitted', 'under_review', 'reviewed'].includes(task.taskStatus)) {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');

            submittedDate = `${year}-${month}-${day}`;
            submittedTime = `${hours}:${minutes}`;

            // Update the task in the store to maintain data consistency
            task.submissionDate = submittedDate;
            task.submissionTime = submittedTime;
          }

          return {
            id: task.id,
            title: task.title,
            taskStatus: task.taskStatus,
            submittedDate: submittedDate,
            submittedTime: submittedTime,
            dueDate: task.dueDate,
            dueTime: task.dueTime,
            // Ensure consistent scores across the application
            taskScore: task.taskScore || (task.taskStatus === 'reviewed' ? (task.id % 10 === 0 ? 100 : task.id % 10 === 1 ? 95 : task.id % 10 === 2 ? 90 : 95) : null)
          };
        });
    });

    // Get the total count of submission history items
    const totalSubmissionHistoryCount = computed(() => {
      // If there are no tasks in allSubmissionHistory, return 0
      if (!allSubmissionHistory.value || allSubmissionHistory.value.length === 0) {
        return 0;
      }

      // For completed classes, only count materials with 'reviewed' status
      if (displayAsCompleted.value) {
        return allSubmissionHistory.value.filter(task => task.taskStatus === 'reviewed').length;
      }
      // For non-completed classes, count all non-pending tasks
      return allSubmissionHistory.value.length;
    });

    // Check if the user has read any materials
    const hasReadAnyMaterials = computed(() => {
      if (!currentClass.value?.materials || currentClass.value.materials.length === 0) {
        return false;
      }

      // Check if at least one material has been read
      return currentClass.value.materials.some(material => material.isRead === true);
    });

    // Get submission history (limited to 3 items for display)
    const submissionHistory = computed(() => {
      // If there are no tasks in allSubmissionHistory, return an empty array
      if (!allSubmissionHistory.value || allSubmissionHistory.value.length === 0) {
        console.log('No submission history available');
        return [];
      }

      // For completed classes, only show materials with 'reviewed' status
      if (displayAsCompleted.value) {
        const reviewedTasks = allSubmissionHistory.value.filter(task => task.taskStatus === 'reviewed');

        // Sort by score (highest first)
        return reviewedTasks.sort((a, b) => {
          // If scores are the same, sort by submission date (most recent first)
          if (b.taskScore === a.taskScore) {
            const dateA = new Date(`${a.submittedDate}T${a.submittedTime || '00:00'}`);
            const dateB = new Date(`${b.submittedDate}T${b.submittedTime || '00:00'}`);
            return dateB - dateA;
          }
          return b.taskScore - a.taskScore;
        }).slice(0, 3);
      }

      // For non-completed classes, show all non-pending tasks
      // First, separate tasks by status for proper prioritization
      const pastDueTasks = allSubmissionHistory.value.filter(task => task.taskStatus === 'past_due');
      const reviewedTasks = allSubmissionHistory.value.filter(task => task.taskStatus === 'reviewed');
      const underReviewTasks = allSubmissionHistory.value.filter(task => task.taskStatus === 'under_review');
      const submittedTasks = allSubmissionHistory.value.filter(task => task.taskStatus === 'submitted');
      const turnedInTasks = allSubmissionHistory.value.filter(task => task.taskStatus === 'turned_in');

      // Sort past_due tasks by due date (most overdue first)
      const sortedPastDueTasks = pastDueTasks.sort((a, b) => {
        const dateA = new Date(`${a.dueDate}T${a.dueTime}`);
        const dateB = new Date(`${b.dueDate}T${b.dueTime}`);
        return dateA - dateB; // Ascending order - oldest due date first (most overdue)
      });

      // Sort other task types by submission date (most recent first)
      const sortBySubmissionDate = (tasks) => {
        return tasks.sort((a, b) => {
          const dateA = new Date(`${a.submittedDate}T${a.submittedTime || '00:00'}`);
          const dateB = new Date(`${b.submittedDate}T${b.submittedTime || '00:00'}`);
          return dateB - dateA; // Reverse order for most recent first
        });
      };

      const sortedReviewedTasks = sortBySubmissionDate(reviewedTasks);
      const sortedUnderReviewTasks = sortBySubmissionDate(underReviewTasks);
      const sortedSubmittedTasks = sortBySubmissionDate(submittedTasks);
      const sortedTurnedInTasks = sortBySubmissionDate(turnedInTasks);

      // Combine the arrays with proper prioritization:
      // 1. Past due tasks first (most critical)
      // 2. Then under review tasks (waiting for mentor action)
      // 3. Then submitted tasks (waiting for review)
      // 4. Then reviewed tasks (completed with feedback)
      // 5. Then turned in tasks (completed without feedback)
      return [
        ...sortedPastDueTasks,
        ...sortedUnderReviewTasks,
        ...sortedSubmittedTasks,
        ...sortedReviewedTasks,
        ...sortedTurnedInTasks
      ].slice(0, 3);
    });

    const viewMaterial = (materialId) => {
      router.push({
        name: 'DetailLearningMaterials',
        params: {
          classId: currentClass.value?.id,
          materialId
        }
      });
    };

    // Handle task submission event from TaskHistoryDialog
    const handleTaskSubmitted = () => {
      // Refresh the task materials list to reflect the updated status
      // No need to do anything special here as the computed property will automatically update
    };

    // Navigate to task detail page
    const navigateToTask = (materialId) => {
      classStore.setCurrentMaterial(materialId);
      router.push({
        name: 'DetailTaskHistory',
        params: {
          classId: currentClass.value?.id,
          materialId
        },
        query: {
          source: 'main_page_task'
        }
      });
    };

    // Navigate to submission detail page
    const navigateToSubmission = (materialId) => {
      classStore.setCurrentMaterial(materialId);
      router.push({
        name: 'DetailTaskHistory',
        params: {
          classId: currentClass.value?.id,
          materialId
        },
        query: {
          source: 'main_page_submission'
        }
      });
    };

    // formatTaskStatus, formatDate, formatTime, and getStatusClass are now imported from studentUtils.js

    // State for feedback dialogs
    const showFeedbackDialog = ref(false);
    const showFeedbackSentDialog = ref(false);

    // State for certificate dialogs
    const showCertificateView = ref(false);
    const showCertificateDownloadedDialog = ref(false);

    // Function to handle feedback button click
    const provideFeedback = () => {
      // Ensure we have a valid class before showing the feedback dialog
      if (!currentClass.value || !currentClass.value.id) {
        console.error('Cannot provide feedback: No current class available');
        return;
      }

      console.log(`Opening feedback dialog for class ${currentClass.value.id}: ${currentClass.value.title}`);

      // Show the feedback dialog
      showFeedbackDialog.value = true;
    };

    // Function to close feedback dialog
    const closeFeedbackDialog = () => {
      showFeedbackDialog.value = false;
    };

    // Function to handle feedback submission
    const handleFeedbackSent = (feedback) => {
      console.log('Feedback received:', feedback);

      // Ensure we have a valid class ID
      if (!currentClass.value || !currentClass.value.id) {
        console.error('Cannot submit feedback: No current class available');
        return;
      }

      const classId = currentClass.value.id;
      console.log(`Submitting feedback for class ${classId}`);

      // Close the feedback dialog
      showFeedbackDialog.value = false;

      // Show the success dialog
      showFeedbackSentDialog.value = true;

      // Save the feedback to the store with the specific class ID
      const success = classStore.submitFeedback(classId, feedback);

      if (success) {
        console.log(`Feedback successfully submitted for class ${classId}`);
      } else {
        console.error(`Failed to submit feedback for class ${classId}`);
      }
    };

    // Function to close feedback sent dialog
    const closeFeedbackSentDialog = () => {
      showFeedbackSentDialog.value = false;
    };

    // Function to view certificate
    const viewCertificate = () => {
      // Ensure we have a valid class before showing the certificate
      if (!currentClass.value || !currentClass.value.id) {
        console.error('Cannot view certificate: No current class available');
        return;
      }

      console.log(`Viewing certificate for class ${currentClass.value.id}: ${currentClass.value.title}`);

      // Show the certificate view
      showCertificateView.value = true;
    };

    // Function to close certificate view
    const closeCertificateView = () => {
      showCertificateView.value = false;
    };

    // Function to handle certificate download
    const handleCertificateDownload = (classId) => {
      console.log(`Certificate download requested for class ${classId}`);

      // Validate the class ID
      if (!classId) {
        console.error('Cannot download certificate: Invalid class ID');
        return;
      }

      // Ensure the class ID matches the current class
      if (currentClass.value && currentClass.value.id !== classId) {
        console.warn(`Certificate class ID (${classId}) doesn't match current class (${currentClass.value.id})`);
        // Use the current class ID instead for consistency
        classId = currentClass.value.id;
        console.log(`Using current class ID (${classId}) for certificate download`);
      }

      // Close the certificate view
      showCertificateView.value = false;

      // Show the certificate downloaded dialog
      showCertificateDownloadedDialog.value = true;

      // Mark the certificate as downloaded in the store
      const success = classStore.downloadCertificate(classId);

      if (success) {
        console.log(`Certificate successfully marked as downloaded for class ${classId}`);
      } else {
        console.error(`Failed to mark certificate as downloaded for class ${classId}`);
      }
    };

    // Function to close certificate downloaded dialog
    const closeCertificateDownloadedDialog = () => {
      showCertificateDownloadedDialog.value = false;
    };

    // Computed property to check if feedback has been provided
    const hasFeedback = computed(() => {
      // Ensure we're using the latest currentClass value
      if (!currentClass.value || !currentClass.value.id) {
        console.log('No current class available for feedback check');
        return false;
      }

      const classId = currentClass.value.id;
      const hasFeedbackResult = classStore.hasFeedback(classId);
      console.log(`Feedback check for class ${classId}: ${hasFeedbackResult ? 'Has feedback' : 'No feedback'}`);
      return hasFeedbackResult;
    });

    // Computed property to check if certificate has been downloaded
    const certificateDownloaded = computed(() => {
      // Ensure we're using the latest currentClass value
      if (!currentClass.value || !currentClass.value.id) {
        console.log('No current class available for certificate check');
        return false;
      }

      const classId = currentClass.value.id;
      const certificateDownloadedResult = classStore.isCertificateDownloaded(classId);
      console.log(`Certificate check for class ${classId}: ${certificateDownloadedResult ? 'Downloaded' : 'Not downloaded'}`);
      return certificateDownloadedResult;
    });

    // Get the next class to show in upcoming sessions
    const nextClass = computed(() => {
      console.log('=== CALCULATING NEXT CLASS FOR UPCOMING SESSIONS ===');

      // For upcoming sessions, we only want to show classes with 'studied' status
      // This ensures only classes that are being studied appear in upcoming sessions
      const allClasses = [...classStore.classes.value];
      console.log('Total classes in store:', allClasses.length);

      // Get the current class ID
      const currentClassId = currentClass.value?.id;
      console.log('Current class ID:', currentClassId);

      // Filter out the current class and only include classes with 'studied' status
      // Also ensure we have valid data for all required fields
      const otherClasses = allClasses.filter(c => {
        // Skip the current class
        if (c.id === currentClassId) {
          console.log(`Skipping current class: ${c.id} - ${c.title}`);
          return false;
        }

        // Only include classes with 'studied' status
        if (c.status !== 'studied') {
          console.log(`Skipping class with status ${c.status}: ${c.id} - ${c.title}`);
          return false;
        }

        // Ensure we have all required data
        if (!c.title || !c.postedDate || !c.postedTime) {
          console.log(`Skipping class with missing data: ${c.id}`);
          return false;
        }

        // Calculate progress for this class
        const progress = classStore.calculateProgress(c.id);
        console.log(`Class ${c.id} - ${c.title} has progress: ${progress}%`);

        // Include this class
        console.log(`Including class in upcoming sessions: ${c.id} - ${c.title}`);
        return true;
      });

      console.log('Filtered classes for upcoming sessions:', otherClasses.length);

      // Sort by posted date (most recent first)
      const sortedClasses = otherClasses.sort((a, b) => {
        // Ensure we have valid date strings
        const dateAStr = `${a.postedDate || '2025-01-01'}T${a.postedTime || '00:00'}`;
        const dateBStr = `${b.postedDate || '2025-01-01'}T${b.postedTime || '00:00'}`;

        const dateA = new Date(dateAStr);
        const dateB = new Date(dateBStr);

        // If either date is invalid, use a default comparison
        if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) {
          return a.id - b.id;
        }

        return dateB - dateA;
      });

      // Get the next class (first in the sorted list)
      const next = sortedClasses.length > 0 ? sortedClasses[0] : null;

      if (next) {
        console.log(`Next class selected: ${next.id} - ${next.title}`);
      } else {
        console.log('No next class available for upcoming sessions');
      }

      // For the first class in the list, we'll show it in the upcoming sessions
      // When the user clicks "View Class", we'll force it to display as non-completed
      // regardless of its actual status
      return next;
    });

    // Function to navigate to the next class
    const navigateToNextClass = () => {
      if (nextClass.value) {
        // Get the next class ID and data
        const nextClassId = nextClass.value.id;

        console.log('=== NAVIGATING TO NEXT CLASS FROM UPCOMING SESSIONS ===');
        console.log('Next class ID:', nextClassId);
        console.log('Next class title:', nextClass.value.title);
        console.log('Next class status:', nextClass.value.status);
        console.log('Next class materials count:', nextClass.value.materials?.length || 0);

        // CRITICAL: We need to force the class to be displayed as 'studied' regardless of its actual status
        // First, clear any existing class source from localStorage
        localStorage.removeItem('flowcamp-classSource');

        // Then explicitly set the class source to 'studied'
        localStorage.setItem('flowcamp-classSource', 'studied');

        console.log('Setting class source to "studied" in localStorage');

        // IMPORTANT: Set the current class in the store BEFORE navigation
        // This ensures the correct class is loaded when the page renders
        const success = classStore.setCurrentClass(nextClassId);
        console.log('Setting current class successful:', success);

        // Get the next class object to check its status
        const nextClassObj = classStore.getClassById(nextClassId);

        // Log information about the navigation
        if (nextClassObj && nextClassObj.status === 'completed') {
          console.log('Navigating to a completed class that will be displayed as non-completed (studied)');
        } else {
          console.log('Navigating to a non-completed class');
        }

        // Force a reload approach to ensure the localStorage change takes effect
        // This is more reliable than just setting the current class

        // First, set the current class ID in localStorage directly
        localStorage.setItem('flowcamp-currentClassId', nextClassId.toString());

        // Log the current state before navigation
        console.log('Current class in store before navigation:', classStore.currentClass.value?.title);
        console.log('Current class ID in localStorage:', localStorage.getItem('flowcamp-currentClassId'));
        console.log('Class source in localStorage:', localStorage.getItem('flowcamp-classSource'));

        // Calculate and log progress
        const progress = classStore.calculateProgress(nextClassId);
        console.log('Class progress:', progress + '%');

        // Navigate to the class detail page without forcing a reload
        router.push({
          name: 'DetailClass',
          params: { classId: nextClassId },
          replace: true // Replace current route instead of adding to history
        });
      } else {
        console.log('No next class available, navigating to academy page');
        // If no next class, navigate to the academy page
        router.push({
          name: 'Academy'
        });
      }
    };

    // Create a computed property that always returns the latest currentClass value
    const currentClassComputed = computed(() => {
      // Always get the latest value from the store
      return classStore.currentClass.value;
    });

    return {
      currentClass: currentClassComputed,
      breadcrumbItems,
      progressPercentage,
      topStudents,
      navigateToLeaderboard,
      navigateToMaterials,
      navigateToMentoring,
      showTaskHistory,
      showSubmissionHistory,
      taskMaterials,
      submissionHistory,
      totalTaskMaterialsCount,
      totalSubmissionHistoryCount,
      viewMaterial,
      handleTaskSubmitted,
      navigateToTask,
      navigateToSubmission,
      formatTaskStatus,
      formatDate,
      formatTime,
      getStatusClass,
      provideFeedback,
      navigateToNextClass,
      nextClass,
      displayAsCompleted,
      hasReadAnyMaterials,
      // Feedback dialog related
      showFeedbackDialog,
      closeFeedbackDialog,
      handleFeedbackSent,
      showFeedbackSentDialog,
      closeFeedbackSentDialog,
      // Certificate related
      viewCertificate,
      showCertificateView,
      closeCertificateView,
      handleCertificateDownload,
      showCertificateDownloadedDialog,
      closeCertificateDownloadedDialog,
      hasFeedback,
      certificateDownloaded,
      // Image error handling
      handleStudentImageError
    };
  }
}
</script>