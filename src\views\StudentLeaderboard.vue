<script setup>
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import StudentLeaderboardEntry from '@/components/@student/StudentLeaderboardEntry.vue';
import StudentLayoutWrapper from '@/components/@student/StudentLayoutWrapper.vue';
import StudentBreadcrumb from '@/components/@student/StudentBreadcrumb.vue';
import { useClassStore } from '@/data/availableClasses';

const route = useRoute();
const classStore = useClassStore();
const currentClass = classStore.currentClass;

// Get students from current class
const students = computed(() => currentClass.value?.students || []);
const isLoading = ref(false);

// Search functionality
const searchQuery = ref('');
const filteredStudents = computed(() => {
  let result = [];

  if (!searchQuery.value) {
    result = [...students.value];
  } else {
    const query = searchQuery.value.toLowerCase();
    result = students.value.filter(student =>
      student.name.toLowerCase().includes(query)
    );
  }

  return result.sort((a, b) => b.mark - a.mark);
});

// Version 1: Load More Button
const itemsPerPage = 10;
const currentPage = ref(1);

const displayedStudents = computed(() => {
  return filteredStudents.value.slice(0, currentPage.value * itemsPerPage);
});

const hasMoreToLoad = computed(() => {
  return displayedStudents.value.length < filteredStudents.value.length;
});

function loadMore() {
  currentPage.value++;
}

const breadcrumbItems = [
  {
    title: 'Classes Studied',
    path: '/student/academy'
  },
  {
    title: currentClass.value?.title || 'Class',
    path: `/student/class/${route.params.classId}`
  },
  {
    title: 'Student Leaderboard',
    active: true
  }
];
</script>

<template>
  <StudentLayoutWrapper>
    <StudentBreadcrumb :items="breadcrumbItems" />
      <div class="max-w-4xl mx-auto">
        <div class="mb-4 sm:mb-6">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="search"
              v-model="searchQuery"
              class="w-full p-2 sm:p-3 pl-8 sm:pl-10 text-xs sm:text-sm border border-gray-300 rounded-full focus:ring-2 focus:ring-orange focus:border-orange focus:outline-orange"
              placeholder="Search"
            />
          </div>
        </div>

        <div class="bg-gray-100 rounded-lg shadow-lg overflow-hidden">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-300">
              <thead class="bg-orange text-white">
                <tr>
                  <th scope="col" class="w-16 sm:w-20 px-2 sm:px-4 py-2 sm:py-3 text-center text-xs sm:text-sm font-semibold">No</th>
                  <th scope="col" class="px-2 sm:px-4 py-2 sm:py-3 text-left text-xs sm:text-sm font-semibold">Name</th>
                  <th scope="col" class="w-16 sm:w-24 px-2 sm:px-4 py-2 sm:py-3 text-center text-xs sm:text-sm font-semibold">Mark</th>
                </tr>
              </thead>
              <tbody class="bg-gray-100 divide-y divide-gray-200">
                <template v-if="displayedStudents.length > 0">
                  <StudentLeaderboardEntry
                    v-for="(student, index) in displayedStudents"
                    :key="student.id"
                    :student="student"
                    :rank="index + 1"
                    :isSearchActive="!!searchQuery"
                  />
                </template>
                <tr v-else>
                  <td colspan="3" class="px-2 sm:px-4 py-6 sm:py-8 text-center text-gray-500 text-xs sm:text-sm">
                    <div v-if="isLoading" class="flex justify-center items-center">
                      <div class="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-orange"></div>
                    </div>
                    <div v-else>
                      {{ searchQuery ? 'No students found matching your search.' : 'No students in the leaderboard yet.' }}
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div v-if="hasMoreToLoad" class="flex justify-center py-3 sm:py-4 bg-gray-100">
            <button
              @click="loadMore"
              class="px-3 sm:px-4 py-1.5 sm:py-2 bg-orange text-white rounded-md hover:bg-orange-dark transition-colors text-xs sm:text-sm"
            >
              Load More
            </button>
          </div>
        </div>
      </div>
  </StudentLayoutWrapper>
</template>