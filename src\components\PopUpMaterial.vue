<template>
  <div class="fixed inset-0 bg-black/30 flex items-center justify-center z-50">
    <div class="bg-white p-8 w-full max-w-xl rounded-lg shadow-lg relative">
      <!-- Header -->
      <div class="flex justify-between items-start mb-4">
        <div class="font-bold text-lg flex items-center gap-2">
          <i class="icon-doc" /> Material
        </div>
      </div>

      <div class="font-semibold mb-4">
        Class Mastery In Golang: From Zero to Hero
      </div>

      <!-- Dropdown -->
      <select v-model="selectedModule" class="w-full p-3 mb-4 border border-gray-300 rounded-md">
        <option v-for="modul in modules" :key="modul" :value="modul">{{ modul }}</option>
      </select>

      <!-- Textarea -->
      <textarea
        v-model="description"
        class="w-full p-3 mb-4 border border-gray-300 rounded-md h-28 resize-none"
        placeholder="Description"
      />

      <!-- File drop -->
      <div class="w-full h-20 border border-dashed border-gray-300 rounded-md text-center text-gray-500 flex items-center justify-center mb-6">
        You can drag and drop files here to add them +
      </div>

      <!-- Action button -->
      <div class="flex justify-end">
        <button
          class="bg-orange text-white px-5 py-2 rounded hover:bg-orange-dark transition-colors duration-200"
          @click="postMaterial"
        >
          Post
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const emit = defineEmits(['close', 'posted'])

const postMaterial = () => {
  emit('posted')  // ⬅ trigger notifikasi
  emit('close')
}


const modules = [
  'Modul 1: Pengenalan Golang',
  'Modul 2: Struktur Data',
  'Modul 3: Goroutine dan Channel',
  'Modul 4: Error Handling',
  'Modul 5: Project Akhir'
]

const selectedModule = ref(modules[0])
const description = ref('')

</script>
