/**
 * Available Classes Data
 *
 * This file contains the available classes data that will be displayed in the Classes Camp page.
 * It also manages the student's joined classes (studied and completed).
 *
 * Data Structure for Admin Reference:
 * - id: Unique identifier for the class (should be a number)
 * - title: Descriptive title of the class
 * - rating: Class rating (number between 1-5, can include decimal)
 * - studentsEnrolled: Number of students enrolled in the class
 * - modules: Number of modules in the class
 * - category: Category of the class (should be one of: 'frontend', 'backend', 'mobile')
 * - description: Detailed description of what the class covers
 * - postedDate: Date when the class was posted (format: YYYY-MM-DD)
 * - postedTime: Time when the class was posted (format: HH:MM AM/PM)
 * - imageUrl: Path to the class image (should be a string pointing to an image in the public directory)
 */

import { ref, readonly, watch } from 'vue';
import { dummyStudentClasses } from './dummyStudentClasses';
import { dummyAvailableClassesData } from './dummyAvailableClasses';

// Import data from dummy sources

// Constants for localStorage keys to avoid duplication and typos
export const STORAGE_KEYS = {
  CLASSES: 'flowcamp-classes',
  CURRENT_CLASS_ID: 'flowcamp-currentClassId',
  CURRENT_MATERIAL_ID: 'flowcamp-currentMaterialId',
  CLASS_SOURCE: 'flowcamp-classSource',
  ACTIVE_TAB: 'flowcamp-activeTab'
};

// Available classes data from dummy data
export const availableClassesData = [...dummyAvailableClassesData];

// Load initial state from localStorage or use dummy data
const initializeFromStorage = () => {
  const storedClasses = localStorage.getItem(STORAGE_KEYS.CLASSES);

  // If there are stored classes, use them
  if (storedClasses) {
    return JSON.parse(storedClasses);
  }

  // Otherwise, use dummy data
  return dummyStudentClasses;
};

// Create reactive references
const joinedClasses = ref(initializeFromStorage());
const currentClass = ref(null);
const currentMaterial = ref(null);

// Helper function to safely parse integer from localStorage
const safeParseInt = (value) => {
  return value ? parseInt(value) : null;
};

// Helper function to get current date in YYYY-MM-DD format
const getCurrentDate = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Helper function to get current time in HH:MM format
const getCurrentTime = () => {
  const now = new Date();
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  return `${hours}:${minutes}`;
};

// Initialize current class and material from localStorage
const initializeCurrentItems = () => {
  const classId = safeParseInt(localStorage.getItem(STORAGE_KEYS.CURRENT_CLASS_ID));

  if (classId !== null) {
    currentClass.value = joinedClasses.value.find(c => c.id === classId) || null;

    const materialId = safeParseInt(localStorage.getItem(STORAGE_KEYS.CURRENT_MATERIAL_ID));
    if (materialId !== null && currentClass.value) {
      currentMaterial.value = currentClass.value.materials.find(m => m.id === materialId) || null;
    }
  }
};

// Initialize on startup
initializeCurrentItems();

// Watch for changes and save to localStorage
watch(joinedClasses, (newClasses) => {
  localStorage.setItem(STORAGE_KEYS.CLASSES, JSON.stringify(newClasses));
}, { deep: true });

// Watch currentClass and save ID to localStorage
watch(currentClass, (newCurrentClass) => {
  if (newCurrentClass && newCurrentClass.id) {
    localStorage.setItem(STORAGE_KEYS.CURRENT_CLASS_ID, newCurrentClass.id.toString());
  } else {
    localStorage.removeItem(STORAGE_KEYS.CURRENT_CLASS_ID);
  }
});

// Watch currentMaterial and save ID to localStorage
watch(currentMaterial, (newCurrentMaterial) => {
  if (newCurrentMaterial && newCurrentMaterial.id) {
    localStorage.setItem(STORAGE_KEYS.CURRENT_MATERIAL_ID, newCurrentMaterial.id.toString());
  } else {
    localStorage.removeItem(STORAGE_KEYS.CURRENT_MATERIAL_ID);
  }
});

// Create the class store
export function useClassStore() {
  // Helper function to ensure IDs are numbers
  function ensureNumericId(id) {
    return typeof id === 'string' ? parseInt(id) : id;
  }

  // Helper functions
  function getClassById(classId) {
    const id = ensureNumericId(classId);
    return joinedClasses.value.find(c => c.id === id) || null;
  }

  function getClassesByStatus(status) {
    return joinedClasses.value.filter(c => c.status === status);
  }

  // Set current class
  function setCurrentClass(classId) {
    const id = ensureNumericId(classId);
    const classItem = joinedClasses.value.find(c => c.id === id);

    // Set the current class
    currentClass.value = classItem || null;

    // localStorage is handled by the watcher
    return classItem !== null;
  }

  // Set current material
  function setCurrentMaterial(materialId) {
    if (!currentClass.value) return false;

    const id = ensureNumericId(materialId);
    currentMaterial.value = currentClass.value.materials.find(m => m.id === id) || null;

    // localStorage is handled by the watcher
    return currentMaterial.value !== null;
  }

  // Calculate progress for a class
  function calculateProgress(classId) {
    const classItem = getClassById(classId);
    if (!classItem || !classItem.materials || classItem.materials.length === 0) return 0;

    const totalMaterials = classItem.materials.length;

    // Count materials as completed if:
    // 1. They are marked as complete (isComplete: true), OR
    // 2. They have been read (isRead: true) AND:
    //    a. They don't have a task (hasTask: false), OR
    //    b. They have a task that's been completed (taskStatus is not 'pending' or 'past_due')
    const completedMaterials = classItem.materials.filter(m => {
      // If material is already marked as complete, count it
      if (m.isComplete) return true;

      // If material has been read
      if (m.isRead) {
        // If it doesn't have a task, count it as complete
        if (!m.hasTask) return true;

        // If it has a task, check if the task is completed
        if (m.hasTask && m.taskStatus &&
            m.taskStatus !== 'pending' &&
            m.taskStatus !== 'past_due') {
          return true;
        }
      }

      return false;
    }).length;

    const progress = Math.round((completedMaterials / totalMaterials) * 100);

    // Update the progress property on the class
    classItem.progress = progress;

    // Update class status if needed
    updateClassStatusBasedOnProgress(classItem, progress);

    return progress;
  }

  // Helper function to set completion date
  function setCompletionDate(classItem) {
    // Set completion date if not already set
    if (!classItem.completionDate) {
      classItem.completionDate = getCurrentDate();
    }
  }

  // Helper function to update class status based on progress
  function updateClassStatusBasedOnProgress(classItem, progress) {
    if (progress === 100 && classItem.status !== 'completed') {
      classItem.status = 'completed';

      // Set completion date
      setCompletionDate(classItem);
    } else if (progress < 100 && classItem.status === 'completed') {
      classItem.status = 'studied';
    }
  }

  // Helper function to find material in a class
  function findMaterial(classItem, materialId) {
    const id = ensureNumericId(materialId);
    return classItem.materials.find(m => m.id === id);
  }

  // Mark material as read
  function markMaterialAsRead(classId, materialId) {
    const classItem = getClassById(classId);
    if (!classItem) return false;

    const material = findMaterial(classItem, materialId);
    if (!material) return false;

    material.isRead = true;

    // Calculate progress after marking as read
    calculateProgress(classId);

    return true;
  }

  // Mark material as complete
  function markMaterialAsComplete(classId, materialId) {
    const classItem = getClassById(classId);
    if (!classItem) return false;

    const material = findMaterial(classItem, materialId);
    if (!material) return false;

    // Mark as both read and complete
    material.isRead = true;
    material.isComplete = true;

    // If the material has a task, mark it as completed too
    if (material.hasTask && (material.taskStatus === 'pending' || material.taskStatus === 'past_due')) {
      material.taskStatus = 'turned_in';

      // Set submission date and time
      material.submissionDate = getCurrentDate();
      material.submissionTime = getCurrentTime();
    }

    // Calculate progress after marking as complete
    const progress = calculateProgress(classId);

    // If progress is 100%, update class status and set completion date
    if (progress === 100 && classItem.status !== 'completed') {
      classItem.status = 'completed';

      // Set completion date using helper function
      setCompletionDate(classItem);
    }

    return true;
  }

  // Constants for task statuses
  const COMPLETED_TASK_STATUSES = ['turned_in', 'reviewed', 'submitted', 'under_review'];

  // Update task status
  function updateTaskStatus(classId, materialId, newStatus) {
    const classItem = getClassById(classId);
    if (!classItem) return false;

    const material = findMaterial(classItem, materialId);
    if (!material) return false;

    // Get the current status before updating
    const oldStatus = material.taskStatus;

    // Update the status
    material.taskStatus = newStatus;

    // Handle specific status transitions
    if (oldStatus === 'submitted' && newStatus === 'under_review') {
      // Mentor has started reviewing the task
      // Set review start date and time if not already set
      if (!material.reviewStartDate) {
        material.reviewStartDate = getCurrentDate();
        material.reviewStartTime = getCurrentTime();
      }
    }
    else if (newStatus === 'reviewed') {
      // Mentor has completed the review
      // Set review completion date and time
      material.reviewedDate = getCurrentDate();
      material.reviewedTime = getCurrentTime();
    }
    else if (newStatus === 'submitted') {
      // Student has submitted the task
      // Set submission date and time if not already set
      if (!material.submissionDate) {
        material.submissionDate = getCurrentDate();
        material.submissionTime = getCurrentTime();
      }
    }

    // If task is in a completed state, check if we need to update class status
    if (COMPLETED_TASK_STATUSES.includes(newStatus)) {
      const allTasksCompleted = classItem.materials
        .filter(m => m.hasTask)
        .every(m => COMPLETED_TASK_STATUSES.includes(m.taskStatus));

      if (allTasksCompleted) {
        updateClassStatus(classId);
      }
    }

    // Calculate progress and update class status if needed
    calculateProgress(classId);

    return true;
  }

  // Update class status
  function updateClassStatus(classId) {
    const classItem = getClassById(classId);
    if (!classItem) return false;

    // Check if all materials are complete
    const allMaterialsComplete = classItem.materials.every(m => m.isComplete);

    // Update class status based on completion
    classItem.status = allMaterialsComplete ? 'completed' : 'studied';

    return true;
  }

  // Add a new class (when student joins a class)
  function addClass(classData) {
    // Check if class with same ID or title already exists
    const existingClassById = joinedClasses.value.find(c => c.id === classData.id);
    const existingClassByTitle = joinedClasses.value.find(c => c.title === classData.title);

    if (existingClassById || existingClassByTitle) {
      // Return the existing class ID instead of creating a duplicate
      return existingClassById ? existingClassById.id : existingClassByTitle.id;
    }

    // Generate a new unique ID
    const maxId = joinedClasses.value.length > 0
      ? Math.max(...joinedClasses.value.map(c => c.id))
      : 0;
    const newId = maxId + 1;

    // Get current date and time
    const currentDate = getCurrentDate();
    const currentTime = getCurrentTime();

    // Create the new class with real-time postedDate and postedTime
    const newClass = {
      id: newId,
      title: classData.title || "New Class",
      rating: classData.rating || 0,
      studentsEnrolled: classData.studentsEnrolled || 0,
      modules: classData.modules || 0,
      description: classData.description || "",
      category: classData.category || "programming",
      status: classData.status || 'studied',
      postedDate: classData.postedDate || currentDate,
      postedTime: classData.postedTime || currentTime,
      joinedDate: classData.joinedDate || currentDate,
      joinedTime: classData.joinedTime || currentTime,
      imageUrl: classData.imageUrl || null,
      learningTools: classData.learningTools || {
        deviceSpecs: {
          processor: "Intel Dual Core (Core i3 and above recommended)"
        },
        tools: ["Google Collaboratory"]
      },
      progress: classData.progress || 0,
      materials: [],
      students: []
    };

    // Add materials if provided, otherwise create default materials
    if (classData.materials && classData.materials.length > 0) {
      // Use materials directly if provided
      newClass.materials = classData.materials;
    } else if (classData.moduleData && classData.moduleData.length > 0) {
      // Convert moduleData to proper material objects (for backward compatibility)
      for (let i = 0; i < classData.moduleData.length; i++) {
        const moduleData = classData.moduleData[i];
        const moduleTitle = moduleData.title || `Module ${i + 1}`;
        const moduleDesc = moduleData.description || `Description for ${moduleTitle}`;

        // Generate a unique ID for the material
        const materialId = newId * 100 + (i + 1);

        // Set due date (7 days from now)
        const dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + 7);
        const dueYear = dueDate.getFullYear();
        const dueMonth = String(dueDate.getMonth() + 1).padStart(2, '0');
        const dueDay = String(dueDate.getDate()).padStart(2, '0');

        // Create material with default values
        const material = {
          id: materialId,
          title: moduleTitle,
          isComplete: false,
          isRead: false,
          hasTask: i % 2 === 0,
          taskStatus: 'pending',
          taskDescription: `Complete the exercises for ${moduleTitle}.`,
          postedDate: currentDate,
          postedTime: currentTime,
          dueDate: `${dueYear}-${dueMonth}-${dueDay}`,
          dueTime: "23:59",
          description: moduleDesc,
          objectives: [
            `Understand ${moduleTitle} concepts`,
            'Apply knowledge to practical examples',
            'Complete exercises and assignments'
          ],
          topics: [
            `Introduction to ${moduleTitle}`,
            'Key principles and techniques',
            'Practical applications'
          ],
          files: {
            presentation: null,
            video: null
          }
        };

        newClass.materials.push(material);
      }
    } else {
      // Create default materials based on the number of modules
      for (let i = 0; i < (classData.modules || 3); i++) {
        const moduleTitle = `Module ${i + 1}`;
        const moduleDesc = `Description for ${moduleTitle}`;

        // Generate a unique ID for the material
        const materialId = newId * 100 + (i + 1);

        // Set due date (7 days from now)
        const dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + 7);
        const dueYear = dueDate.getFullYear();
        const dueMonth = String(dueDate.getMonth() + 1).padStart(2, '0');
        const dueDay = String(dueDate.getDate()).padStart(2, '0');

        // Create material with default values
        const material = {
          id: materialId,
          title: moduleTitle,
          isComplete: false,
          isRead: false,
          hasTask: i % 2 === 0,
          taskStatus: 'pending',
          taskDescription: `Complete the exercises for ${moduleTitle}.`,
          postedDate: currentDate,
          postedTime: currentTime,
          dueDate: `${dueYear}-${dueMonth}-${dueDay}`,
          dueTime: "23:59",
          description: moduleDesc,
          objectives: [
            `Understand ${moduleTitle} concepts`,
            'Apply knowledge to practical examples',
            'Complete exercises and assignments'
          ],
          topics: [
            `Introduction to ${moduleTitle}`,
            'Key principles and techniques',
            'Practical applications'
          ],
          files: {
            presentation: null,
            video: null
          }
        };

        newClass.materials.push(material);
      }
    }

    // Add mock students if provided
    if (classData.mockStudents && classData.mockStudents.length > 0) {
      newClass.students = classData.mockStudents;
    }

    // Add the new class to the classes array
    joinedClasses.value.push(newClass);

    return newId;
  }

  // Get material files
  async function getMaterialFiles(classId, materialId) {
    const classItem = getClassById(classId);
    if (!classItem) return null;

    // Convert materialId to number if it's a string
    const id = typeof materialId === 'string' ? parseInt(materialId) : materialId;
    const material = classItem.materials.find(m => m.id === id);
    if (!material) return null;

    return material.files;
  }

  // Submit feedback for a class
  function submitFeedback(classId, feedbackData) {
    const classItem = getClassById(classId);
    if (!classItem) return false;

    classItem.feedback = feedbackData;
    classItem.hasFeedback = true;

    return true;
  }

  // Check if a class has feedback
  function hasFeedback(classId) {
    const classItem = getClassById(classId);
    if (!classItem) return false;

    return classItem.hasFeedback === true;
  }

  // Get feedback for a class
  function getFeedback(classId) {
    const classItem = getClassById(classId);
    if (!classItem) return null;

    return classItem.feedback;
  }

  // Download certificate for a class
  function downloadCertificate(classId) {
    const classItem = getClassById(classId);
    if (!classItem) return false;

    // Mark certificate as downloaded
    classItem.certificateDownloaded = true;

    // Add completion date using helper function
    setCompletionDate(classItem);

    return true;
  }

  // Check if certificate is downloaded
  function isCertificateDownloaded(classId) {
    const classItem = getClassById(classId);
    if (!classItem) return false;

    return classItem.certificateDownloaded === true;
  }

  // Add a student to a class
  function addStudentToClass(classId, studentData) {
    const classItem = getClassById(classId);
    if (!classItem) return false;

    // Initialize students array if it doesn't exist
    if (!classItem.students) {
      classItem.students = [];
    }

    // Generate a new unique ID for the student
    const maxId = classItem.students.length > 0
      ? Math.max(...classItem.students.map(s => s.id))
      : (classId * 1000);
    const newId = maxId + 1;

    // Create the new student
    const newStudent = {
      id: newId,
      name: studentData.name || "New Student",
      profilePicture: studentData.profilePicture || "",
      mark: studentData.mark || 0
    };

    // Add the student to the class
    classItem.students.push(newStudent);

    // Update the studentsEnrolled count
    classItem.studentsEnrolled = classItem.students.length;

    return true;
  }



  // Check and update task status for all materials in a class
  function checkAndUpdateTaskStatus(classId) {
    const classItem = getClassById(classId);
    if (!classItem || !classItem.materials) return false;

    // Get current date to check for past due tasks
    const currentDate = new Date();

    // Check each material with a task
    classItem.materials.forEach(material => {
      if (material.hasTask && material.taskStatus === 'pending') {
        // Check if the task is past due
        if (material.dueDate && material.dueTime) {
          const dueDate = new Date(`${material.dueDate}T${material.dueTime}`);
          if (currentDate > dueDate) {
            // Update status to 'past_due' if past due date and still pending
            material.taskStatus = 'past_due';
          }
        }
      }
    });

    return true;
  }

  // Get all task history for a class
  function getAllTaskHistory(classId) {
    const classItem = getClassById(classId);
    if (!classItem || !classItem.materials) return [];

    // Return all materials that have tasks
    return classItem.materials.filter(material => material.hasTask);
  }

  // Synchronize material data with class
  function syncMaterialWithClass(classId, materialId, materialData) {
    const classItem = getClassById(classId);
    if (!classItem) return false;

    const id = ensureNumericId(materialId);
    const materialIndex = classItem.materials.findIndex(m => m.id === id);

    if (materialIndex === -1) return false;

    // Update material data
    Object.assign(classItem.materials[materialIndex], materialData);

    // Recalculate progress
    calculateProgress(classId);

    return true;
  }

  // Return readonly refs to prevent direct mutations
  return {
    classes: readonly(joinedClasses),
    currentClass: readonly(currentClass),
    currentMaterial: readonly(currentMaterial),
    setCurrentClass,
    setCurrentMaterial,
    markMaterialAsRead,
    markMaterialAsComplete,
    calculateProgress,
    getClassById,
    getClassesByStatus,
    getMaterialFiles,
    updateClassStatus,
    updateTaskStatus,
    addClass,
    addStudentToClass,
    checkAndUpdateTaskStatus,
    getAllTaskHistory,
    syncMaterialWithClass,
    findMaterial, // Expose findMaterial function for use in components
    // Feedback and certificate functions
    submitFeedback,
    hasFeedback,
    getFeedback,
    downloadCertificate,
    isCertificateDownloaded
  };
}

export default availableClassesData;
