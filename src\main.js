import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import './style.css';

// Add global navigation guards for debugging
router.beforeEach((to, from, next) => {
  console.log('=== ROUTER NAVIGATION GUARD ===');
  console.log('Navigating from:', from.path);
  console.log('Navigating to:', to.path);
  console.log('To route name:', to.name);
  console.log('To route meta:', to.meta);

  // Always proceed with navigation
  next();
});

const app = createApp(App);

// Add error handler
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err);
  console.error('Component:', vm);
  console.error('Error Info:', info);
};

app.use(router);
app.mount('#app');
