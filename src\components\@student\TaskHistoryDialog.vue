<template>
  <div v-if="show" class="fixed inset-0 z-[999] overflow-hidden">
    <!-- Full screen backdrop overlay -->
    <div class="absolute inset-0 bg-black/50" @click="$emit('close')"></div>

    <!-- Dialog content -->
    <div class="absolute inset-0 flex items-center justify-center p-4">
      <div class="bg-white rounded-lg p-4 sm:p-6 max-w-2xl w-full max-h-[90vh] flex flex-col relative z-[1000]">
        <!-- Header -->
        <div class="flex items-center justify-between mb-3 sm:mb-4 flex-shrink-0">
          <div class="flex items-center">
            <div class="flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8 border-2 border-orange rounded-full mr-2 sm:mr-3">
              <img src="/task.png" alt="Task Icon" class="h-3 w-3 sm:h-5 sm:w-5" />
            </div>
            <h2 class="text-base sm:text-xl font-semibold text-gray-700">Task History</h2>
          </div>
          <button @click="$emit('close')" class="text-gray-500 hover:text-gray-700 border-2 rounded-full border-gray-500 hover:border-gray-700">
            <svg class="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Task list -->
        <div class="flex-1 overflow-y-auto">
          <div class="border-b ml-1 mr-1 border-black"></div>
          <div v-if="taskHistory.length > 0" class="font-semibold p-2 mb-2 sm:mb-4 text-base sm:text-lg">Task</div>
          <div v-if="taskHistory.length === 0" class="text-center text-gray-500 py-4 text-sm sm:text-base">
            No tasks available yet. Complete your learning materials to see tasks here.
          </div>
          <div v-else v-for="task in taskHistory" :key="task.id"
               class="p-2 mb-0.5 hover:bg-gray-100 hover:rounded-lg transition-colors cursor-pointer"
               @click="navigateToTask(task.id)">
            <div class="flex flex-wrap sm:flex-nowrap justify-between items-center gap-2">
              <h3 class="text-base sm:text-lg font-light text-gray-800 w-full sm:max-w-[70%]">{{ task.title }}</h3>
              <span class="text-xs font-medium px-2 sm:px-3 py-1 rounded-full shadow-sm whitespace-nowrap"
                    :class="getStatusClass(task.taskStatus)">
                {{ formatTaskStatus(task.taskStatus) }}
              </span>
            </div>

            <!-- Posted date -->
            <div class="flex items-center mt-1 sm:mt-2">
              <span class="text-xs sm:text-sm text-gray-500">Posted: {{ formatDate(task.postedDate) }} {{ task.postedTime }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { watch, onBeforeUnmount, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useClassStore } from '@/data/availableClasses';
import { formatDate, getStatusClass, formatTaskStatus } from '@/utils/studentUtils';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  classId: {
    type: [String, Number],
    required: true
  }
});

const emit = defineEmits(['close']);
const router = useRouter();
const classStore = useClassStore();

const taskHistory = computed(() => {
  // Get all tasks that need attention (pending, past_due, submitted, under_review)
  const tasks = classStore.getAllTaskHistory(parseInt(props.classId))
    .filter(task => ['pending', 'past_due', 'submitted', 'under_review'].includes(task.taskStatus));

  // Separate tasks by status
  const pastDueTasks = tasks.filter(task => task.taskStatus === 'past_due');
  const pendingTasks = tasks.filter(task => task.taskStatus === 'pending');
  const submittedTasks = tasks.filter(task => task.taskStatus === 'submitted');
  const underReviewTasks = tasks.filter(task => task.taskStatus === 'under_review');

  // Sort past_due tasks by due date (most overdue first)
  const sortedPastDueTasks = pastDueTasks.sort((a, b) => {
    const dateA = new Date(`${a.dueDate}T${a.dueTime}`);
    const dateB = new Date(`${b.dueDate}T${b.dueTime}`);
    return dateA - dateB; // Ascending order - oldest due date first (most overdue)
  });

  // Sort pending tasks by due date (closest first)
  const sortedPendingTasks = pendingTasks.sort((a, b) => {
    const dateA = new Date(`${a.dueDate}T${a.dueTime}`);
    const dateB = new Date(`${b.dueDate}T${b.dueTime}`);
    return dateA - dateB; // Ascending order - closest due date first
  });

  // Sort submitted tasks by submission date (most recent first)
  const sortedSubmittedTasks = submittedTasks.sort((a, b) => {
    const dateA = new Date(`${a.submissionDate || a.postedDate}T${a.submissionTime || '00:00'}`);
    const dateB = new Date(`${b.submissionDate || b.postedDate}T${b.submissionTime || '00:00'}`);
    return dateB - dateA; // Descending order - most recent first
  });

  // Sort under_review tasks by review start date (most recent first)
  const sortedUnderReviewTasks = underReviewTasks.sort((a, b) => {
    const dateA = new Date(`${a.reviewStartDate || a.submissionDate || a.postedDate}T${a.reviewStartTime || a.submissionTime || '00:00'}`);
    const dateB = new Date(`${b.reviewStartDate || b.submissionDate || b.postedDate}T${b.reviewStartTime || b.submissionTime || '00:00'}`);
    return dateB - dateA; // Descending order - most recent first
  });

  // Combine the arrays with priority order: past_due, pending, under_review, submitted
  return [
    ...sortedPastDueTasks,
    ...sortedPendingTasks,
    ...sortedUnderReviewTasks,
    ...sortedSubmittedTasks
  ];
});

const navigateToTask = (materialId) => {
  classStore.setCurrentMaterial(materialId);
  emit('close');
  router.push({
    name: 'DetailTaskHistory',
    params: {
      classId: props.classId,
      materialId
    },
    query: {
      source: 'dialog_task' // Add query parameter to indicate source is task history dialog
    }
  });
};

// formatTaskStatus, getStatusClass, and formatDate are now imported from studentUtils.js

// Watch for dialog state changes
watch(() => props.show, (isVisible) => {
  if (isVisible) {
    document.documentElement.classList.add('overflow-hidden');
  } else {
    document.documentElement.classList.remove('overflow-hidden');
  }
});

// Cleanup on component unmount
onBeforeUnmount(() => {
  document.documentElement.classList.remove('overflow-hidden');
});
</script>