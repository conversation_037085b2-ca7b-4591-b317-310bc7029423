<template>
  <div>
    <HeroSection
      badge-text="Bootcamp"
      title-text="Explore Our Bootcamp Programs"
      subtitle-text="Choose the right path for you. Each class is designed to sharpen your skills and prepare you for real-world projects."
      description-text="Ready to grow?"
      whatsappText="Try Free Consultation"
      :hero-image="headerImage"
      :use-overlay="true"
      :is-dark="true"
      :hide-right-section="true"
    />

    <!-- Program Cards Section -->
    <section class="py-5">
      <div class="container mx-auto px-4 pt-16 pb-16">
        <h2 class="text-center text-4xl font-bold mb-12">
          Explore All Our Bootcamp Classes — Find<br>
          the Right Program for You!
        </h2>

        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center items-center py-5">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>

        <!-- Program Cards -->
        <div v-else class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div v-for="program in programs" :key="program.id">
            <ProgramCard :program="program" />
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import ProgramCard from '@/components/ProgramCard.vue';
import { programs } from '@/data/programs';
import HeroSection from '@/components/HeroSection.vue';
const headerImage = '/header.jpg';

const loading = ref(true);

onMounted(() => {
  loading.value = false;
});

</script>
