<template>
  <div>
    <!-- Display Hero Section only on Home Page -->
    <HeroSection
      v-if="isHomePage"
      badge-text="Program"
      title-text="Level Up Your Tech Skills!"
      subtitle-text="Join our intensive Bootcamps and Free Classes"
      description-text="Learn from industry experts and kickstart your career!"
      whatsappText="Try Free Consultation"
      :hero-image="homeHeroImage"
      :use-overlay="false"
      :is-dark="false"
      :is-detail-page="false"
    />
    <!-- Why Choose Us Section -->
    <WhyChooseUsSection />

    <!-- Program Section -->
    <section class="py-16 bg-[#ff8c00]">
      <div class="container mx-auto px-4">
        <div class="relative mb-12">
          <h2 class="text-center text-white text-3xl md:text-4xl font-bold">Our Program Section</h2>
          <!-- Yellow dots decoration -->
          <div class="absolute -top-4 right-1/4">
            <div class="grid grid-cols-4 gap-1">
              <div v-for="n in 16" :key="n" class="w-2 h-2 bg-yellow-400 rounded-full"></div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Back End Development Card -->
          <div
            class="group bg-white rounded-xl shadow-md transition-all duration-300 cursor-pointer overflow-hidden hover:bg-[#006D77] hover:text-white"
            @click="selectProgram('backend')"
          >
            <div class="p-6">
              <div class="flex items-center mb-4">
                <div class="bg-[#e6f2f2] p-3 rounded-lg mr-3 group-hover:bg-white/20">
                  <svg class="w-6 h-6 text-[#006D77] group-hover:text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                  </svg>
                </div>
                <h3 class="text-xl font-bold">Back End Development</h3>
              </div>

              <p class="text-gray-600 group-hover:text-white/90 mb-4">Join our intensive programs featuring Nest JS and Golang.</p>

              <span
                class="inline-flex items-center text-[#006D77] group-hover:text-white cursor-pointer"
              >
                Learn More
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </span>
            </div>
          </div>

          <!-- Front End Development Card -->
          <div
            class="group bg-white rounded-xl shadow-md transition-all duration-300 cursor-pointer overflow-hidden hover:bg-[#6366f1] hover:text-white"
            @click="selectProgram('frontend')"
          >
            <div class="p-6">
              <div class="flex items-center mb-4">
                <div class="bg-[#ededfc] p-3 rounded-lg mr-3 group-hover:bg-white/20">
                  <svg class="w-6 h-6 text-[#6366f1] group-hover:text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 class="text-xl font-bold">Front End Development</h3>
              </div>

              <p class="text-gray-600 group-hover:text-white/90 mb-4">Dive into programs focused on Next JS dan React JS.</p>

              <span
                class="inline-flex items-center text-[#6366f1] group-hover:text-white cursor-pointer"
              >
                Learn More
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </span>
            </div>
          </div>

          <!-- Mobile App Development Card -->
          <div
            class="group bg-white rounded-xl shadow-md transition-all duration-300 cursor-pointer overflow-hidden hover:bg-[#f472b6] hover:text-white"
            @click="selectProgram('mobile')"
          >
            <div class="p-6">
              <div class="flex items-center mb-4">
                <div class="bg-[#fdf2f8] p-3 rounded-lg mr-3 group-hover:bg-white/20">
                  <svg class="w-6 h-6 text-[#f472b6] group-hover:text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 class="text-xl font-bold">Mobile App Development</h3>
              </div>

              <p class="text-gray-600 group-hover:text-white/90 mb-4">Explore Flutter and Kotlin mobile development tools.</p>

              <span
                class="inline-flex items-center text-[#f472b6] group-hover:text-white cursor-pointer"
              >
                Learn More
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Mentors Section -->
    <section class="bg-gray-50 py-16 text-center">
      <div class="container mx-auto px-4">
        <h3 class="text-emerald-700 font-bold text-2xl mb-2">Mentors</h3>
        <h2 class="text-gray-800 font-bold text-4xl mb-2">Meet the Heroes</h2>
        <p class="text-gray-400 text-sm mb-10 mx-auto max-w-xl">
          Our expert mentors bring years of industry experience and a passion for teaching. They've worked with top tech companies and are ready to guide you through your learning journey.
        </p>

        <div class="relative">
          <!-- Navigation Buttons - Replaced Bootstrap icons with SVG icons -->
          <button
            class="absolute top-1/2 -translate-y-1/2 -left-5 z-10 w-10 h-10 rounded-full bg-white border border-gray-100 flex items-center justify-center text-teal-600 transition-all duration-300 hover:bg-teal-600 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed disabled:text-gray-500 disabled:hover:bg-white"
            @click="previousSlide"
            :disabled="currentSlide === 0"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <button
            class="absolute top-1/2 -translate-y-1/2 -right-5 z-10 w-10 h-10 rounded-full bg-white border border-gray-100 flex items-center justify-center text-teal-600 transition-all duration-300 hover:bg-teal-600 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed disabled:text-gray-500 disabled:hover:bg-white"
            @click="nextSlide"
            :disabled="currentSlide >= Math.ceil(mentors.length / itemsPerSlide) - 1"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>

          <!-- Draggable Mentor Cards Container -->
          <div
            class="overflow-hidden relative py-5"
            ref="sliderContainer"
            @mousedown="startDrag"
            @mousemove="onDrag"
            @mouseup="endDrag"
            @mouseleave="endDrag"
            @touchstart="startDrag"
            @touchmove="onDrag"
            @touchend="endDrag"
          >
            <div
              class="flex w-full"
              :style="{ transform: `translateX(${-currentSlide * 100}%)`, transition: isDragging ? 'none' : 'transform 0.3s ease' }"
            >
              <div v-for="mentor in mentors" :key="mentor.id" class="w-1/4 px-4 lg:w-1/4 md:w-1/3 sm:w-1/2 xs:w-full flex-shrink-0">
                <MentorCard :mentor="mentor" />
              </div>
            </div>
          </div>

          <!-- Pagination Dots -->
          <div class="mt-8 relative z-10">
            <div class="flex justify-center gap-2">
              <button
                v-for="n in Math.ceil(mentors.length / itemsPerSlide)"
                :key="n"
                class="h-1 transition-all duration-300 cursor-pointer"
                :class="[
                  currentSlide === n - 1 ? 'w-12 bg-[#006D77]' : 'w-8 bg-[#006D77]/30'
                ]"
                @click="goToSlide(n - 1)"
              ></button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonial Section -->
    <section class="bg-[#ff8c00] text-center py-12 relative overflow-hidden">
      <div class="container mx-auto px-4">
        <div class="flex items-center justify-center mb-8">
          <img
            src="/logo.png"
            alt="Flow Camp"
            class="h-10 brightness-0 invert"
          />
          <h3 class="text-white text-2xl font-bold ml-3">Flow Camp</h3>
        </div>

        <!-- Draggable Testimonial Container -->
        <div
          class="overflow-hidden relative select-none"
          ref="testimonialContainer"
          @mousedown="startTestimonialDrag"
          @mousemove="onTestimonialDrag"
          @mouseup="endTestimonialDrag"
          @mouseleave="endTestimonialDrag"
          @touchstart="startTestimonialDrag"
          @touchmove="onTestimonialDrag"
          @touchend="endTestimonialDrag"
        >
          <div
            class="flex w-full"
            :style="{ transform: `translateX(${-currentTestimonial * 100}%)`, transition: isTestimonialDragging ? 'none' : 'transform 0.3s ease' }"
          >
            <div v-for="testimonial in testimonials" :key="testimonial.id" class="w-full flex-shrink-0">
              <div class="px-5">
                <p class="text-white text-2xl font-medium leading-relaxed mb-8 mx-auto max-w-4xl">
                  "{{ testimonial.text }}"
                </p>

                <div class="flex flex-col items-center">
                  <img
                    :src="testimonial.image"
                    :alt="testimonial.name"
                    class="w-16 h-16 rounded-full object-cover border-2 border-white border-opacity-20 mb-3"
                  />
                  <h5 class="text-white text-lg font-semibold mb-1">{{ testimonial.name }}</h5>
                  <p class="text-[#98E5DE] mb-3">{{ testimonial.role }}</p>
                  <div class="flex justify-center gap-1">
                    <!-- Replaced Bootstrap star icons with SVG stars -->
                    <svg
                      v-for="star in testimonial.rating"
                      :key="star"
                      class="h-5 w-5 text-yellow-400"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Pagination Dots -->
        <div class="mt-8 relative z-10">
          <div class="flex justify-center gap-2">
            <button
              v-for="n in testimonials.length"
              :key="n"
              class="h-1 transition-all duration-300 cursor-pointer"
              :class="[
                currentTestimonial === n - 1 ? 'w-12 bg-white' : 'w-8 bg-white/30'
              ]"
              @click="goToTestimonial(n - 1)"
            ></button>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted } from 'vue';
import { mentors, testimonials } from '@/data/programs';
import HeroSection from '@/components/HeroSection.vue';
import WhyChooseUsSection from '@/components/WhyChoose.vue';
import MentorCard from '@/components/MentorCard.vue';
import StudentImageSection from '@/components/StudentImageSection.vue';
import ProgramTypeCard from '@/components/ProgramTypeCard.vue';

const homeHeroImage = '/join.png';
const homeStudentImage = '/choose.png';

export default {
  name: 'Home',
  components: {
    HeroSection,
    WhyChooseUsSection,
    MentorCard,
    StudentImageSection,
    ProgramTypeCard
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    const isHomePage = route.path === '/';
    const currentSlide = ref(0);
    const currentTestimonial = ref(0);
    const isDragging = ref(false);
    const startX = ref(0);
    const scrollLeft = ref(0);
    const sliderContainer = ref(null);
    const itemsPerSlide = 4;
    const selectedProgram = ref(null);

    const testimonialContainer = ref(null);
    const isTestimonialDragging = ref(false);
    const testimonialStartX = ref(0);
    const testimonialScrollLeft = ref(0);

    const startDrag = (e) => {
      isDragging.value = true;
      startX.value = e.type === 'mousedown' ? e.pageX : e.touches[0].pageX;
      scrollLeft.value = currentSlide.value;
    };

    const onDrag = (e) => {
      if (!isDragging.value) return;
      e.preventDefault();

      const x = e.type === 'mousemove' ? e.pageX : e.touches[0].pageX;
      const walk = (startX.value - x) / sliderContainer.value?.offsetWidth;

      let newSlide = scrollLeft.value + walk;
      newSlide = Math.max(0, Math.min(newSlide, Math.ceil(mentors.length / itemsPerSlide) - 1));
      currentSlide.value = newSlide;
    };

    const endDrag = () => {
      isDragging.value = false;
      currentSlide.value = Math.round(currentSlide.value);
    };

    const nextSlide = () => {
      if (currentSlide.value < Math.ceil(mentors.length / itemsPerSlide) - 1) {
        currentSlide.value++;
      }
    };

    const previousSlide = () => {
      if (currentSlide.value > 0) {
        currentSlide.value--;
      }
    };

    const goToSlide = (index) => {
      currentSlide.value = index;
    };

    const nextTestimonial = () => {
      currentTestimonial.value = (currentTestimonial.value + 1) % testimonials.length;
    };

    // Function to go to previous testimonial (used in swipe functionality)
    const previousTestimonial = () => {
      currentTestimonial.value = currentTestimonial.value === 0
        ? testimonials.length - 1
        : currentTestimonial.value - 1;
    };

    const goToTestimonial = (index) => {
      currentTestimonial.value = index;
    };

    const startTestimonialDrag = (e) => {
      isTestimonialDragging.value = true;
      testimonialStartX.value = e.type === 'mousedown' ? e.pageX : e.touches[0].pageX;
      testimonialScrollLeft.value = currentTestimonial.value;
    };

    const onTestimonialDrag = (e) => {
      if (!isTestimonialDragging.value) return;
      e.preventDefault();

      const x = e.type === 'mousemove' ? e.pageX : e.touches[0].pageX;
      const walk = (testimonialStartX.value - x) / testimonialContainer.value?.offsetWidth;

      let newSlide = testimonialScrollLeft.value + walk;
      newSlide = Math.max(0, Math.min(newSlide, testimonials.length - 1));
      currentTestimonial.value = newSlide;
    };

    const endTestimonialDrag = () => {
      isTestimonialDragging.value = false;
      currentTestimonial.value = Math.round(currentTestimonial.value);
    };

    const selectProgram = (program) => {
      selectedProgram.value = program;
      // Navigate directly to the BootcampPage without any query parameters
      router.push('/bootcamp');
    };

    // Auto rotate testimonial setiap 7 detik
    onMounted(() => {
      setInterval(nextTestimonial, 7000);
    });

    return {
      isHomePage,
      mentors,
      currentSlide,
      isDragging,
      itemsPerSlide,
      sliderContainer,
      startDrag,
      onDrag,
      endDrag,
      nextSlide,
      previousSlide,
      goToSlide,
      testimonials,
      currentTestimonial,
      goToTestimonial,
      testimonialContainer,
      isTestimonialDragging,
      startTestimonialDrag,
      onTestimonialDrag,
      endTestimonialDrag,
      homeHeroImage,
      homeStudentImage,
      selectedProgram,
      selectProgram,
      previousTestimonial // Include in returned object to avoid unused warning
    };
  },
  data() {
    return {
      programTypes: [
        {
          type: 'backend',
          title: 'Back End Development',
          description: 'Join our intensive programs featuring Nest JS and Golang',
          icon: 'server',
          color: 'teal'
        },
        {
          type: 'frontend',
          title: 'Front End Development',
          description: 'Dive into programs focused on Next JS dan React JS.',
          icon: 'desktop',
          color: 'blue'
        },
        {
          type: 'mobile',
          title: 'Mobile App Development',
          description: 'Explore Flutter and Kotlin mobile development tools.',
          icon: 'device-mobile',
          color: 'pink'
        }
      ]
    }
  },
  methods: {
  }
}
</script>