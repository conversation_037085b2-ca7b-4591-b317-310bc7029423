<template>
  <section class="bg-[#ff8c00] pt-8 pb-0 overflow-hidden">
    <div class="bg-white rounded-t-[30px] pb-8 relative overflow-hidden">
      <div class="container mx-auto px-4">
        <h2 class="text-center font-bold text-3xl pt-12 mb-10">Why Choose Our Program?</h2>
        
        <div class="flex flex-wrap">
          <!-- Cards Section - Left Side -->
          <div class="w-full md:w-7/12 px-4 pb-5">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- Card 1: <PERSON>rn from Industry Experts -->
              <div class="border border-[#f8d0a9] rounded-3xl p-6 hover:translate-y-[-5px] transition-transform duration-300">
                <div class="flex">
                  <div class="mr-3 text-4xl">🚀</div>
                  <div>
                    <h5 class="font-bold mb-2 text-lg">Learn from Industry Experts</h5>
                    <p class="text-gray-600 text-sm">
                      Our bootcamps and free classes are led by experienced professionals who have worked on real-world projects. Get insights, tips, and practical knowledge straight from the experts.
                    </p>
                  </div>
                </div>
              </div>
              
              <!-- Card 2: Comprehensive and Up-to-Date Curriculum -->
              <div class="border border-[#f8d0a9] rounded-3xl p-6 hover:translate-y-[-5px] transition-transform duration-300">
                <div class="flex">
                  <div class="mr-3 text-4xl">📋</div>
                  <div>
                    <h5 class="font-bold mb-2 text-lg">Comprehensive and Up-to-Date Curriculum</h5>
                    <p class="text-gray-600 text-sm">
                      We offer carefully designed programs in Back-End, Front-End, and Mobile App Development. Our curriculum is always updated to match the latest industry trends and technologies.
                    </p>
                  </div>
                </div>
              </div>
              
              <!-- Card 3: Flexible Learning Options -->
              <div class="border border-[#f8d0a9] rounded-3xl p-6 hover:translate-y-[-5px] transition-transform duration-300">
                <div class="flex">
                  <div class="mr-3 text-4xl">🏅</div>
                  <div>
                    <h5 class="font-bold mb-2 text-lg">Flexible Learning Options</h5>
                    <p class="text-gray-600 text-sm">
                      Whether you want to dive deep through our intensive bootcamps or explore new skills with free classes, we have flexible options tailored to fit your learning pace and goals.
                    </p>
                  </div>
                </div>
              </div>
              
              <!-- Card 4: Join a Supportive Tech Community -->
              <div class="border border-[#f8d0a9] rounded-3xl p-6 hover:translate-y-[-5px] transition-transform duration-300">
                <div class="flex">
                  <div class="mr-3 text-4xl">🔥</div>
                  <div>
                    <h5 class="font-bold mb-2 text-lg">Join a Supportive Tech Community</h5>
                    <p class="text-gray-600 text-sm">
                      Learning is better together. Connect with a vibrant community of learners, share ideas, and grow alongside others who are just as passionate about tech as you are.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Student Image Section - Right Side -->
          <div class="w-full md:w-5/12 relative flex items-center justify-center">
            <div class="relative">
              <!-- Main Circle -->
              <div class="absolute w-[450px] h-[450px] border border-yellow-400 rounded-full z-0 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
              
              <!-- Secondary Circle -->
              <div class="absolute w-[350px] h-[350px] border border-yellow-400 rounded-full z-0 bottom-[-5%] right-0"></div>
              
              <!-- Yellow Circles -->
              <div class="absolute w-[50px] h-[50px] bg-yellow-400 rounded-full top-[10%] right-[5%] z-10"></div>
              <div class="absolute w-[30px] h-[30px] bg-yellow-400 rounded-full top-[30%] left-[5%] z-10"></div>
              <div class="absolute w-[30px] h-[30px] bg-yellow-400 rounded-full bottom-[20%] right-[30%] z-10"></div>
              
              <!-- Orange Circle Background -->
              <div class="w-[350px] h-[350px] bg-[#ff8c00] rounded-full relative z-0 flex items-center justify-center">
                <img :src="studentImage" alt="Student" class="relative z-10 max-w-full h-auto" />
              </div>
              
              <!-- Badges -->
              <div class="absolute top-[10%] left-[5%] z-20 bg-white rounded-xl shadow-md p-2 flex items-center">
                <div class="bg-teal-600 w-9 h-9 rounded-lg flex items-center justify-center mr-2">
                  <i class="bi bi-journal-text text-white text-xl"></i>
                </div>
                <div>
                  <div class="font-bold text-lg">2K+</div>
                  <div class="text-xs text-gray-600">Video Courses</div>
                </div>
              </div>
              
              <div class="absolute bottom-[30%] right-[-5%] z-20 bg-white rounded-xl shadow-md p-2 flex items-center">
                <div class="bg-teal-600 w-9 h-9 rounded-lg flex items-center justify-center mr-2">
                  <i class="bi bi-people-fill text-white text-xl"></i>
                </div>
                <div>
                  <div class="font-bold text-lg">250+</div>
                  <div class="text-xs text-gray-600">Assisted Students</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
const studentImage = '/choose.png';
</script>
