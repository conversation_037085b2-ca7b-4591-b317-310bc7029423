<template>
  <StudentLayoutWrapper>
    <StudentBreadcrumb :items="breadcrumbItems" />

    <!-- Task Complete Dialog -->
    <DialogBox
      :show="showTaskCompleteDialog"
      type="taskComplete"
      @close="closeTaskCompleteDialog"
    />

    <!-- Grades Dialog -->
    <DialogBox
      :show="showGradesDialog"
      type="viewGrades"
      :gradeData="gradeData"
      @close="closeGradesDialog"
    />

    <div class="max-w-4xl mx-auto">
        <!-- Title, Status & Posted Date -->
        <div class="flex flex-col md:flex-row justify-between items-start mb-4 sm:mb-6">
          <!-- Title on the left -->
          <h1 class="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-3 md:mb-0 md:max-w-[60%]">{{ currentMaterial?.title }}</h1>

          <!-- Status badge and posted date -->
          <div class="flex flex-wrap items-center space-x-0 sm:space-x-3 self-start md:self-start gap-2 sm:gap-0">
            <span class="text-xs sm:text-sm font-medium px-3 sm:px-4 py-1 sm:py-1.5 rounded-full shadow-sm whitespace-nowrap"
                  :class="getStatusClass(currentMaterial?.taskStatus)">
              {{ formatTaskStatus(currentMaterial?.taskStatus) }}
            </span>
            <span class="text-gray-500 text-xs sm:text-sm whitespace-nowrap">Posted {{ formatDate(currentMaterial?.postedDate) }} {{ currentMaterial?.postedTime }}</span>
          </div>
        </div>

        <!-- Task Description -->
        <div class="bg-white rounded-lg sm:mb-8">
          <div class="space-y-3 sm:space-y-4">
            <p class="text-sm sm:text-base text-gray-700 leading-relaxed">{{ currentMaterial?.taskDescription }}</p>

            <!-- Posted Date and Status -->
            <div class="flex items-center justify-between text-xs sm:text-sm text-gray-500">
              <span>Due {{ formatDate(currentMaterial?.dueDate) }} {{ currentMaterial?.dueTime }}</span>
            </div>
          </div>
        </div>

        <!-- Attachment Section -->
        <div class="bg-white rounded-lg">
          <div class="mb-3 sm:mb-4">

            <!-- Link Input Section -->
            <div class="mt-3">
              <!-- Link Input (only shown when task is editable) -->
              <div v-if="isTaskEditable" class="flex flex-col w-full">
                <div class="mb-2">
                  <label for="submissionLink" class="block text-sm font-medium text-gray-700 mb-1">Submission Reference</label>
                  <div class="flex items-center w-full">
                    <div class="relative flex-grow">
                      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5zm-5 5a2 2 0 012.828 0 1 1 0 101.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5a2 2 0 11-2.828-2.828l3-3z" clip-rule="evenodd" />
                        </svg>
                      </div>
                      <input
                        id="submissionLink"
                        type="text"
                        v-model="submissionLink"
                        @input="validateLink"
                        placeholder="Enter link for your submission"
                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange focus:border-orange sm:text-sm"
                      />
                    </div>
                  </div>
                </div>
                <p v-if="submissionLink" class="text-xs sm:text-sm text-teal-600 mt-1">
                  <span class="font-medium">Link added:</span> {{ truncateLink(submissionLink) }}
                </p>
              </div>

              <!-- Past Due Warning Message -->
              <div v-if="isPastDue" class="mt-3 sm:mt-4 bg-red-50 border border-red-200 rounded-lg p-3 sm:p-4">
                <div class="flex items-start sm:items-center">
                  <svg class="w-4 h-4 sm:w-5 sm:h-5 text-red-600 mr-2 mt-0.5 sm:mt-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clip-rule="evenodd"></path>
                  </svg>
                  <p class="text-red-600 text-xs sm:text-sm">
                    <span class="font-medium">Warning:</span> This task is past its due date ({{ formatDate(currentMaterial?.dueDate) }} {{ currentMaterial?.dueTime }}).
                    You can still submit it, but it will be marked as late.
                  </p>
                </div>
              </div>

              <!-- Task Already Submitted Message -->
              <div v-if="isTaskSubmitted" class="mt-3 sm:mt-4 bg-orange-50 border border-orange-200 rounded-lg p-3 sm:p-4">
                <div class="flex items-start sm:items-center">
                  <svg class="w-4 h-4 sm:w-5 sm:h-5 text-orange mr-2 mt-0.5 sm:mt-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clip-rule="evenodd"></path>
                  </svg>
                  <div class="text-orange text-xs sm:text-sm">
                    <p>
                      Your task has been submitted and is currently {{ currentMaterial?.taskStatus === 'submitted' ? 'awaiting review' : 'under review' }}.
                      You cannot make changes at this time.
                    </p>
                    <p v-if="currentMaterial?.taskStatus === 'under_review'" class="mt-1 font-medium">
                      A mentor is currently reviewing your submission.
                    </p>
                    <p v-if="submittedLink" class="mt-2">
                      <span class="font-medium">Submitted reference: </span>
                      <span v-if="isValidUrl(submittedLink)">
                        <a :href="submittedLink" target="_blank" class="text-blue-600 hover:underline">{{ truncateLink(submittedLink) }}</a>
                      </span>
                      <span v-else class="text-gray-700">{{ truncateLink(submittedLink) }}</span>
                    </p>
                    <p v-if="currentMaterial?.submissionDate" class="mt-1">
                      Submitted on: {{ formatDate(currentMaterial.submissionDate) }} {{ currentMaterial.submissionTime }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- Task Reviewed Message -->
              <div v-if="isTaskReviewed" class="mt-3 sm:mt-4 bg-orange-50 border border-orange-200 rounded-lg p-3 sm:p-4">
                <div class="flex items-start sm:items-center">
                  <svg class="w-4 h-4 sm:w-5 sm:h-5 text-orange mr-2 mt-0.5 sm:mt-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  <div class="text-orange text-xs sm:text-sm">
                    <p>
                      Your task has been reviewed by the mentor. Click the "View Grades" button to see your assessment.
                    </p>
                    <p v-if="submittedLink" class="mt-2">
                      <span class="font-medium">Submitted reference:</span>
                      <span v-if="isValidUrl(submittedLink)">
                        <a :href="submittedLink" target="_blank" class="text-blue-600 hover:underline">{{ truncateLink(submittedLink) }}</a>
                      </span>
                      <span v-else class="text-gray-700">{{ truncateLink(submittedLink) }}</span>
                    </p>
                  </div>
                </div>
              </div>

              <!-- Error Message -->
              <p v-if="errorMessage" class="mt-2 text-xs sm:text-sm text-red-500">{{ errorMessage }}</p>
              <!-- Success Message -->
              <p v-if="successMessage" class="mt-2 text-xs sm:text-sm text-green-600">{{ successMessage }}</p>
            </div>

            <!-- Submit Button or View Grades Button -->
            <div class="flex justify-end mt-4 sm:mt-6">
              <!-- Submit Task Button (shown when task is editable or submitted/under review) -->
              <button
                v-if="isTaskEditable || isTaskSubmitted"
                class="px-4 sm:px-6 py-1.5 sm:py-2 rounded-md transition-colors text-xs sm:text-sm"
                :class="{
                  'bg-orange text-white hover:bg-orange-dark': isTaskEditable,
                  'bg-gray-300 text-gray-500 cursor-not-allowed': isTaskSubmitted
                }"
                @click="handleSubmit"
                :disabled="!canSubmit || isSubmitting || isTaskSubmitted"
              >
                <span v-if="isSubmitting">Submitting...</span>
                <span v-else>Submit</span>
              </button>

              <!-- View Grades Button (only shown when task is reviewed) -->
              <button
                v-else-if="isTaskReviewed"
                class="px-4 sm:px-6 py-1.5 sm:py-2 bg-orange text-white rounded-md hover:bg-orange-dark transition-colors text-xs sm:text-sm"
                @click="viewGrades"
              >
                View Grades
              </button>
            </div>
          </div>
        </div>
      </div>
  </StudentLayoutWrapper>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useClassStore, STORAGE_KEYS } from '../data/availableClasses';
import StudentLayoutWrapper from '../components/@student/StudentLayoutWrapper.vue';
import StudentBreadcrumb from '../components/@student/StudentBreadcrumb.vue';
import DialogBox from '../components/@student/DialogBox.vue';
import { formatDate, formatTaskStatus, getStatusClass } from '../utils/studentUtils';

const router = useRouter();
const classStore = useClassStore();
const currentClass = classStore.currentClass;
const currentMaterial = classStore.currentMaterial;

// Link handling
const submissionLink = ref('');
const errorMessage = ref('');
const successMessage = ref('');
const isSubmitting = ref(false);
const showTaskCompleteDialog = ref(false);
const showGradesDialog = ref(false);

// Grade data for the current material
const gradeData = ref({
  score: 0,
  maxScore: 100,
  submittedOn: '',
  reviewedOn: ''
});

// URL validation regex
const URL_REGEX = /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/;

// Check if task can be submitted (has valid link and no errors)
const canSubmit = computed(() => {
  return submissionLink.value && URL_REGEX.test(submissionLink.value) && !errorMessage.value;
});

// Check if task is in a state where it can be edited (not submitted or under review)
const isTaskEditable = computed(() => {
  const status = currentMaterial.value?.taskStatus;
  return status === 'pending' || status === 'past_due';
});

// Check if task is past due
const isPastDue = computed(() => {
  return currentMaterial.value?.taskStatus === 'past_due';
});

// Check if task has been submitted and is awaiting review or under review
const isTaskSubmitted = computed(() => {
  const status = currentMaterial.value?.taskStatus;
  return status === 'submitted' || status === 'under_review';
});

// Check if task has been reviewed and grades are available
const isTaskReviewed = computed(() => {
  return currentMaterial.value?.taskStatus === 'reviewed';
});

// Get the submitted link from localStorage if available
const submittedLink = computed(() => {
  if (!currentMaterial.value) return '';
  return localStorage.getItem(`submission-link-${currentMaterial.value.id}`) || '';
});

function truncateLink(text) {
  if (!text) return '';
  // If text is longer than 50 characters, truncate it
  return text.length > 50 ? text.substring(0, 47) + '...' : text;
}

function isValidUrl(text) {
  if (!text) return false;
  try {
    // Only consider it a URL if it has a proper protocol prefix
    return /^(https?:\/\/|ftp:\/\/|mailto:|tel:|file:|data:|#)/i.test(text);
  } catch (e) {
    return false;
  }
}

function validateLink() {
  errorMessage.value = '';
  successMessage.value = '';

  if (!submissionLink.value) {
    return;
  }

  // Only validate that the link is not empty
  if (submissionLink.value.trim() === '') {
    errorMessage.value = 'Please enter a link or text';
    return;
  }

  // No automatic prefix addition - keep user input exactly as entered
}

function mockUpload(linkData) {
  return new Promise((resolve) => {
    // Log the link data for debugging purposes
    console.log('Submitting link:', linkData.link);

    setTimeout(() => {
      resolve({
        success: true,
        message: 'Link submission successful',
        data: linkData
      });
    }, 1500);
  });
}

// Function to handle viewing grades
function viewGrades() {
  // Use setTimeout to simulate API call and ensure UI updates
  setTimeout(() => {
    successMessage.value = '';

    // Get the submission date from localStorage or material data
    let submissionDate;

    // First try to get from material's submissionDate if available
    if (currentMaterial.value && currentMaterial.value.submissionDate) {
      submissionDate = formatDate(currentMaterial.value.submissionDate);
    }
    // Then try localStorage
    else {
      submissionDate = localStorage.getItem(`submission-date-${currentMaterial.value.id}`);
    }

    // If still not found, use posted date or current date as fallback
    if (!submissionDate) {
      submissionDate = currentMaterial.value ?
        formatDate(currentMaterial.value.postedDate) :
        formatDate(new Date().toISOString().split('T')[0]);
    }

    // Get the review date from material or generate one
    let reviewDate;

    // First try to get from material's reviewedDate if available
    if (currentMaterial.value && currentMaterial.value.reviewedDate) {
      reviewDate = formatDate(currentMaterial.value.reviewedDate);
    }
    // Then try localStorage
    else {
      reviewDate = localStorage.getItem(`grade-reviewed-date-${currentMaterial.value.id}`);
    }

    // If still not found, generate a review date (2 days after submission)
    if (!reviewDate) {
      const submissionDateObj = new Date(submissionDate);
      const reviewDateObj = new Date(submissionDateObj);
      reviewDateObj.setDate(reviewDateObj.getDate() + 2);
      reviewDate = formatDate(reviewDateObj.toISOString().split('T')[0]);
    }

    // Get task-specific score if available
    let score = 0;
    let maxScore = 100;

    // First check if the material has a taskScore property
    if (currentMaterial.value && currentMaterial.value.taskScore !== undefined) {
      score = currentMaterial.value.taskScore;
      maxScore = currentMaterial.value.taskMaxScore || 100;
    }
    // Then try localStorage
    else {
      const savedScore = localStorage.getItem(`grade-score-${currentMaterial.value.id}`);
      if (savedScore) {
        score = parseInt(savedScore);
      }
      // If still not found, generate a score based on student data or a default
      else {
        // Get student data from the class
        const studentId = 1001; // This would normally come from the user's profile
        let studentData = null;

        if (currentClass.value && currentClass.value.students) {
          studentData = currentClass.value.students.find(s => s.id === studentId);
        }

        // Calculate score based on student data or default to a high score
        score = studentData ? studentData.mark : 20;

        // NOTE: Automatic score updating has been disabled
        // In a real app, this would be set by the mentor
        console.log('Score updating simulation has been disabled to maintain task status consistency');
      }
    }

    // Store the grade data in localStorage for persistence
    if (currentMaterial.value) {
      localStorage.setItem(`grade-score-${currentMaterial.value.id}`, score.toString());
      localStorage.setItem(`grade-reviewed-date-${currentMaterial.value.id}`, reviewDate);
    }

    // Set grade data
    gradeData.value = {
      score: score,
      maxScore: maxScore,
      submittedOn: submissionDate,
      reviewedOn: reviewDate
    };

    // Show the grades dialog
    showGradesDialog.value = true;
  });
}

function closeTaskCompleteDialog() {
  showTaskCompleteDialog.value = false;
}

function closeGradesDialog() {
  showGradesDialog.value = false;
}

// This function simulates a mentor reviewing a task
function simulateMentorGrading() {
  if (!currentMaterial.value || !currentClass.value) return;

  // Get the current task status
  const currentStatus = currentMaterial.value.taskStatus;

  // Only proceed if the task is in 'submitted' status
  if (currentStatus === 'submitted') {
    // Update task status to 'under_review'
    classStore.updateTaskStatus(currentClass.value.id, currentMaterial.value.id, 'under_review');

    // Show a message indicating the task is now under review
    successMessage.value = 'Task is now under review by a mentor.';

    // After a delay, simulate the mentor completing the review
    setTimeout(() => {
      // Update task status to 'reviewed'
      classStore.updateTaskStatus(currentClass.value.id, currentMaterial.value.id, 'reviewed');

      // Generate a random score between 70-100
      const score = Math.floor(Math.random() * 31) + 70;

      // Update the task score
      if (currentMaterial.value) {
        currentMaterial.value.taskScore = score;
      }

      // Show a message indicating the task has been reviewed
      successMessage.value = 'Task has been reviewed by a mentor. You can now view your grades.';

      // Clear the message after 3 seconds
      setTimeout(() => {
        successMessage.value = '';
      }, 3000);
    }, 2000);
  } else {
    // Show a message indicating the task is not in the right state for review
    successMessage.value = `Task cannot be reviewed because it is in '${formatTaskStatus(currentStatus)}' status.`;

    // Clear the message after 3 seconds
    setTimeout(() => {
      successMessage.value = '';
    }, 3000);
  }
}

async function handleSubmit() {
  if (!submissionLink.value || !currentMaterial.value || !currentClass.value) return;

  // Only check if the link is not empty
  if (submissionLink.value.trim() === '') {
    errorMessage.value = 'Please enter a link or text before submitting';
    return;
  }

  try {
    isSubmitting.value = true;
    errorMessage.value = '';
    successMessage.value = '';

    // Create submission data
    const submissionData = {
      link: submissionLink.value,
      materialId: currentMaterial.value.id,
      classId: currentClass.value.id
    };

    const response = await mockUpload(submissionData);
    console.log('Link submission response:', response);

    if (response.success) {
      // Update task status in the store
      classStore.updateTaskStatus(currentClass.value.id, currentMaterial.value.id, 'submitted');

      // Store the submission link in localStorage for potential future reference
      localStorage.setItem(`submission-link-${currentMaterial.value.id}`, submissionLink.value);

      // Get current date and time for submission timestamp
      const today = new Date();
      const formattedDate = formatDate(today.toISOString().split('T')[0]);

      // Format time as HH:MM
      const hours = String(today.getHours()).padStart(2, '0');
      const minutes = String(today.getMinutes()).padStart(2, '0');
      const formattedTime = `${hours}:${minutes}`;

      // Store submission date and time in localStorage
      localStorage.setItem(`submission-date-${currentMaterial.value.id}`, formattedDate);
      localStorage.setItem(`submission-time-${currentMaterial.value.id}`, formattedTime);

      // If the material doesn't have submissionDate/submissionTime properties, add them
      if (currentMaterial.value && !currentMaterial.value.submissionDate) {
        // Update the current material in memory
        currentMaterial.value.submissionDate = today.toISOString().split('T')[0];
        currentMaterial.value.submissionTime = formattedTime;

        // Sync the material data with the class to ensure data consistency
        classStore.syncMaterialWithClass(
          currentClass.value.id,
          currentMaterial.value.id,
          {
            submissionDate: today.toISOString().split('T')[0],
            submissionTime: formattedTime
          }
        );
      }

      // Show the task complete dialog
      showTaskCompleteDialog.value = true;

      // NOTE: Automatic mentor grading simulation has been disabled
      // This ensures task status remains as 'submitted' until manually changed by a mentor
      // In a real application, the mentor would review the submission and update the status
      console.log('Task submitted successfully. Status will remain as "submitted" until reviewed by mentor.');

      // The dialog will auto-close after 1 second
    } else {
      errorMessage.value = 'Failed to submit task. Please try again.';
    }
  } catch (error) {
    console.error('Error during submission:', error);
    errorMessage.value = 'Failed to submit task. Please try again.';
  } finally {
    isSubmitting.value = false;
  }
}

// formatTaskStatus, getStatusClass, and formatDate are now imported from studentUtils.js

const source = computed(() => {
  return router.currentRoute.value.query.source || '';
});

// Check if we're coming from submission history
const isFromSubmission = computed(() => {
  return source.value === 'dialog_submission' || source.value === 'main_page_submission';
});

// Check if we're coming from main page (not dialog)
const isFromMainPage = computed(() => {
  return source.value === 'main_page_task' || source.value === 'main_page_submission';
});

// Check if we're coming from dialog
const isFromDialog = computed(() => {
  return source.value === 'dialog_task' || source.value === 'dialog_submission';
});

// We'll read the class source directly in the computed property
// This ensures we always have the latest value

// Initialize grade data when component is mounted
onMounted(() => {
  if (currentMaterial.value && currentMaterial.value.taskStatus === 'reviewed') {
    // Get submission date from material or localStorage
    let submissionDate;
    if (currentMaterial.value.submissionDate) {
      submissionDate = formatDate(currentMaterial.value.submissionDate);
    } else {
      submissionDate = localStorage.getItem(`submission-date-${currentMaterial.value.id}`);
      if (!submissionDate) {
        submissionDate = formatDate(currentMaterial.value.postedDate);
      }
    }

    // Get review date from material or localStorage
    let reviewDate;
    if (currentMaterial.value.reviewedDate) {
      reviewDate = formatDate(currentMaterial.value.reviewedDate);
    } else {
      reviewDate = localStorage.getItem(`grade-reviewed-date-${currentMaterial.value.id}`);
      if (!reviewDate) {
        // Generate a review date (2 days after submission)
        const submissionDateObj = new Date(submissionDate);
        const reviewDateObj = new Date(submissionDateObj);
        reviewDateObj.setDate(reviewDateObj.getDate() + 2);
        reviewDate = formatDate(reviewDateObj.toISOString().split('T')[0]);
      }
    }

    // Get task-specific score
    let score = 0;
    let maxScore = 100;

    // First check if the material has a taskScore property
    if (currentMaterial.value.taskScore !== undefined) {
      score = currentMaterial.value.taskScore;
      maxScore = currentMaterial.value.taskMaxScore || 100;
    }
    // Then try localStorage
    else {
      const savedScore = localStorage.getItem(`grade-score-${currentMaterial.value.id}`);
      if (savedScore) {
        score = parseInt(savedScore);
      }
      // If still not found, generate a score based on student data or a default
      else {
        // Get student data from the class
        const studentId = 1001; // This would normally come from the user's profile
        let studentData = null;

        if (currentClass.value && currentClass.value.students) {
          studentData = currentClass.value.students.find(s => s.id === studentId);
        }

        // Calculate score based on student data or default to a high score
        score = studentData ? studentData.mark : 95;

        // NOTE: Automatic score updating has been disabled
        // In a real app, this would be set by the mentor
        console.log('Score updating simulation has been disabled to maintain task status consistency');
      }
    }

    // Initialize grade data
    gradeData.value = {
      score: score,
      maxScore: maxScore,
      submittedOn: submissionDate,
      reviewedOn: reviewDate
    };
  }
});

const breadcrumbItems = computed(() => {
  // Always check localStorage directly to ensure we have the latest value
  const currentSource = localStorage.getItem(STORAGE_KEYS.CLASS_SOURCE) || 'studied';

  let firstBreadcrumbTitle = 'Classes Studied';
  let firstBreadcrumbPath = '/student/academy';

  // If class is completed or source is 'completed', show "Completed Classes"
  if (currentClass.value?.status === 'completed' || currentSource === 'completed') {
    firstBreadcrumbTitle = 'Completed Classes';
    firstBreadcrumbPath = {
      path: '/student/academy',
      query: { tab: 'completed' }
    };
  }

    const historyItem = {
    title: isFromSubmission.value ? 'Submission History' : 'Task History',
    path: `/student/class/${currentClass.value?.id || ''}`,
    onClick: () => {
      // If coming from main page, navigate back to DetailClass1 without query parameters
      if (isFromMainPage.value) {
        router.push({
          name: 'DetailClass',
          params: { classId: currentClass.value?.id || '' }
        });
      }
      // If coming from dialog, navigate back to DetailClass1 with dialog open
      else if (isFromDialog.value) {
        router.push({
          name: 'DetailClass',
          params: { classId: currentClass.value?.id || '' },
          query: isFromSubmission.value ? { showSubmissionHistory: true } : { showTaskHistory: true }
        });
      }
      // Default fallback
      else {
        router.push({
          name: 'DetailClass',
          params: { classId: currentClass.value?.id || '' }
        });
      }
    }
  };

  return [
    {
      title: firstBreadcrumbTitle,
      path: firstBreadcrumbPath
    },
    {
      title: currentClass.value?.title || 'Class',
      path: `/student/class/${currentClass.value?.id || ''}`
    },
    historyItem,
    {
      title: currentMaterial.value?.title || (isFromSubmission.value ? 'Submission Details' : 'Task Details'),
      active: true
    }
  ];
});
</script>
