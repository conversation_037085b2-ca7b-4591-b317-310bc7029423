<template>
  <div class="flex space-x-2">
    <input
      v-for="(digit, index) in digits"
      :key="index"
      type="tel"
      inputmode="numeric"
      pattern="[0-9]*"
      maxlength="1"
      class="w-12 h-12 text-center text-xl font-semibold border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#ff8c00] focus:border-[#ff8c00]"
      v-model="digits[index]"
      @input="onInput(index)"
      @keydown="onKeyDown($event, index)"
      @paste="onPaste"
      ref="inputs"
    />
  </div>
</template>

<script>
export default {
  name: 'VerificationInput',
  props: {
    length: {
      type: Number,
      default: 6
    },
    modelValue: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      digits: Array(this.length).fill('')
    }
  },
  methods: {
    onInput(index) {
      // Ensure only one digit is entered and it's a number
      if (this.digits[index]) {
        // Ensure digit is a string
        const digitStr = String(this.digits[index] || '');

        // Filter out non-numeric characters
        this.digits[index] = digitStr.replace(/[^0-9]/g, '');

        // Take only the first character if multiple were entered
        if (this.digits[index].length > 1) {
          this.digits[index] = this.digits[index].charAt(0);
        }
      }

      // Move to next input after entering a digit
      if (this.digits[index] && index < this.length - 1) {
        this.$nextTick(() => {
          this.$refs.inputs[index + 1].focus();
        });
      }

      // Emit the code
      this.emitCode();
    },
    onKeyDown(event, index) {
      // Handle backspace to move to previous input
      if (event.key === 'Backspace' && !this.digits[index] && index > 0) {
        this.$nextTick(() => {
          this.$refs.inputs[index - 1].focus();
        });
      }
    },
    onPaste(event) {
      event.preventDefault();

      try {
        // Safely get clipboard data
        const clipboardData = event.clipboardData || window.clipboardData;
        if (!clipboardData) return;

        // Get pasted text and ensure it's a string
        let pastedData = '';
        try {
          pastedData = String(clipboardData.getData('Text') || '').trim();
        } catch (e) {
          console.error('Error getting clipboard data:', e);
          return;
        }

        // Only process if the pasted data is numeric and not longer than our input length
        if (/^\d+$/.test(pastedData) && pastedData.length <= this.length) {
          for (let i = 0; i < pastedData.length; i++) {
            this.digits[i] = pastedData[i];
          }
          // Focus on the next empty input or the last one
          const nextIndex = Math.min(pastedData.length, this.length - 1);
          this.$nextTick(() => {
            this.$refs.inputs[nextIndex].focus();
          });
          this.emitCode();
        }
      } catch (error) {
        console.error('Error in paste handler:', error);
      }
    },
    emitCode() {
      // Filter out any non-numeric characters before joining
      const filteredDigits = this.digits.map(digit => {
        // Ensure digit is a string before calling replace
        const digitStr = String(digit || '');
        return digitStr.replace(/[^0-9]/g, '');
      });

      const code = filteredDigits.join('');
      console.log("Emitting code:", code, "Type:", typeof code, "Length:", code.length);

      // Use 'update:modelValue' instead of 'input' for Vue 3
      this.$emit('update:modelValue', code);
      // Keep 'input' for backward compatibility
      this.$emit('input', code);
    },
    reset() {
      this.digits = Array(this.length).fill('');
    }
  }
}
</script>

<style scoped>
/* Custom styles if needed */
</style>
