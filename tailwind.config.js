/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
    "./node_modules/flowbite/**/*.js"
  ],
  theme: {
    extend: {
      fontFamily: {
        'gabarito': ['Gabarito', 'sans-serif'],
      },
      colors: {
        'primary': '#ff8c00',
        'primary-hover': '#e67e00',
        'teal': '#006D77',
        'teal-dark': '#005a63',
        'orange': {
          DEFAULT: '#FF8C00',
          dark: '#FF7C00',
          '600': '#E67E00',
          '50': '#FFF8F0',
          '100': '#FFEBD9',
        },
        'success': '#10b981',
        'warning': '#f59e0b',
        'info': '#3b82f6',
      },
      animation: {
        'pulse-once': 'pulse 1s ease-in-out 1',
        'subtle-pulse': 'subtle-pulse 1.5s ease-in-out',
        'ripple': 'ripple 0.6s cubic-bezier(0.22, 0.61, 0.36, 1) forwards',
      },
      keyframes: {
        'subtle-pulse': {
          '0%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(1.05)' },
          '100%': { transform: 'scale(1)' }
        },
        'ripple': {
          '0%': { transform: 'scale(0)', opacity: '0.7' },
          '50%': { opacity: '0.4' },
          '100%': { transform: 'scale(4)', opacity: '0' }
        }
      }
    },
  },
  plugins: [
    require('flowbite/plugin'),
    require('@tailwindcss/forms')
  ],
}