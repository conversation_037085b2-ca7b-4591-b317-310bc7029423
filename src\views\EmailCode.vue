<template>
  <div class="flex flex-col items-center justify-start min-h-screen bg-gray-50 overflow-x-hidden">
    <!-- Header with logo - fixed width container -->
    <div class="w-full border-b border-gray-300 py-4">
      <div class="container mx-auto px-4">
        <router-link to="/" class="flex items-center">
          <img src="/logo.png" alt="FlowCamp Logo" class="h-10" />
          <span class="ml-2 text-[#ff8c00] font-bold text-xl">Flow Camp</span>
        </router-link>
      </div>
    </div>

    <!-- Main content container - centered with max width -->
    <div class="container mx-auto flex justify-center px-4 py-8">
      <div class="w-full max-w-md">
        <!-- Lock icon in circle -->
        <div class="flex justify-center mb-6">
          <div class="w-20 h-20 rounded-full bg-gray-100 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
        </div>

        <!-- Form content -->
        <div class="w-full">
          <h2 class="text-3xl font-bold text-center mb-2">Email Verification</h2>
          <p class="text-center text-gray-600 mb-6">Enter your email and we'll send you a verification code</p>

          <div v-if="formError" class="mb-4 p-3 bg-red-100 text-red-700 rounded-md text-sm">
            {{ formError }}
          </div>

          <form @submit.prevent="submitForm">
            <div class="mb-4">
              <input
                type="email"
                placeholder="Email"
                v-model="email"
                :class="{'border-red-500': errors.email}"
                class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
              <p v-if="errors.email" class="text-red-500 text-xs mt-1">{{ errors.email }}</p>
            </div>

            <div class="w-full flex justify-center">
              <button
                type="submit"
                class="w-full flex items-center justify-center px-4 py-3 bg-[#ff8c00] text-white font-semibold rounded-md hover:bg-[#ff7f00] focus:outline-none transition duration-200 ease-in-out"
              >
                Send Verification Code
              </button>
            </div>
          </form>

          <div class="text-center mt-6 mb-8">
            <span class="text-sm text-gray-600">
              Remember your password?
              <router-link to="/login" style="color: #ff8c00;" class="font-medium hover:underline">Sign In</router-link>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer with copyright only -->
    <div class="w-full mt-auto border-t border-gray-200">
      <div class="container mx-auto px-4 py-4 flex justify-center items-center">
        <Copyright />
      </div>
    </div>
  </div>
</template>

<script>
import Copyright from "@/components/Copyright.vue";

export default {
  name: "EmailCode",
  components: {
    Copyright
  },
  data() {
    return {
      email: "",
      formError: "",
      errors: {
        email: ""
      }
    };
  },
  methods: {
    validateForm() {
      let isValid = true;

      this.formError = "";
      this.errors = {
        email: ""
      };

      if (!this.email.trim()) {
        this.errors.email = "Email is required";
        isValid = false;
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.email)) {
        this.errors.email = "Please enter a valid email address";
        isValid = false;
      }

      if (!isValid) {
        this.formError = "Please enter a valid email address";
      }

      return isValid;
    },

    submitForm() {
      if (this.validateForm()) {
        // Form is valid, proceed with submission
        console.log("Form submitted successfully");

        // Here you would typically make an API call to send the reset code
        // For demo purposes, redirect to the email verification page
        this.$router.push({
          name: 'email-verification',
          query: {
            email: this.email,
            source: 'forgot-password'
          }
        });
      } else {
        console.log("Form validation failed");
      }
    }
  }
};
</script>

<style scoped>
/* No custom styles needed */
</style>
