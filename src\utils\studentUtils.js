/**
 * Student Flow Utility Functions
 * 
 * This file contains utility functions used across the student flow components.
 * Centralizing these functions reduces code duplication and ensures consistent behavior.
 */

/**
 * Format a date string to a more readable format
 * @param {string} dateString - Date string in YYYY-MM-DD format
 * @returns {string} Formatted date string
 */
export function formatDate(dateString) {
  if (!dateString) return '';
  
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString(undefined, options);
}

/**
 * Format a time string to a more readable format
 * @param {string} timeString - Time string in HH:MM format
 * @returns {string} Formatted time string
 */
export function formatTime(timeString) {
  if (!timeString) return '';
  
  // If the time already includes AM/PM, return as is
  if (timeString.includes('AM') || timeString.includes('PM')) {
    return timeString;
  }
  
  // Otherwise, format it
  const [hours, minutes] = timeString.split(':');
  const hour = parseInt(hours, 10);
  const ampm = hour >= 12 ? 'PM' : 'AM';
  const formattedHour = hour % 12 || 12;
  
  return `${formattedHour}:${minutes} ${ampm}`;
}

/**
 * Get CSS class for a task status
 * @param {string} status - Task status
 * @returns {string} CSS class for the status
 */
export function getStatusClass(status) {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'past_due':
      return 'bg-red-100 text-red-800';
    case 'submitted':
      return 'bg-blue-100 text-blue-800';
    case 'turned_in':
      return 'bg-green-100 text-green-800';
    case 'under_review':
      return 'bg-orange-100 text-orange-800';
    case 'reviewed':
      return 'bg-green-100 text-green-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

/**
 * Format a task status to a more readable format
 * @param {string} status - Task status
 * @returns {string} Formatted status
 */
export function formatTaskStatus(status) {
  switch (status) {
    case 'pending':
      return 'Pending';
    case 'past_due':
      return 'Past Due';
    case 'submitted':
      return 'Submitted';
    case 'turned_in':
      return 'Turned In';
    case 'under_review':
      return 'Under Review';
    case 'reviewed':
      return 'Reviewed';
    default:
      return status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ');
  }
}

/**
 * Handle student image loading error by using a fallback image
 * @param {Event} event - Image error event
 */
export function handleStudentImageError(event) {
  // Generate a random but consistent image based on the student's name
  const seed = event.target.alt.length % 99; // Use name length as a simple hash
  const gender = seed % 2 === 0 ? 'men' : 'women';

  // Use randomuser.me with a consistent seed for reproducibility
  event.target.src = `https://randomuser.me/api/portraits/${gender}/${seed}.jpg`;
}

/**
 * Sort tasks by due date
 * @param {Array} tasks - Array of tasks
 * @param {string} order - Sort order ('asc' or 'desc')
 * @returns {Array} Sorted tasks
 */
export function sortTasksByDueDate(tasks, order = 'asc') {
  return [...tasks].sort((a, b) => {
    const dateA = new Date(`${a.dueDate}T${a.dueTime}`);
    const dateB = new Date(`${b.dueDate}T${b.dueTime}`);
    return order === 'asc' ? dateA - dateB : dateB - dateA;
  });
}

/**
 * Sort tasks by submission date
 * @param {Array} tasks - Array of tasks
 * @param {string} order - Sort order ('asc' or 'desc')
 * @returns {Array} Sorted tasks
 */
export function sortTasksBySubmissionDate(tasks, order = 'desc') {
  return [...tasks].sort((a, b) => {
    const dateA = new Date(`${a.submissionDate || a.postedDate}T${a.submissionTime || a.postedTime || '00:00'}`);
    const dateB = new Date(`${b.submissionDate || b.postedDate}T${b.submissionTime || b.postedTime || '00:00'}`);
    return order === 'desc' ? dateB - dateA : dateA - dateB;
  });
}
