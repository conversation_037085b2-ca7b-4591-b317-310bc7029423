@import url('https://fonts.googleapis.com/css2?family=Gabarito:wght@400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Gabarito', system-ui, sans-serif;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-white text-gray-900 antialiased;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-gabarito font-bold;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg transition-all duration-300 font-medium;
  }

  .btn-primary {
    @apply bg-primary text-white hover:bg-primary-hover;
  }

  .btn-teal {
    @apply bg-teal text-white hover:bg-teal-dark;
  }

  .btn-orange {
    @apply bg-orange text-white hover:bg-orange-dark;
  }

  .card {
    @apply bg-white rounded-lg shadow-md transition-all duration-300;
  }

  .card-hover {
    @apply hover:-translate-y-1 hover:shadow-lg;
  }

  .badge {
    @apply px-2 py-1 rounded-md text-sm font-medium;
  }

  .badge-success {
    @apply bg-success/10 text-success;
  }

  .badge-warning {
    @apply bg-warning/10 text-warning;
  }

  .badge-info {
    @apply bg-info/10 text-info;
  }

  .input {
    @apply w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent;
  }
}

@layer utilities {
  .text-shadow {
    @apply [text-shadow:2px_2px_4px_rgba(0,0,0,0.1)];
  }

  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-teal;
  }

  /* Custom scrollbar utilities */
  .scrollbar-thin {
    @apply [scrollbar-width:thin];
  }

  .scrollbar-track-gray-100::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full hover:bg-gray-400;
  }

  .scrollbar-w-2::-webkit-scrollbar {
    @apply w-2;
  }

  /* Animation utilities */
  .fade-transition {
    @apply transition-opacity duration-300 ease-in-out;
  }

  .fade-enter-from {
    @apply opacity-0;
  }

  .fade-leave-to {
    @apply opacity-0;
  }
}

/* Container */
.container {
  @apply mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
}

/* Section spacing */
.section {
  @apply py-12 sm:py-16 lg:py-20;
}

/* Typography */
.heading-1 {
  @apply text-4xl sm:text-5xl font-bold;
}

.heading-2 {
  @apply text-3xl sm:text-4xl font-bold;
}

.heading-3 {
  @apply text-2xl sm:text-3xl font-bold;
}

.body-large {
  @apply text-lg leading-relaxed;
}

.body-base {
  @apply text-base leading-relaxed;
}

.body-small {
  @apply text-sm leading-relaxed;
}
