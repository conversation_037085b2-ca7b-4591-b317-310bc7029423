<template>
    <div>
      <!-- Hero Section -->
      <section class="bg-[#ff8c00] overflow-hidden relative min-h-[500px]">
        <!-- Background image with overlay -->
        <div v-if="useOverlay" class="absolute inset-0">
          <img
            :src="heroImage"
            alt="Hero background"
            class="absolute inset-0 w-full h-full object-cover"
            loading="eager"
            fetchpriority="high"
          />
          <div class="absolute inset-0 bg-gradient-to-l from-[#FF9A33]/20 to-black/90"></div>
        </div>
      <div class="container mx-auto py-12 relative px-4">
        <div class="flex flex-wrap items-center">
          <div :class="[
            'text-left relative',
            hideRightSection ? 'w-full' : 'lg:w-2/3 w-full'
          ]">
            <div class="inline-block relative mb-8">
              <!-- Yellow dots decoration -->
              <div class="absolute -top-2.5 -left-2.5 z-0">
                <div class="grid grid-cols-5 gap-1">
                  <div v-for="n in 25" :key="n" class="w-2 h-2 bg-yellow-400 rounded-full opacity-70"></div>
                </div>
              </div>

              <span class="relative z-10 bg-yellow-400 text-black font-medium px-4 py-2 rounded-lg text-base">
                {{ badgeText }}
              </span>
            </div>

            <h1 class="text-2xl font-bold mb-3" :class="isDark ? 'text-white' : 'text-gray-900'">
              {{ titleText }}
            </h1>

            <p class="text-4xl font-bold mb-3 text-white">
              {{ subtitleText }}
            </p>

            <p class="text-xl font-bold mb-6" :class="isDark ? 'text-white' : 'text-[#444444]'">
              {{ descriptionText }}
            </p>

            <div class="flex gap-4">
              <router-link
                v-if="showGetStarted"
                to="/register"
                class="bg-[#00796B] text-white rounded-lg px-6 py-3 inline-flex items-center gap-2 hover:bg-[#00695C] transition-colors"
              >
                Get Started
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16" class="bi bi-arrow-right">
                  <path fill-rule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"/>
                </svg>
              </router-link>

              <a href="https://wa.me/your_number" class="bg-[#00796B] text-white rounded-lg px-6 py-3 inline-flex items-center gap-2 hover:bg-[#00695C] transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-whatsapp" viewBox="0 0 16 16">
                  <path d="M13.601 2.326A7.854 7.854 0 0 0 7.994 0C3.627 0 .068 3.558.064 7.926c0 1.399.366 2.76 1.057 3.965L0 16l4.204-1.102a7.933 7.933 0 0 0 3.79.965h.004c4.368 0 7.926-3.558 7.93-7.93A7.898 7.898 0 0 0 13.6 2.326zM7.994 14.521a6.573 6.573 0 0 1-3.356-.92l-.24-.144-2.494.654.666-2.433-.156-.251a6.56 6.56 0 0 1-1.007-3.505c0-3.626 2.957-6.584 6.591-6.584a6.56 6.56 0 0 1 4.66 1.931 6.557 6.557 0 0 1 1.928 4.66c-.004 3.639-2.961 6.592-6.592 6.592zm3.615-4.934c-.197-.099-1.17-.578-1.353-.646-.182-.065-.315-.099-.445.099-.133.197-.513.646-.627.775-.114.133-.232.148-.43.05-.197-.1-.836-.308-1.592-.985-.59-.525-.985-1.175-1.103-1.372-.114-.198-.011-.304.088-.403.087-.088.197-.232.296-.346.1-.114.133-.198.198-.33.065-.134.034-.248-.015-.347-.05-.099-.445-1.076-.612-1.47-.16-.389-.323-.335-.445-.34-.114-.007-.247-.007-.38-.007a.729.729 0 0 0-.529.247c-.182.198-.691.677-.691 1.654 0 .977.71 1.916.81 2.049.098.133 1.394 2.132 3.383 2.992.47.205.84.326 1.129.418.475.152.904.129 1.246.08.38-.058 1.171-.48 1.338-.943.164-.464.164-.86.114-.943-.049-.084-.182-.133-.38-.232z"/>
                </svg>
                {{whatsappText}}
              </a>
            </div>
          </div>

          <div v-if="!hideRightSection" class="lg:w-1/3 w-full text-right relative mt-8 lg:mt-0">
            <div class="relative w-full lg:w-4/5 ml-auto">
              <!-- Border hijau -->
              <div class="absolute top-4 right-4 w-full h-full border-2 border-teal-600 rounded-3xl z-0"></div>

              <div class="relative w-full pb-[100%] overflow-hidden rounded-3xl bg-gray-100 z-10">
                <img
                  :src="heroImage"
                  :alt="'Hero image'"
                  class="absolute top-0 left-0 w-full h-full object-cover"
                  @error="handleImageError"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    </div>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  badgeText: {
    type: String,
    required: true
  },
  titleText: {
    type: String,
    required: true
  },
  subtitleText: {
    type: String,
    required: true
  },
  descriptionText: {
    type: String,
    required: true
  },
  whatsappText: {
    type: String,
  },
  heroImage: {
    type: String,
    required: true
  },
  useOverlay: {
    type: Boolean,
    default: false
  },
  isDark: {
    type: Boolean,
    default: false
  },
  hideRightSection: {
    type: Boolean,
    default: false
  },
  className: {
    type: String,
    default: ''
  },
  isDetailPage: {
    type: Boolean,
    default: false
  },
  showGetStarted: {
    type: Boolean,
    default: false
  }
});

// Fallback image jika gambar utama gagal dimuat
const fallbackImage = '/join.png';

const handleImageError = (e) => {
  console.warn('Hero image failed to load, using fallback');
  e.target.src = fallbackImage;
};
</script>