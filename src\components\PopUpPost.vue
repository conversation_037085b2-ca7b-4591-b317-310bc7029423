<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
    <div class="bg-white w-[360px] rounded-lg p-6 shadow-xl text-center space-y-4">
      <div class="flex justify-center">
        <div class="w-12 h-12 rounded-full bg-[#ff8c00] flex items-center justify-center">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
          </svg>
        </div>
      </div>
      <h2 class="text-lg font-semibold text-gray-800">Posted successfully!</h2>
      <p class="text-sm text-gray-500">Item has been posted</p>
    </div>
  </div>
</template>

<script setup>
// This is a passive popup (auto-hide from parent)
</script>

<style scoped>
/* Optional: Add a fade-in animation if desired */
</style>
