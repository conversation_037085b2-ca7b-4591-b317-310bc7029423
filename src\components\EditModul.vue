<template>
    <div v-if="visible" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
      <div class="bg-white rounded-md max-w-3xl w-full p-6 relative shadow-lg">
        <!-- Close button -->
        <button @click="$emit('close')" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-xl">&times;</button>

        <!-- Header -->
        <div class="mb-6 flex justify-between items-start">
          <h2 class="text-lg font-semibold flex items-center gap-2">
            <img src="/materialicon.png" class="w-5 h-5" />
            Material
          </h2>
          <h3 class="text-sm text-right text-gray-700 font-medium">{{ modulTitle }}</h3>
        </div>

        <!-- Select Modul -->
        <select v-model="form.title" class="w-full border-2 border-orange rounded-md px-3 py-2 mb-4 text-sm focus:outline-none focus:border-orange-dark focus:ring-1 focus:ring-orange">
          <option v-for="opt in moduleOptions" :key="opt" :value="opt">{{ opt }}</option>
        </select>

        <!-- Deskripsi -->
        <textarea
          v-model="form.description"
          rows="4"
          class="w-full border-2 border-orange rounded-md px-3 py-2 text-sm mb-4 focus:outline-none focus:border-orange-dark focus:ring-1 focus:ring-orange resize-none"
          placeholder="Deskripsi materi..."
        ></textarea>

        <!-- List Materi -->
        <div class="border-2 border-orange rounded-md px-4 py-3 flex items-center gap-3">
          <img src="/ppticon.png" class="w-6 h-6" />
          <p class="text-sm text-gray-800">{{ form.itemTitle }}</p>
        </div>

        <!-- Save button -->
        <div class="mt-6 flex justify-end">
          <button
            @click="save"
            class="bg-orange text-white px-6 py-2 rounded-md hover:bg-orange-dark transition-colors duration-200 text-sm"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  </template>

  <script setup>
  import { reactive, watch } from 'vue'
  import { ref } from 'vue'
  import EditSaved from '@/components/EditSaved.vue'

  const showEditSaved = ref(false)

  const showEditModal = ref(false)
const selectedModulData = ref({})
const selectedModulTitle = ref('')

const handleEditSave = (updatedData) => {
  console.log('Saved data:', updatedData)
  showEditSaved.value = true
}

  const props = defineProps({
    visible: Boolean,
    modulTitle: String,
    moduleOptions: {
      type: Array,
      default: () => []
    },
    initialData: {
      type: Object,
      default: () => ({ title: '', description: '', itemTitle: '' })
    }
  })

  // Emits
  const emit = defineEmits(['close', 'save'])

  // Form state
  const form = reactive({
    title: '',
    description: '',
    itemTitle: ''
  })

  // Sync prop data to form
  watch(
    () => props.initialData,
    (val) => {
      if (val) {
        form.title = val.title || ''
        form.description = val.description || ''
        form.itemTitle = val.itemTitle || ''
      }
    },
    { immediate: true }
  )

  // Save handler
  const save = () => {
    emit('save', { ...form })
    emit('close')
  }
  </script>
