<template>
  <div>
    <HeroSection
      badge-text="Free Class"
      title-text="Learn for Free, Grow Without Limits"
      subtitle-text="Join our Free Classes and gain tech skills in Front-End, Back-End, and Mobile App Development — no cost, just pure learning!"
      description-text="Ready to grow?"
      whatsappText="Try Free Consultation"
      :hero-image="bootcampHero"
    />

    <!-- CLASSES SECTION -->
    <section class="container mx-auto px-4 py-16 z-10 relative">
     <!-- SECTION TITLE -->
<h2 class="text-3xl md:text-4xl font-bold text-center text-gray-800 mb-8">
  Upcoming Free Classes
</h2>

<!-- CHECK FREE CLASSES -->
<div v-if="freePrograms.length === 0" class="flex flex-col items-center justify-center">
  <img src="/box.png" alt="No Classes Found" class="w-20 h-20 mb-4" />
  <p class="text-gray-500 text-lg">No Classes Found</p>
</div>

<!-- WHEN FREE PROGRAMS EXIST -->
<div v-else>
  <!-- FILTER BUTTONS -->
  <div class="flex justify-center gap-4 mb-12 flex-wrap">
    <button
      v-for="cat in programTypes"
      :key="cat.id"
      @click="selectedCategory = cat.id"
      :class="[
        'px-4 py-2 rounded-full font-semibold transition flex items-center gap-2',
        selectedCategory === cat.id
          ? 'bg-[#006D77] text-white'
          : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
      ]"
    >
      <i :class="cat.icon"></i>
      {{ cat.name }}
    </button>
  </div>

  <!-- CLASS CARDS -->
  <div v-if="filteredPrograms.length === 0" class="flex flex-col items-center justify-center">
    <img src="/box.png" alt="No Classes Found" class="w-20 h-20 mb-4" />
    <p class="text-gray-500 text-lg">No Classes Found</p>
  </div>

  <div v-else class="grid md:grid-cols-3 gap-8">
  <ClassCard
    v-for="program in mappedPrograms"
    :key="program.id"
    :classData="program"
  />
</div>


  <!-- SEE MORE BUTTON -->
  <div class="mt-10 flex justify-center">
    <button
      @click="navigateToSeeMore"
      class="bg-[#006D77] text-white px-6 py-3 rounded-full font-semibold hover:bg-[#005961] transition"
    >
      See More
    </button>
  </div>
</div>
    </section>

    <!-- SKILLS SECTION (tetap) -->
    <section class="relative bg-[#ff8c00] py-20">
      <div class="container mx-auto px-4 relative z-10">
        <div class="text-center mb-12">
          <h1 class="text-5xl font-bold text-white mb-6">
            Benefits You Will Get in This Free Class
          </h1>
          <p class="text-white text-lg max-w-2xl mx-auto">
            Join our free classes and gain valuable skills that will help you kickstart your tech career. Learn from industry experts and get hands-on experience with real-world projects.
          </p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- CARD 1 -->
          <div class="bg-white rounded-lg p-8 shadow-lg">
            <div class="flex justify-center -mt-16 mb-6">
              <div class="w-16 h-16 rounded-full bg-[#6C63FF] flex items-center justify-center">
                <img src="/invoice.png" alt="" class="h-8 w-8" />
              </div>
            </div>
            <h3 class="text-xl font-semibold text-[#006D77] text-center mb-4">
              Real-world Curriculum
            </h3>
            <p class="text-gray-600 text-center">
              Our curriculum is designed based on industry needs and best practices. You'll work on real projects that mirror actual workplace scenarios and challenges.
            </p>
          </div>

           <!-- CARD 2 -->
          <div class="bg-white rounded-lg p-8 shadow-lg">
            <div class="flex justify-center -mt-16 mb-6">
              <div class="w-16 h-16 rounded-full bg-[#F2720C] flex items-center justify-center">
                <img src="/certificate.png" alt="" class="h-8 w-8" />
              </div>
            </div>
            <h3 class="text-xl font-semibold text-[#006D77] text-center mb-4">
              Career Support & Certification
            </h3>
            <p class="text-gray-600 text-center">
              Get a certificate upon completion and access to our career support services, including resume reviews, interview preparation, and job placement assistance.
            </p>
          </div>

          <div class="bg-white rounded-lg p-8 shadow-lg">
            <div class="flex justify-center -mt-16 mb-6">
              <div class="w-16 h-16 rounded-full bg-[#00BCD4] flex items-center justify-center">
                <img src="/user.png" alt="" class="h-8 w-8" />
              </div>
            </div>
            <h3 class="text-xl font-semibold text-[#006D77] text-center mb-4">
              Expert Mentorship
            </h3>
            <p class="text-gray-600 text-center">
              Learn directly from industry experts who have years of experience. Get personalized guidance and feedback to help you grow as a developer.
            </p>
          </div>
        </div>

      </div>
    </section>

    <!-- REGISTRATION HELP SECTION -->
     <section class="relative bg-white py-20 overflow-hidden">
      <div class="relative container mx-auto px-4 flex flex-col md:flex-row items-center justify-between z-10">
        <div class="mb-10 md:mb-0 md:w-1/2 relative z-10">
          <h2 class="text-3xl sm:text-6xl font-extrabold text-black leading-tight mb-4">
          Having Problems During Registration?
          </h2>
          <a
            href="https://wa.me/your-number"
            target="_blank"
            class="inline-flex items-center gap-2 mt-4 bg-[#006D77] text-white px-6 py-3 rounded-md shadow hover:bg-[#005961] transition"
          >
            <span>📞</span> Contact Us
          </a>
        </div>
        <div class="md:w-1/2 flex justify-center relative z-10">
          <img src="/problem.png" alt="Having problems?" class="max-w-sm" />
        </div>
      </div>
    </section>

  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import HeroSection from '@/components/HeroSection.vue'
import { freeClasses as freePrograms, programTypes } from '@/data/programs.js'
import ClassCard from '@/components/ClassCard.vue'

const router = useRouter()
const bootcampHero = '/freeclass.png'

// Active category (default to first category)
const selectedCategory = ref(programTypes[0].id)

// Filter free programs by selected category
const filteredPrograms = computed(() =>
  freePrograms.filter(p => p.type === selectedCategory.value)
)

// Map programs to include necessary properties for ClassCard component
const mappedPrograms = computed(() =>
  filteredPrograms.value.slice(0, 3).map(p => ({
    ...p,
    // Use the image property if it exists, otherwise use mentorImage as fallback
    image: p.image || p.mentorImage,
    date: p.date || 'TBA',
    time: p.time || 'TBA',
  }))
)

// Navigate to the See More page
const navigateToSeeMore = () => {
  router.push('/freeClassSeeMore')
}

// Scroll to top when component is mounted
onMounted(() => {
  window.scrollTo({ top: 0, behavior: 'smooth' })
})
</script>
