<template>
  <StudentLayoutWrapper contentClass="bg-gray-50">
    <h1 class="text-2xl font-bold mb-6">Student Dashboard</h1>

      <!-- Dashboard Cards -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8 w-full mx-auto">
        <!-- Loading State for Dashboard Cards -->
        <template v-if="isLoading">
          <div v-for="i in 4" :key="i" class="bg-white p-4 rounded-lg shadow-sm border border-gray-100 animate-pulse">
            <div class="flex justify-between items-center">
              <div class="w-full">
                <div class="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                <div class="h-8 bg-gray-200 rounded w-16"></div>
              </div>
              <div class="w-10 h-10 rounded-full bg-gray-200 flex-shrink-0"></div>
            </div>
          </div>
        </template>

        <template v-else>
          <!-- Completed Classes Card -->
          <div
            class="bg-white p-4 rounded-lg shadow-sm border border-gray-100 transition-all duration-300 hover:shadow-md hover:border-purple-200"
            role="region"
            aria-label="Completed Classes"
          >
            <div class="flex justify-between items-center">
              <div>
                <p class="text-gray-500 text-sm font-medium">Completed Classes</p>
                <h2 class="text-3xl font-bold mt-1">{{ completedClassesCount }}</h2>
              </div>
              <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center z-10">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
                </svg>
              </div>
            </div>
          </div>

          <!-- My Overall Progress Card -->
          <div
            class="bg-white p-4 rounded-lg shadow-sm border border-gray-100 transition-all duration-300 hover:shadow-md hover:border-blue-200"
            role="region"
            aria-label="My Overall Progress"
          >
            <div class="flex justify-between items-center">
              <div>
                <p class="text-gray-500 text-sm font-medium">My Overall Progress</p>
                <h2 class="text-3xl font-bold mt-1">{{ overallProgress }}%</h2>
              </div>
              <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center z-10">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
          </div>

          <!-- Total Classes Card -->
          <div
            class="bg-white p-4 rounded-lg shadow-sm border border-gray-100 transition-all duration-300 hover:shadow-md hover:border-orange-200"
            role="region"
            aria-label="Total Classes"
          >
            <div class="flex justify-between items-center">
              <div>
                <p class="text-gray-500 text-sm font-medium">Total Classes</p>
                <h2 class="text-3xl font-bold mt-1">{{ totalClasses }}</h2>
              </div>
              <div class="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center z-10">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
            </div>
          </div>

          <!-- Completed Lessons Card -->
          <div
            class="bg-white p-4 rounded-lg shadow-sm border border-gray-100 transition-all duration-300 hover:shadow-md hover:border-green-200"
            role="region"
            aria-label="Completed Lessons"
          >
            <div class="flex justify-between items-center">
              <div>
                <p class="text-gray-500 text-sm font-medium">Completed Lessons</p>
                <h2 class="text-3xl font-bold mt-1">{{ completedLessonsCount }}</h2>
              </div>
              <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center z-10">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </div>
        </template>
      </div>

      <!-- Monthly Class Activity & Test Results -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8 w-full mx-auto">
        <!-- Monthly Class Activity Chart -->
        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100 transition-all duration-300 hover:shadow-md">
          <div class="flex justify-between items-center mb-4">
            <h3 class="font-semibold text-gray-800">Monthly Class Activity</h3>
            <div class="bg-gray-100 rounded-md px-3 py-1 text-xs font-medium">
              Monthly
            </div>
          </div>
          <p class="text-xs text-gray-500 mb-4">Activity over the past year</p>

          <!-- Chart Legend Pills (non-clickable) -->
          <div class="flex flex-wrap gap-2 mb-3">
            <div class="px-3 py-1 rounded-full text-xs bg-orange text-white inline-flex items-center" aria-label="My Active Classes indicator">
              <span class="w-2 h-2 bg-white rounded-full mr-1.5"></span>
              <span>My Active Classes</span>
            </div>
            <div class="px-3 py-1 rounded-full text-xs bg-purple-600 text-white inline-flex items-center" aria-label="My Learning Progress indicator">
              <span class="w-2 h-2 bg-white rounded-full mr-1.5"></span>
              <span>My Learning Progress</span>
            </div>
          </div>

          <!-- Chart Implementation with Loading and Empty States -->
          <div class="h-64 w-full relative" aria-label="Monthly class activity chart">
            <!-- Loading State -->
            <div v-if="isLoading" class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
              <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange"></div>
            </div>

            <!-- Empty State -->
            <div v-if="!isLoading && !hasChartData" class="absolute inset-0 flex flex-col items-center justify-center bg-white">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-300 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <p class="text-gray-500 font-medium">No activity data available</p>
              <p class="text-sm text-gray-400">Complete more classes to see your activity</p>
            </div>

            <canvas id="classActivityChart" class="w-full h-full"></canvas>
          </div>

          <!-- Chart Legend -->
          <div class="flex flex-wrap gap-4 mt-4 text-xs">
            <div class="flex items-center">
              <div class="w-3 h-3 rounded-full bg-orange mr-1.5"></div>
              <span>My Active Classes</span>
            </div>
            <div class="flex items-center">
              <div class="w-3 h-3 rounded-full bg-purple-600 mr-1.5"></div>
              <span>My Learning Progress</span>
            </div>
          </div>
        </div>

        <!-- Test Results -->
        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-100 transition-all duration-300 hover:shadow-md">
          <!-- Header Section with Stats -->
          <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
            <div class="flex items-center mb-4 sm:mb-0">
              <div class="mr-3">
                <h3 class="font-semibold text-gray-800 text-xl mb-1">Test Results</h3>
                <p class="text-sm text-gray-500">Track your performance across all classes</p>
              </div>
            </div>
            <div class="flex items-center space-x-4">
              <div class="bg-gray-50 px-4 py-2 rounded-lg">
                <div class="text-xs text-gray-500 mb-1">Total Tests</div>
                <div class="text-lg font-semibold text-gray-800">{{ testResults.length }}</div>
              </div>
              <div class="bg-gray-50 px-4 py-2 rounded-lg">
                <div class="text-xs text-gray-500 mb-1">Avg. Score</div>
                <div class="text-lg font-semibold text-gray-800">
                  {{ testResults.length > 0
                    ? (testResults.reduce((acc, curr) => acc + curr.score, 0) / testResults.length).toFixed(1)
                    : '0' }}
                </div>
              </div>
            </div>
          </div>          <div class="relative">
            <!-- Loading State -->
            <div v-if="isLoading" class="py-12 text-center">
              <div class="animate-spin rounded-full h-14 w-14 border-t-2 border-b-2 border-orange mx-auto mb-4"></div>
              <p class="text-gray-600 font-medium">Loading test results...</p>
              <p class="text-sm text-gray-500 mt-1">This won't take long</p>
            </div>

            <!-- Empty State -->
            <div v-else-if="testResults.length === 0" class="py-12 text-center bg-gray-50 rounded-lg">
              <div class="bg-white rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center shadow-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <p class="text-gray-700 font-medium text-lg mb-2">No test results available yet</p>
              <p class="text-sm text-gray-500">Complete tasks in your classes to see your results here</p>
            </div>

            <!-- Results Table with Scroll -->
            <div v-else class="relative">              <!-- Sticky Header -->
              <div class="sticky top-0 z-10 bg-gray-50 border-b border-gray-200">
                <table class="min-w-full">
                  <thead>
                    <tr>
                      <th class="w-1/2 px-6 py-3 text-left text-sm font-medium text-gray-600">Class/Material</th>
                      <th class="w-1/4 px-6 py-3 text-center text-sm font-medium text-gray-600">Score</th>
                      <th class="w-1/4 px-6 py-3 text-right text-sm font-medium text-gray-600">Submission</th>
                    </tr>
                  </thead>
                </table>
              </div>

              <!-- Scrollable Content -->
              <div
                ref="tableWrapper"
                class="overflow-y-auto max-h-[400px] scrollbar-thin scrollbar-thumb-orange scrollbar-track-gray-100 hover:scrollbar-thumb-orange-600"
              >
                <table class="min-w-full" aria-label="Test results table">
                  <tbody class="divide-y divide-gray-100">
                    <tr v-for="(result, index) in testResults" :key="index"
                        class="hover:bg-gray-50 transition-colors">
                      <td class="w-1/2 px-6 py-4">
                        <div class="flex flex-col">
                          <span class="font-medium text-gray-800 mb-0.5">{{ result.classTitle }}</span>
                          <span class="text-sm text-gray-500">{{ result.materialTitle }}</span>
                        </div>
                      </td>
                      <td class="w-1/4 px-6 py-4">
                        <div class="flex justify-center">
                          <div :class="getScoreColorClass(result.score)"
                               class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                            {{ result.score }}
                          </div>
                        </div>
                      </td>
                      <td class="w-1/4 px-6 py-4 text-right">
                        <div class="flex flex-col items-end">
                          <span class="text-sm text-gray-800">{{ result.date }}</span>
                          <span class="text-xs text-gray-500">{{ result.time }}</span>
                        </div>
                      </td>

                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- All Exams Section -->
      <div class="mb-8 w-full mx-auto">
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
          <h3 class="font-semibold text-gray-800 text-xl">All Exams</h3>
          <div class="flex flex-wrap gap-3 w-full sm:w-auto">
            <!-- Search Input with Accessibility -->
            <div class="relative flex-grow sm:flex-grow-0">
              <label for="exam-search" class="sr-only">Search exams</label>
              <input
                id="exam-search"
                v-model="searchQuery"
                type="text"
                placeholder="Search exams..."
                class="pl-9 pr-4 py-2 w-full rounded-md border border-gray-300 text-sm focus:ring-2 focus:ring-orange focus:border-orange transition-colors"
                aria-label="Search exams"
              >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400 absolute left-3 top-2.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>

            <!-- Status Filter Dropdown -->
            <div class="relative">
              <button
                @click="toggleStatusFilter"
                class="px-4 py-2 rounded-md border border-gray-300 text-sm flex items-center hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-orange focus:border-orange"
                aria-haspopup="true"
                :aria-expanded="statusFilterOpen"
              >
                <span>{{ statusFilter || 'Exam status' }}</span>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              <!-- Status Filter Dropdown Menu -->
              <div
                v-if="statusFilterOpen"
                class="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200"
                role="menu"
              >
                <div class="py-1">
                  <button
                    @click="setStatusFilter('')"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                    role="menuitem"
                  >
                    All Statuses
                  </button>
                  <button
                    @click="setStatusFilter('active')"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                    role="menuitem"
                  >
                    Active
                  </button>
                  <button
                    @click="setStatusFilter('completed')"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                    role="menuitem"
                  >
                    Completed
                  </button>
                  <button
                    @click="setStatusFilter('pending')"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                    role="menuitem"
                  >
                    Pending
                  </button>
                  <button
                    @click="setStatusFilter('past_due')"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                    role="menuitem"
                  >
                    Past Due
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Loading State for Exams -->
        <div v-if="isLoading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
          <div v-for="i in 3" :key="i" class="bg-white rounded-lg shadow-sm border border-gray-100 animate-pulse p-6">
            <div class="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div class="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
            <div class="h-4 bg-gray-200 rounded w-2/3 mb-4"></div>
            <div class="flex gap-2 mb-4">
              <div class="h-6 bg-gray-200 rounded w-16"></div>
              <div class="h-6 bg-gray-200 rounded w-20"></div>
            </div>
            <div class="flex justify-between items-center">
              <div class="h-8 bg-gray-200 rounded w-24"></div>
              <div class="h-4 bg-gray-200 rounded-full w-16"></div>
            </div>
          </div>
        </div>

        <!-- Empty State for Exams -->
        <div v-else-if="filteredExams.length === 0" class="py-12 text-center bg-white rounded-lg shadow-sm mb-4">
          <div class="bg-gray-50 rounded-full w-24 h-24 mx-auto mb-4 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
          <h4 class="text-lg font-medium text-gray-700 mb-2">No exams found</h4>
          <p class="text-gray-500 max-w-md mx-auto">
            {{ searchQuery ? 'No exams match your search criteria.' : 'You haven\'t been assigned any exams yet.' }}
          </p>
        </div>

        <!-- Exam Cards -->
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6 w-full mx-auto">
          <div
            v-for="(exam, index) in filteredExams"
            :key="index"
            class="bg-white rounded-lg shadow-sm border border-gray-200 transition-all duration-300 hover:shadow-md hover:border-orange-200 hover:-translate-y-1 p-6"
          >
            <!-- Header with title and status -->
            <div class="flex justify-between items-start mb-4">
              <div class="flex-1 mr-3">
                <h3 class="text-lg font-bold text-gray-900 leading-tight mb-1">{{ exam.title }}</h3>
                <p class="text-sm text-gray-600 font-medium">{{ exam.category }}</p>
              </div>
              <span
                :class="{
                  'px-3 py-1.5 rounded-full text-xs font-semibold flex-shrink-0 uppercase tracking-wide': true,
                  'bg-green-100 text-green-700 border border-green-200': exam.status === 'completed',
                  'bg-orange-100 text-orange-700 border border-orange-200': exam.status === 'active',
                  'bg-red-100 text-red-700 border border-red-200': exam.status === 'past_due',
                  'bg-yellow-100 text-yellow-700 border border-yellow-200': exam.status === 'pending'
                }"
                :aria-label="`Status: ${exam.status}`"
              >
                {{ formatStatus(exam.status) }}
              </span>
            </div>

            <!-- Description -->
            <p v-if="exam.description" class="text-gray-600 text-sm mb-4 line-clamp-2 leading-relaxed">
              {{ exam.description }}
            </p>

            <!-- Exam details -->
            <div class="flex flex-wrap gap-2 mb-6">
              <span class="inline-flex items-center text-xs bg-gray-50 text-gray-700 px-3 py-1.5 rounded-md border border-gray-200 font-medium">
                <svg class="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                </svg>
                {{ exam.category }}
              </span>
              <span v-if="exam.questions" class="inline-flex items-center text-xs bg-orange-50 text-orange-700 px-3 py-1.5 rounded-md border border-orange-200 font-medium">
                <svg class="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                </svg>
                {{ exam.questions }}
              </span>
              <span v-if="exam.points" class="inline-flex items-center text-xs bg-blue-50 text-blue-700 px-3 py-1.5 rounded-md border border-blue-200 font-medium">
                <svg class="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                {{ exam.points }}
              </span>
            </div>

            <!-- Date info and action button -->
            <div class="flex flex-col gap-3 mb-4">
              <div class="flex items-center justify-between text-xs text-gray-500">
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <span class="font-medium">{{ exam.createdDate }}</span>
                </div>
                <div v-if="exam.dueDate" class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span class="font-medium">{{ exam.dueDate }}</span>
                </div>
              </div>
            </div>

            <!-- Action button -->
            <button
              class="w-full px-4 py-2.5 bg-orange-600 text-white rounded-lg text-sm font-semibold hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200 transform"
              :aria-label="`See details for ${exam.title}`"
            >
              See details
            </button>
          </div>
        </div>

        <!-- Pagination -->
        <div v-if="totalPages > 1" class="flex flex-wrap justify-center items-center gap-2 w-full mx-auto">
          <button
            @click="prevPage"
            :disabled="currentPage === 1"
            :class="{
              'px-4 py-2 rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-orange focus:ring-offset-1': true,
              'bg-orange text-white hover:bg-orange-600': currentPage !== 1,
              'bg-gray-100 text-gray-400 cursor-not-allowed': currentPage === 1
            }"
            aria-label="Previous page"
          >
            <span class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
              Previous
            </span>
          </button>

          <div class="flex flex-wrap gap-1">
            <button
              v-for="page in paginationRange"
              :key="page"
              @click="goToPage(page)"
              :class="{
                'w-9 h-9 flex items-center justify-center rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-orange focus:ring-offset-1': true,
                'bg-orange text-white': currentPage === page,
                'bg-gray-100 text-gray-600 hover:bg-gray-200': currentPage !== page && page !== '...',
                'bg-gray-50 text-gray-400 cursor-default': page === '...'
              }"
              :disabled="page === '...'"
              :aria-label="page === '...' ? 'More pages' : `Go to page ${page}`"
              :aria-current="currentPage === page ? 'page' : undefined"
            >
              {{ page }}
            </button>
          </div>

          <button
            @click="nextPage"
            :disabled="currentPage === totalPages"
            :class="{
              'px-4 py-2 rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-orange focus:ring-offset-1': true,
              'bg-orange text-white hover:bg-orange-600': currentPage !== totalPages,
              'bg-gray-100 text-gray-400 cursor-not-allowed': currentPage === totalPages
            }"
            aria-label="Next page"
          >
            <span class="flex items-center">
              Next
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </span>
          </button>
        </div>
      </div>

  </StudentLayoutWrapper>
</template>

<script setup>
import { ref, computed, onMounted, watch, onUnmounted, nextTick } from 'vue';
import StudentLayoutWrapper from '@/components/@student/StudentLayoutWrapper.vue';
import { useClassStore } from '@/data/availableClasses';
import Chart from 'chart.js/auto';

const classStore = useClassStore();

// Loading states
const isLoading = ref(true);
const isLoadingMore = ref(false);

// Dashboard statistics
const completedClassesCount = ref(0);
const overallProgress = ref(0);
const totalClasses = ref(0);
const completedLessonsCount = ref(0);

// Computed properties for class data
const classes = computed(() => {
  return classStore.classes?.value || [];
});

const completedClasses = computed(() => {
  return classes.value.filter(c => c.progress === 100);
});

// Count total lessons/materials across all classes
const totalLessons = computed(() => {
  return classes.value.reduce((total, cls) => {
    return total + (cls.materials?.length || 0);
  }, 0);
});

// Count completed lessons/materials across all classes
const completedLessons = computed(() => {
  return classes.value.reduce((total, cls) => {
    const completedMaterials = cls.materials?.filter(m => m.isComplete || (m.isRead && (!m.hasTask || (m.hasTask && m.taskStatus && m.taskStatus !== 'pending' && m.taskStatus !== 'past_due')))) || [];
    return total + completedMaterials.length;
  }, 0);
});

// Monthly class activity data for chart - generate based on class data
const monthlyClassData = computed(() => {
  const monthlyData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    activeClasses: Array(12).fill(0),
    learningProgress: Array(12).fill(0)
  };

  // Count active classes and track learning progress by month
  classes.value.forEach(cls => {
    // Use joinedDate instead of startDate (which doesn't exist in the data)
    const dateToUse = cls.joinedDate || cls.postedDate;

    if (dateToUse) {
      const classDate = new Date(dateToUse);
      const month = classDate.getMonth();

      // Count active classes - include all classes that aren't completed
      if (cls.status === 'active' || cls.status === 'ongoing' ||
          cls.status === 'available' || (cls.status === 'studied' && cls.progress < 100)) {

        monthlyData.activeClasses[month] += 1;
      }

      // Track learning progress (completed materials) by month
      if (cls.materials && cls.materials.length > 0) {
        const completedMaterials = cls.materials.filter(m =>
          m.isComplete || (m.isRead && m.taskStatus && m.taskStatus !== 'pending' && m.taskStatus !== 'past_due')
        ).length;

        // Calculate progress percentage for this class
        const progressPercentage = (completedMaterials / cls.materials.length) * 100;

        // Add to the monthly progress (we'll average it later)
        monthlyData.learningProgress[month] += progressPercentage;
      }
    }
  });

  // Calculate average progress for each month
  for (let i = 0; i < 12; i++) {
    if (monthlyData.activeClasses[i] > 0) {
      // Average the progress across active classes
      monthlyData.learningProgress[i] = Math.round(monthlyData.learningProgress[i] / monthlyData.activeClasses[i]);
    }
  }

  return monthlyData;
});

// Check if there's any chart data to display
const hasChartData = computed(() => {
  return monthlyClassData.value.activeClasses.some(value => value > 0) ||
         monthlyClassData.value.learningProgress.some(value => value > 0);
});

// Chart instance reference
const chartInstance = ref(null);

// Test results data - generate from class materials with tasks
const testResults = computed(() => {
  const results = [];
  const currentUserName = 'You'; // In a real app, this would come from user authentication

  // Extract test results from class materials with tasks
  classes.value.forEach(cls => {
    if (cls.materials) {
      cls.materials.forEach(material => {
        if (material.hasTask && material.taskStatus) {
          const score = material.taskScore || 0;

          results.push({
            name: currentUserName,
            avatar: '/prof.png',
            score: parseFloat(score.toFixed(1)),
            classTitle: cls.title,
            materialTitle: material.title,
            date: material.submissionDate || material.postedDate,
            time: material.submissionTime || material.postedTime
          });
        }
      });
    }
  });

  // Sort by score in descending order
  return results.sort((a, b) => b.score - a.score);
});

// Number of test results to display
const displayedResultsCount = ref(5);
const showLoadMore = computed(() => testResults.value.length > displayedResultsCount.value);

// Sort test results by score (highest first) and apply limit
const sortedTestResults = computed(() => {
  return [...testResults.value]
    .sort((a, b) => b.score - a.score)
    .slice(0, displayedResultsCount.value);
});

// Exam data with search functionality and pagination
const currentPage = ref(1);
const itemsPerPage = 6;

const exams = computed(() => {
  const examsList = [];

  // Generate exams from class materials with tasks
  classes.value.forEach(cls => {
    if (cls.materials) {
      cls.materials.forEach(material => {
        if (material.hasTask) {
          // Convert all status values to standardized English
          let status = material.taskStatus || 'active';

          // Convert Indonesian terms to English
          if (status === 'Aktif') status = 'active';
          if (status === 'selesai') status = 'completed';
          if (status === 'tertunda') status = 'pending';
          if (status === 'terlambat') status = 'past_due';

          // Convert other status values to standardized format
          if (status === 'turned_in' || status === 'reviewed') status = 'completed';

          examsList.push({
            title: material.title || `${cls.title} - Task`,
            status: status,
            category: cls.category || 'General',
            questions: material.taskQuestions ? `${material.taskQuestions} Questions` : 'Practical Task',
            points: material.taskPoints ? `Total Points: ${material.taskPoints}` : null,
            createdDate: material.postedDate,
            dueDate: material.dueDate,
            description: material.description
          });
        }
      });
    }
  });

  return examsList;
});

// Status filter for exams
const statusFilter = ref('');
const statusFilterOpen = ref(false);

// Toggle status filter dropdown
const toggleStatusFilter = () => {
  statusFilterOpen.value = !statusFilterOpen.value;
};

// Set status filter
const setStatusFilter = (status) => {
  statusFilter.value = status;
  statusFilterOpen.value = false;
};

// Format status for display
const formatStatus = (status) => {
  if (!status) return '';

  // Convert to title case and replace underscores with spaces
  const formatted = status
    .replace(/_/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());

  return formatted;
};

// Search functionality for exams
const searchQuery = ref('');
const processedExams = computed(() => {
  return exams.value;
});

const filteredExams = computed(() => {
  const query = searchQuery.value.toLowerCase();
  const status = statusFilter.value.toLowerCase();

  // Filter by search query and status
  const filtered = processedExams.value.filter(exam => {
    const matchesQuery = exam.title.toLowerCase().includes(query) ||
                         exam.category.toLowerCase().includes(query);
    const matchesStatus = !status || exam.status.toLowerCase() === status;

    return matchesQuery && matchesStatus;
  });

  // Calculate total pages
  const totalPages = Math.ceil(filtered.length / itemsPerPage);
  if (currentPage.value > totalPages) {
    currentPage.value = Math.max(1, totalPages);
  }

  // Get paginated results
  const startIndex = (currentPage.value - 1) * itemsPerPage;
  return filtered.slice(startIndex, startIndex + itemsPerPage);
});

// Pagination controls
const totalPages = computed(() => {
  const query = searchQuery.value.toLowerCase();
  const status = statusFilter.value.toLowerCase();

  const filtered = processedExams.value.filter(exam => {
    const matchesQuery = exam.title.toLowerCase().includes(query) ||
                         exam.category.toLowerCase().includes(query);
    const matchesStatus = !status || exam.status.toLowerCase() === status;

    return matchesQuery && matchesStatus;
  });

  return Math.ceil(filtered.length / itemsPerPage);
});

// Create a pagination range with ellipsis for many pages
const paginationRange = computed(() => {
  const total = totalPages.value;
  const current = currentPage.value;
  const range = [];

  if (total <= 7) {
    // Show all pages if 7 or fewer
    for (let i = 1; i <= total; i++) {
      range.push(i);
    }
  } else {
    // Always show first page
    range.push(1);

    // Calculate start and end of range around current page
    let start = Math.max(2, current - 1);
    let end = Math.min(total - 1, current + 1);

    // Adjust if at the beginning
    if (current <= 3) {
      end = 4;
    }

    // Adjust if at the end
    if (current >= total - 2) {
      start = total - 3;
    }

    // Add ellipsis before range if needed
    if (start > 2) {
      range.push('...');
    }

    // Add range around current page
    for (let i = start; i <= end; i++) {
      range.push(i);
    }

    // Add ellipsis after range if needed
    if (end < total - 1) {
      range.push('...');
    }

    // Always show last page
    range.push(total);
  }

  return range;
});

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const goToPage = (page) => {
  currentPage.value = page;
};

// Function to initialize chart
const initializeChart = () => {
  const chartElement = document.getElementById('classActivityChart');
  if (!chartElement) {
    return;
  }

  // Ensure we have some data to display
  const hasActiveClassData = monthlyClassData.value.activeClasses.some(value => value > 0);
  const hasLearningProgressData = monthlyClassData.value.learningProgress.some(value => value > 0);

  // Create empty state data if no real data is available
  let chartData = { ...monthlyClassData.value };
  if (!hasActiveClassData && !hasLearningProgressData) {
    // Use empty arrays instead of sample data for proper empty state
    chartData = {
      ...chartData,
      activeClasses: Array(12).fill(0),
      learningProgress: Array(12).fill(0)
    };
  }

  // Destroy existing chart if it exists
  if (chartInstance.value) {
    chartInstance.value.destroy();
  }

  const ctx = chartElement.getContext('2d');
  chartInstance.value = new Chart(ctx, {
    type: 'line',
    data: {
      labels: chartData.labels,
      datasets: [
        {
          label: 'My Active Classes',
          data: chartData.activeClasses,
          borderColor: '#F2720C', // Orange color
          backgroundColor: 'rgba(242, 114, 12, 0.1)',
          tension: 0.4,
          fill: true,
          borderWidth: 2,
          pointBackgroundColor: '#F2720C',
          pointRadius: 3,
          pointHoverRadius: 5
        },
        {
          label: 'My Learning Progress',
          data: chartData.learningProgress,
          borderColor: '#9333ea', // Purple color
          backgroundColor: 'rgba(147, 51, 234, 0.1)',
          tension: 0.4,
          fill: true,
          borderWidth: 2,
          pointBackgroundColor: '#9333ea',
          pointRadius: 3,
          pointHoverRadius: 5
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          mode: 'index',
          intersect: false,
          callbacks: {
            title: function(tooltipItems) {
              return tooltipItems[0].label + ' ' + new Date().getFullYear();
            },
            label: function(context) {
              let label = context.dataset.label || '';
              if (label) {
                label += ': ';
              }
              if (context.parsed.y !== null) {
                label += context.parsed.y;
              }
              return label;
            }
          }
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          grid: {
            color: 'rgba(0, 0, 0, 0.05)'
          },
          ticks: {
            precision: 0, // Show only integer values
            font: {
              size: 10
            }
          }
        },
        x: {
          grid: {
            display: false
          },
          ticks: {
            font: {
              size: 10
            }
          }
        }
      },
      interaction: {
        mode: 'index',
        intersect: false
      }
    }
  });
};

// Function to get score color class with standardized percentage thresholds
const getScoreColorClass = (score) => {
  if (score >= 90) return 'bg-green-100 text-green-600';
  if (score >= 75) return 'bg-orange-100 text-orange';
  if (score >= 60) return 'bg-blue-100 text-blue-600';
  return 'bg-yellow-100 text-yellow-600';
};

// Function to load more test results with loading state
const loadMoreResults = () => {
  isLoadingMore.value = true;

  // Simulate loading delay
  setTimeout(() => {
    displayedResultsCount.value += 5;
    isLoadingMore.value = false;
  }, 500);
};

// Close status filter dropdown when clicking outside
const closeStatusFilterOnClickOutside = (event) => {
  if (statusFilterOpen.value && !event.target.closest('.relative')) {
    statusFilterOpen.value = false;
  }
};

// Add event listener for click outside
onMounted(() => {
  // Show loading state
  isLoading.value = true;

  // Simulate data loading
  setTimeout(() => {
    // Update statistics based on actual data
    completedClassesCount.value = completedClasses.value.length;
    totalClasses.value = classes.value.length;
    completedLessonsCount.value = completedLessons.value;

    // Calculate overall progress based on completed lessons vs total lessons
    if (totalLessons.value > 0) {
      overallProgress.value = Math.round((completedLessons.value / totalLessons.value) * 100);
    } else {
      overallProgress.value = 0;
    }

    // Initialize chart after DOM is ready
    initializeChart();

    // Hide loading state
    isLoading.value = false;

    // Add click event listener for closing status filter dropdown
    document.addEventListener('click', closeStatusFilterOnClickOutside);
  }, 800);
});

// Clean up event listeners
onUnmounted(() => {
  document.removeEventListener('click', closeStatusFilterOnClickOutside);

  // Destroy chart instance if it exists
  if (chartInstance.value) {
    chartInstance.value.destroy();
  }
});

// This function is already defined above as processedExams

// Watch for changes in class data and update statistics
watch([classes, completedClasses, completedLessons, totalLessons], () => {
  // Update statistics
  completedClassesCount.value = completedClasses.value.length;
  totalClasses.value = classes.value.length;
  completedLessonsCount.value = completedLessons.value;

  // Calculate overall progress based on completed lessons vs total lessons
  if (totalLessons.value > 0) {
    overallProgress.value = Math.round((completedLessons.value / totalLessons.value) * 100);
  } else {
    overallProgress.value = 0;
  }

  // Update chart when class data changes
  if (chartInstance.value) {
    // Ensure we have some data to display
    const hasActiveClassData = monthlyClassData.value.activeClasses.some(value => value > 0);
    const hasLearningProgressData = monthlyClassData.value.learningProgress.some(value => value > 0);

    // Use actual data or empty arrays
    const activeClasses = hasActiveClassData ? monthlyClassData.value.activeClasses : Array(12).fill(0);
    const learningProgress = hasLearningProgressData ? monthlyClassData.value.learningProgress : Array(12).fill(0);

    chartInstance.value.data.datasets[0].data = activeClasses;
    chartInstance.value.data.datasets[1].data = learningProgress;
    chartInstance.value.update();
  } else {
    setTimeout(() => {
      initializeChart();
    }, 100);
  }
});
</script>