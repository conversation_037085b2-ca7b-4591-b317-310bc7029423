<script setup>
import { useRoute } from 'vue-router'
const route = useRoute()
const classTitle = route.params.title || 'Unknown Class'

// Dummy data
const modules = [
  'Pengenalan Golang & Setup',
  'Struktur Data dalam Golang',
  'Penerapan OOP dalam Golang',
  'Penerapan OOP dalam Golang',
  'Penerapan OOP dalam Golang'
]

const ratingBars = [80, 45, 20, 5, 1] // Representasi persentase per bintang
</script>

<template>
  <div class="max-w-6xl mx-auto px-6 py-8 space-y-8">
    <!-- Breadcrumb -->
    <div class="text-sm text-gray-500 flex items-center gap-2">
      <router-link to="/AllClasses" class="text-primary hover:underline font-medium">Class Camp</router-link>
      <span>›</span>
      <span class="text-gray-700 font-medium">{{ classTitle }}</span>
    </div>

    <!-- Congratulations Box -->
    <div class="border border-gray-300 rounded-lg px-6 py-4 flex items-start gap-3 bg-white shadow-sm">
      <img src="/trophy.png" class="w-6 h-6 mt-1" />
      <div>
        <p class="text-primary font-semibold">🎉 Congratulations, Your class is complete!</p>
        <p class="text-sm text-gray-600 mt-1">Well done on guiding your students to the finish line and helping them succeed</p>
      </div>
    </div>

    <!-- 2-Column Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Modul Card -->
      <div class="border border-gray-300 rounded-lg bg-white p-6 shadow-sm flex flex-col">
        <div class="flex items-center gap-2 mb-2">
          <img src="/folder.png" class="w-5 h-5" />
          <h3 class="font-semibold text-lg">Modul</h3>
        </div>
        <p class="text-sm text-gray-500 mb-4">Access all completed structured modules</p>

        <div class="space-y-3 flex-1">
          <div
            v-for="(modul, index) in modules"
            :key="index"
            class="bg-gray-100 px-4 py-2 rounded shadow-sm text-sm text-gray-800"
          >
            {{ modul }}
          </div>
        </div>
        <div class="text-right mt-6">
  <router-link
    :to="{ name: 'DetailModul', params: { title: classTitle } }"
    class="inline-block bg-primary hover:bg-primary-hover text-white px-4 py-2 rounded-lg text-sm font-semibold"
  >
    See More
  </router-link>
</div>
</div>


      <!-- Review & Rating Card -->
      <div class="border border-gray-300 rounded-lg bg-white p-6 shadow-sm flex flex-col">
        <div class="flex items-center gap-2 mb-2">
          <img src="/comment.png" class="w-5 h-5" />
          <h3 class="font-semibold text-lg">Review & Rating</h3>
        </div>
        <p class="text-sm text-gray-500 mb-4">See all ratings and reviews from your students</p>

        <div class="flex items-center justify-between mb-2">
          <div class="flex text-yellow-400 text-xl gap-1">★ ★ ★ ★ ★</div>
          <div class="text-3xl font-bold text-gray-800">4.5<span class="text-gray-400 text-xl">/5</span></div>
        </div>
        <p class="text-sm text-gray-500 mb-4">576 reviews from 21.342 ratings</p>

        <!-- Rating bar chart -->
        <div class="space-y-2 text-sm text-gray-700">
          <div v-for="i in 5" :key="i" class="flex items-center gap-2">
            <span class="w-4">{{ 6 - i }}</span>
            <div class="flex-1 h-2 bg-gray-200 rounded overflow-hidden">
              <div
                class="h-full bg-yellow-400"
                :class="{
                  'w-[80%]': ratingBars[5 - i] === 80,
                  'w-[45%]': ratingBars[5 - i] === 45,
                  'w-[20%]': ratingBars[5 - i] === 20,
                  'w-[5%]': ratingBars[5 - i] === 5,
                  'w-[1%]': ratingBars[5 - i] === 1
                }"
              ></div>
            </div>
          </div>
        </div>

        <div class="text-right mt-6">
  <router-link
    :to="{ name: 'DetailRating', params: { title: classTitle } }"
    class="inline-block bg-primary hover:bg-primary-hover text-white px-4 py-2 rounded-lg text-sm font-semibold"
  >
    See More
  </router-link>
</div>


        </div>
      </div>
    </div>

</template>
