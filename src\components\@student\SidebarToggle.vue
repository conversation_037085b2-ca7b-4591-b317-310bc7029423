<template>
  <button
    @click="handleToggle"
    class="p-2.5 sm:p-2 rounded-full text-gray-600 hover:text-orange-500 hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-orange-300 active:scale-95 transition-all duration-300 shadow-md bg-white group relative overflow-hidden border border-gray-100 hover:border-orange-200"
    aria-label="Toggle sidebar"
  >
    <!-- Ripple effect on click -->
    <span
      v-if="showRipple"
      class="absolute inset-0 bg-orange-100 opacity-70 rounded-full transform scale-0 animate-ripple"
    ></span>

    <!-- Responsive icon - Menu icon for mobile, arrow for desktop -->
    <div class="relative z-10 flex items-center justify-center">
      <!-- Mobile menu icon (hamburger) - Enhanced for touch -->
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-6 w-6 transition-all duration-300 transform group-hover:scale-110 md:hidden"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        aria-hidden="true"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M4 6h16M4 12h16M4 18h16"
        />
      </svg>

      <!-- Desktop arrow icon -->
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-6 w-6 transition-all duration-300 transform group-hover:translate-x-0.5 group-hover:scale-110 hidden md:block"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        aria-hidden="true"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M9 5l7 7-7 7"
        />
      </svg>
    </div>
  </button>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['toggle']);
const showRipple = ref(false);

// Handle toggle with ripple effect
const handleToggle = () => {
  // Show ripple effect
  showRipple.value = true;

  // Hide ripple after animation completes
  setTimeout(() => {
    showRipple.value = false;
  }, 500);

  // Emit toggle event
  emit('toggle');
};
</script>


