<template>
  <div>
    <HeroSection
      badge-text="Free Class"
      title-text="Level Up Your Skills — Join Our Free Classes"
      subtitle-text="Explore tech topics for free! Learn from industry experts, sharpen your skills, and kickstart your journey."
      description-text="Ready to grow?"
      whatsappText="Try Free Consultation"
      :hero-image="headerImage"
      :use-overlay="true"
      :is-dark="true"
      :hide-right-section="true"
      class-name="text-white"
    />

    <div class="container mx-auto px-4 py-16">
      <h2 class="text-4xl font-bold text-center mb-12">Pick Your Free Class — Learn Anytime, Anywhere</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12">
        <ClassCard
          v-for="classItem in paginatedClasses"
          :key="classItem.id"
          :class-data="classItem"
        />
      </div>

      <!-- Pagination -->
      <!--
      <div class="flex justify-center items-center space-x-1 mt-8">
        <button
          v-for="page in totalPages"
          :key="page"
          @click="currentPage = page"
          :class="[
            'px-4 py-2 rounded-lg transition-colors duration-200',
            currentPage === page
              ? 'bg-teal text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          ]"
        >
          {{ page }}
        </button>
      </div>
      -->
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import HeroSection from '@/components/HeroSection.vue';
import ClassCard from '@/components/ClassCard.vue';
import { freeClasses } from '@/data/programs';

const headerImage = '/header.jpg';
const loading = ref(true);
const currentPage = ref(1);
const itemsPerPage = 9; // Show more items per page

// Use the freeClasses data directly
const classes = ref(freeClasses);

// Calculate total pages based on the number of classes
const totalPages = computed(() => Math.ceil(classes.value.length / itemsPerPage));

// Get the classes for the current page
const paginatedClasses = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;

  // Map the classes to ensure they have all required properties for ClassCard
  return classes.value.slice(start, end).map(classItem => ({
    ...classItem,
    image: classItem.image || classItem.mentorImage,
    date: classItem.date || 'TBA',
    time: classItem.time || 'TBA',
  }));
});

onMounted(() => {
  // Set loading to false when component is mounted
  loading.value = false;

  // Scroll to top of the page
  window.scrollTo({ top: 0, behavior: 'smooth' });
});
</script>