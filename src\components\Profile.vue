<template>
    <!-- Removed the full-height screen centering -->
    <div class="flex justify-center items-center">
      <!-- Outer circle with gradient background - reduced size -->
      <div class="relative">
        <!-- Circle with gradient and border outside - reduced size -->
        <div class="w-20 h-20 rounded-full bg-gradient-to-t from-gray-100 to-gray-200 border-4 border-white flex justify-center items-center">
          <!-- Profile image inside the circle - reduced size -->
          <img src="/Profile.png" alt="Profile" class="w-16 h-16 rounded-full object-cover" />
        </div>
      </div>
    </div>
  </template>

  <script>
  export default {
    name: 'Profile'
  }
  </script>

