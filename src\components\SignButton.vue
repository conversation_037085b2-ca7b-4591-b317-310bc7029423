<template>
  <button class="flex items-center justify-center space-x-2 px-4 sm:px-8 md:px-12 py-2 bg-[#ff8c00] text-white font-semibold rounded-md hover:bg-[#ff7f00] focus:outline-none transition duration-200 ease-in-out w-full">
    {{ label }}
  </button>
</template>

<script>
export default {
  name: 'SignButton',
  props: {
    label: {
      type: String,
      default: 'Sign In'
    }
  }
}
</script>

<style scoped>
/* Custom CSS jika perlu */
</style>
