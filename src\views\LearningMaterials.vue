<template>
  <StudentLayoutWrapper>
    <StudentBreadcrumb :items="breadcrumbItems" />

    <!-- Main Content -->

      <!-- Materials List -->
      <div class="w-full">
        <!-- Material Items -->
        <div class="relative">
          <div v-for="(material, index) in currentClass?.materials" :key="material.id"
               class="relative group cursor-pointer transition-colors"
               @click="navigateToMaterial(material.id)">
            <div class="absolute inset-0 -left-2 sm:-left-6 group-hover:bg-orange-50 transition-colors"></div>

            <!-- Material Content -->
            <div class="py-3 sm:py-4 px-4 sm:px-6 md:px-8 relative z-10">
              <div class="flex items-center justify-between">
                <h3 class="text-base sm:text-lg font-semibold text-gray-900 pr-2">{{ material.title }}</h3>

                <div class="flex items-center flex-shrink-0">
                  <!-- Read Status Badge -->
                  <div v-if="material.isRead"
                       class="flex items-center bg-orange/10 text-orange text-xs sm:text-sm rounded-full px-2 sm:px-3 py-0.5 sm:py-1 mr-2"
                       @click.stop>
                    Mark as read
                  </div>

                  <!-- Chevron Icon -->
                  <div class="text-gray-400 group-hover:text-orange transition-all">
                    <svg xmlns="http://www.w3.org/2000/svg"
                         class="h-4 w-4 sm:h-5 sm:w-5 transform group-hover:translate-x-1 transition-transform duration-200"
                         fill="none"
                         viewBox="0 0 24 24"
                         stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            <!-- Divider Line (except for last item) -->
            <div v-if="index < currentClass?.materials.length - 1" class="border-b border-gray-200 mx-4 sm:mx-6 md:mx-8 relative z-10"></div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="!currentClass?.materials?.length" class="py-6 sm:py-8 px-4 sm:px-6 md:px-8 text-center text-gray-500 text-sm sm:text-base">
          No learning materials available for this class.
        </div>
      </div>
  </StudentLayoutWrapper>
</template>

<script setup>
import { computed } from 'vue';
import StudentLayoutWrapper from '@/components/@student/StudentLayoutWrapper.vue';
import StudentBreadcrumb from '@/components/@student/StudentBreadcrumb.vue';
import { useRouter, useRoute } from 'vue-router';
import { useClassStore, STORAGE_KEYS } from '@/data/availableClasses';

const router = useRouter();
const route = useRoute();
const classStore = useClassStore();
const currentClass = classStore.currentClass;

const materialsList = currentClass.value?.materials || [];

// Calculate progress percentage (used internally for data synchronization)
classStore.calculateProgress(parseInt(route.params.classId));

// We'll read the class source directly in the computed property
// This ensures we always have the latest value

const breadcrumbItems = computed(() => {
  // Always check localStorage directly to ensure we have the latest value
  const currentSource = localStorage.getItem(STORAGE_KEYS.CLASS_SOURCE) || 'studied';

  // Determine the appropriate breadcrumb title based on class status and source
  let firstBreadcrumbTitle = 'Classes Studied';
  let firstBreadcrumbPath = '/student/academy';

  // If class is completed or source is 'completed', show "Completed Classes"
  if (currentClass.value?.status === 'completed' || currentSource === 'completed') {
    firstBreadcrumbTitle = 'Completed Classes';
    // When clicking on the breadcrumb, it should navigate to academy with completed tab active
    firstBreadcrumbPath = {
      path: '/student/academy',
      query: { tab: 'completed' }
    };
  }

  return [
    {
      title: firstBreadcrumbTitle,
      path: firstBreadcrumbPath
    },
    {
      title: currentClass.value?.title || 'Class',
      path: `/student/class/${route.params.classId}`
    },
    {
      title: 'Learning Materials',
      active: true
    }
  ];
});

const navigateToMaterial = (materialId) => {
  const material = materialsList.find(m => m.id === materialId);
  if (material) {
    classStore.setCurrentMaterial(materialId);
    router.push({
      name: 'DetailLearningMaterials',
      params: {
        classId: route.params.classId,
        materialId
      }
    });
  }
};


</script>
