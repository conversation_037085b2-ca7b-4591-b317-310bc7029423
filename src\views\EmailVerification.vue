<template>
  <div class="flex flex-col items-center justify-start min-h-screen bg-gray-50 overflow-x-hidden">
    <!-- Header with logo - fixed width container -->
    <div class="w-full border-b border-gray-300 py-4">
      <div class="container mx-auto px-4">
        <router-link to="/" class="flex items-center">
          <img src="/logo.png" alt="FlowCamp Logo" class="h-10" />
          <span class="ml-2 text-[#ff8c00] font-bold text-xl">Flow Camp</span>
        </router-link>
      </div>
    </div>

    <!-- Main content container - centered with max width -->
    <div class="container mx-auto flex justify-center px-4 py-8">
      <div class="w-full max-w-md">
        <!-- Email icon in circle -->
        <div class="flex justify-center mb-6">
          <div class="w-20 h-20 rounded-full bg-gray-100 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
        </div>

        <!-- Form content -->
        <div class="w-full">
          <h2 class="text-3xl font-bold text-center mb-2">Confirm your email</h2>
          <p class="text-center text-gray-600 mb-6">
            We've sent an email with a code to
            <span class="font-medium">{{ email }}</span>, please enter it below to
            create your {{ source === 'register' ? 'FlowCamp account' : 'new password' }}.
          </p>

          <div v-if="formError" class="mb-4 p-3 bg-red-100 text-red-700 rounded-md text-sm">
            {{ formError }}
          </div>

          <div v-if="resendSuccess" class="mb-4 p-3 bg-green-100 text-green-700 rounded-md text-sm flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            A new code has been sent to your email
          </div>

          <form @submit.prevent="verifyCode">
            <div class="mb-6 flex justify-center">
              <VerificationInput v-model="code" :length="6" ref="verificationInput" />
            </div>

            <div class="w-full flex justify-center">
              <div class="flex justify-center" style="width: 328px;">
                <button
                  type="submit"
                  class="w-full flex items-center justify-center px-4 py-3 bg-[#ff8c00] text-white font-semibold rounded-md hover:bg-[#ff7f00] focus:outline-none transition duration-200 ease-in-out"
                >
                  {{ source === 'register' ? 'Verify Account' : 'Verify Email' }}
                </button>
              </div>
            </div>
          </form>

          <div class="text-center mt-6 mb-8">
            <p class="text-sm text-gray-600 mb-2">
              Didn't receive a code?
              <button
                @click="resendCode"
                :disabled="cooldownActive"
                :class="[
                  'font-medium transition-colors duration-200 ease-in-out',
                  cooldownActive ? 'text-gray-400 cursor-not-allowed' : 'text-[#ff8c00] hover:underline'
                ]"
              >
                <span v-if="cooldownActive">
                  Resend code ({{ cooldownTime }}s)
                </span>
                <span v-else>
                  Send a new code
                </span>
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer with copyright only -->
    <div class="w-full mt-auto border-t border-gray-200">
      <div class="container mx-auto px-4 py-4 flex justify-center items-center">
        <Copyright />
      </div>
    </div>
  </div>
</template>

<script>
import Copyright from "@/components/Copyright.vue";
import VerificationInput from "@/components/VerificationInput.vue";

export default {
  name: "EmailVerification",
  components: {
    Copyright,
    VerificationInput
  },
  props: {
    source: {
      type: String,
      default: 'register', // 'register' or 'forgot-password'
      validator: (value) => ['register', 'forgot-password'].includes(value)
    }
  },
  data() {
    return {
      email: '',
      code: '',
      formError: '',
      resendSuccess: false,
      cooldownActive: false,
      cooldownTime: 60, // 60 seconds cooldown
      cooldownTimer: null,
    };
  },
  created() {
    // Get email from route query or use a default
    this.email = this.$route.query.email || '<EMAIL>';
  },

  beforeDestroy() {
    // Clean up the timer when component is destroyed
    if (this.cooldownTimer) {
      clearInterval(this.cooldownTimer);
    }
  },
  watch: {
    code(newValue) {
      console.log("Code changed:", newValue, "Type:", typeof newValue);

      // Ensure value is a string
      const valueStr = String(newValue || '');
      console.log("Value as string:", valueStr, "Length:", valueStr.length);

      // Count actual digits (not empty strings)
      const filledDigits = valueStr.replace(/[^0-9]/g, '').length;
      console.log("Filled digits:", filledDigits);

      // Clear error message if code is complete
      if (filledDigits === 6) {
        this.formError = "";
      }
    }
  },
  methods: {
    verifyCode() {
      console.log("Verifying code:", this.code, "Type:", typeof this.code);

      // Ensure code is a string and handle both string and event object cases
      let codeStr;
      if (typeof this.code === 'object' && this.code !== null) {
        // If it's an event object, try to get the value
        if (this.code.target && this.code.target.value) {
          codeStr = String(this.code.target.value || '');
        } else {
          // If we can't get a value, use an empty string
          codeStr = '';
        }
      } else {
        // If it's already a string or other primitive, convert to string
        codeStr = String(this.code || '');
      }

      // Count actual digits (not empty strings)
      const filledDigits = codeStr.replace(/[^0-9]/g, '').length;
      console.log("Code string:", codeStr, "Filled digits:", filledDigits);

      // Check if all digits are filled
      if (filledDigits !== 6) {
        this.formError = "Please enter the 6-digit code";
        return;
      }

      // Here you would typically make an API call to verify the code
      console.log("Code validation passed, proceeding with verification");

      // For demo purposes, simulate successful verification
      if (this.source === 'register') {
        // Redirect to home or dashboard after successful registration
        this.$router.push('/');
      } else {
        // Redirect to reset password page
        this.$router.push('/ForgotPassword');
      }
    },
    resendCode() {
      // Don't allow resending if cooldown is active
      if (this.cooldownActive) {
        return;
      }

      // Clear any previous messages
      this.formError = "";

      // Here you would typically make an API call to resend the code
      console.log("Resending code to:", this.email);

      // Reset the verification input
      if (this.$refs.verificationInput && typeof this.$refs.verificationInput.reset === 'function') {
        this.$refs.verificationInput.reset();
      }
      this.code = '';

      // Show success message
      this.resendSuccess = true;

      // Start cooldown
      this.startCooldown();

      // Auto-hide success message after 5 seconds
      setTimeout(() => {
        this.resendSuccess = false;
      }, 5000);
    },

    startCooldown() {
      this.cooldownActive = true;
      this.cooldownTime = 60; // Reset to 60 seconds

      // Clear any existing timer
      if (this.cooldownTimer) {
        clearInterval(this.cooldownTimer);
      }

      // Start the countdown
      this.cooldownTimer = setInterval(() => {
        this.cooldownTime -= 1;
        if (this.cooldownTime <= 0) {
          clearInterval(this.cooldownTimer);
          this.cooldownActive = false;
        }
      }, 1000);
    }
  }
};
</script>

<style scoped>
/* No custom styles needed */
</style>
