<script setup>
import NavbarAcademy from '@/components/NavbarAcademy.vue'

const ongoingClasses = [
  {
    title: "Mastery In Golang: From Zero to Hero",
    date: "17 Februari 2025",
    progress: 25,
    completed: 5,
    total: 20,
    students: 21342
  },
  {
    title: "AI for Everyone: Understanding the Basics of Machine Learning",
    date: "1 January 2025",
    progress: 50,
    completed: 5,
    total: 10,
    students: 342
  },
  {
    title: "UI/UX Bootcamp: Designing for Web & Mobile",
    date: "2 January 2024",
    progress: 75,
    completed: 15,
    total: 20,
    students: 543
  },
  {
    title: "Mobile App Development with Flutter",
    date: "7 Februari 2025",
    progress: 100,
    completed: 18,
    total: 18,
    students: 123
  },
  {
    title: "React Native Intermediate",
    date: "10 Februari 2025",
    progress: 40,
    completed: 8,
    total: 20,
    students: 890
  },
  {
    title: "Backend with Node.js & Express",
    date: "12 Januari 2025",
    progress: 60,
    completed: 12,
    total: 20,
    students: 673
  },
  {
    title: "Tailwind CSS From Scratch",
    date: "3 Januari 2025",
    progress: 85,
    completed: 17,
    total: 20,
    students: 1023
  },
  {
    title: "Intro to Databases with PostgreSQL",
    date: "22 Desember 2024",
    progress: 30,
    completed: 6,
    total: 20,
    students: 754
  }
];

import { ref, computed } from 'vue'

const classes = [,
  'AI for Everyone: Understanding the Basics of Machine Learning',
  'UI/UX Bootcamp: Designing for Web & Mobile',
  'Modern Web Development with React & Next.js',
  'Backend Mastery: Building APIs with Node.js & Express',
  'Mastery In Golang: From Zero to Hero',
  'Hack the System: Ethical Hacking & Penetration Testing',
]

const selectedClass = ref(classes[0])

const allPerformanceData = {
  'AI for Everyone: Understanding the Basics of Machine Learning': [
    { name: 'Leo Herwitz', mark: 90 },
    { name: 'Ashlynn Stanton', mark: 90 },
    { name: 'Talan Workman', mark: 86 },
    { name: 'Paityn Siphron', mark: 85 },
    { name: 'Emery Ekstrom Bothman', mark: 80 },
    { name: 'Kianna Stanton', mark: 75 },
    { name: 'Giana Vaccaro', mark: 75 },
    { name: 'Alfredo Dorwart', mark: 70 },
    { name: 'Brandon Baptista', mark: 60 },
    { name: 'Jaxson Torff', mark: 60 }
  ],
  'UI/UX Bootcamp: Designing for Web & Mobile': [
    { name: 'Amanda Fox', mark: 88 },
    { name: 'John Doe', mark: 81 },
    { name: 'Sarah Green', mark: 92 },
    { name: 'Liam Turner', mark: 70 }
  ],
  'Modern Web Development with React & Next.js': [
    { name: 'Kevin Stone', mark: 84 },
    { name: 'Nina Brooks', mark: 75 },
    { name: 'Aliya Patel', mark: 89 }
  ],
  'Backend Mastery: Building APIs with Node.js & Express': [
    { name: 'David Nolan', mark: 91 },
    { name: 'Emma Wright', mark: 80 },
    { name: 'Mason Brown', mark: 87 }
  ],
  'Mastery In Golang: From Zero to Hero': [
    { name: 'Sophia Sky', mark: 93 },
    { name: 'Noah Kent', mark: 85 }
  ],
  'Hack the System: Ethical Hacking & Penetration Testing': [
    { name: 'Eli Harris', mark: 95 },
    { name: 'Luna Storm', mark: 88 }
  ]
}

const filteredStudents = computed(() => allPerformanceData[selectedClass.value] || [])

const today = new Date()
const currentDate = ref(new Date(today.getFullYear(), today.getMonth(), today.getDate()))

const monthNames = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
]
const dayNames = ['fri', 'sat', 'sun', 'mon', 'tue']

// Mendapatkan minggu yang sedang aktif (5 hari: jumat-selasa)
const currentWeekDates = computed(() => {
  const date = new Date(currentDate.value)
  const week = []
  // Cari hari Jumat terdekat ke belakang
  let dayOfWeek = date.getDay()
  // 5 = Jumat, 0 = Minggu, dst
  let diffToFriday = (5 - dayOfWeek + 7) % 7
  let start = new Date(date)
  start.setDate(date.getDate() - ((dayOfWeek + 2) % 7))
  for (let i = 0; i < 5; i++) {
    const d = new Date(start)
    d.setDate(start.getDate() + i)
    week.push(d)
  }
  return week
})

function prevWeek() {
  currentDate.value.setDate(currentDate.value.getDate() - 7)
  currentDate.value = new Date(currentDate.value)
}
function nextWeek() {
  currentDate.value.setDate(currentDate.value.getDate() + 7)
  currentDate.value = new Date(currentDate.value)
}

</script>

<template>
  <div>
    <!-- NAVBAR -->
    <NavbarAcademy />

   <!-- GREETING + ILLUSTRATION -->
<section class="mt-6 px-6 flex gap-6">
  <!-- LEFT ORANGE BOX (Greeting + Illustration) -->
  <div class="flex flex-1 items-center justify-between bg-orange p-6 rounded-2xl shadow-md">
    <!-- GREETING -->
    <div class="relative">
      <h2 class="text-white text-2xl font-bold mb-1">
        Hello Nathan Tjoe A On 👋
      </h2>
      <p class="text-sm text-white">
        Welcome to your dashboard of your account.<br />
        Have a nice day to work
      </p>
    </div>

    <!-- ILLUSTRATION -->
    <div class="w-40 flex-shrink-0 ml-6">
      <img src="/illustration.png" alt="People Working" class="w-full object-contain" />
    </div>
  </div>

  <!-- CALENDAR (dynamic) -->
  <div class="bg-primary rounded-2xl px-6 py-5 text-white text-center shadow w-72 flex-shrink-0">
    <div class="flex items-center justify-between mb-4 text-base">
      <span @click="prevWeek" class="cursor-pointer">&lt;</span>
      <span class="font-bold">
        {{ monthNames[currentDate.getMonth()] }} {{ currentDate.getFullYear() }}
      </span>
      <span @click="nextWeek" class="cursor-pointer">&gt;</span>
    </div>
    <div class="grid grid-cols-5 gap-2 text-sm font-medium mb-2">
      <span v-for="(day, i) in dayNames" :key="i">{{ day }}</span>
    </div>
    <div class="grid grid-cols-5 gap-2 text-sm">
      <span
        v-for="date in currentWeekDates"
        :key="date"
        :class="[
          'rounded-full px-3 py-1',
          date.toDateString() === today.toDateString() ? 'bg-white text-primary' : ''
        ]"
      >
        {{ date.getDate() }}
      </span>
    </div>
  </div>
</section>


    <!-- PAGE CONTENT -->
    <section class="px-6 py-12">
     <!-- OVERVIEW -->
<section class="px-6 pb-12">
  <h2 class="text-xl font-semibold text-gray-800 mb-6">Overview</h2>

  <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
    <!-- Box 1 -->
    <div class="border border-orange rounded-lg px-6 py-4 flex items-center gap-4">
      <img src="/graduation.png" alt="Class Complete" class="w-8 h-8" />
      <div>
        <p class="text-sm text-gray-700">Class Complete</p>
        <p class="text-xl font-semibold text-gray-900">5</p>
      </div>
    </div>

    <!-- Box 2 -->
    <div class="border border-orange rounded-lg px-6 py-4 flex items-center gap-4">
      <img src="/people.png" alt="Total Students" class="w-8 h-8" />
      <div>
        <p class="text-sm text-gray-700">Total Students</p>
        <p class="text-xl font-semibold text-gray-900">236</p>
      </div>
    </div>

    <!-- Box 3 -->
    <div class="border border-orange rounded-lg px-6 py-4 flex items-center gap-4">
      <img src="/book.png" alt="Total Classes" class="w-8 h-8" />
      <div>
        <p class="text-sm text-gray-700">Total Classes</p>
        <p class="text-xl font-semibold text-gray-900">12</p>
      </div>
    </div>

    <!-- Box 4 -->
    <div class="border border-orange rounded-lg px-6 py-4 flex items-center gap-4">
      <img src="/file.png" alt="Lesson Complete" class="w-8 h-8" />
      <div>
        <p class="text-sm text-gray-700">Lesson Complete</p>
        <p class="text-xl font-semibold text-gray-900">150</p>
      </div>
    </div>
  </div>
</section>

<div class="flex flex-col lg:flex-row gap-6 mt-10">
<!-- ONGOING CLASSES -->
<section class="px-6 pb-12">
  <div class="bg-orange p-6 rounded-2xl shadow-md">
    <h2 class="text-xl font-bold text-white mb-4">Ongoing Classes</h2>

    <div class="space-y-4 max-h-[400px] overflow-y-auto pr-2 custom-scroll">
      <div
  v-for="(cls, idx) in ongoingClasses"
  :key="idx"
  class="bg-white rounded-xl shadow px-6 py-4 flex items-center justify-between gap-4"
>
  <!-- LEFT: Title & Date -->
  <div class="flex-1 min-w-[200px]">
    <h3 class="font-semibold text-gray-800">{{ cls.title }}</h3>
    <p class="text-sm text-gray-500 mt-1">{{ cls.date }}</p>
  </div>

  <!-- MIDDLE: Progress Bar -->
  <div class="flex-1 min-w-[200px]">
    <p class="text-xs text-gray-500 mb-0.5">Your progress</p>
    <p class="text-blue-500 font-bold text-sm mb-1">{{ cls.completed }} Modul</p>
    <div class="w-full bg-gray-200 rounded-full h-2">
      <div
        class="h-2 rounded-full bg-primary"
        :class="{
          'w-1/4': cls.progress === 25,
          'w-1/2': cls.progress === 50,
          'w-3/4': cls.progress === 75,
          'w-full': cls.progress === 100,
          'w-2/5': cls.progress === 40,
          'w-3/5': cls.progress === 60,
          'w-4/5': cls.progress === 80,
          'w-[85%]': cls.progress === 85,
          'w-[30%]': cls.progress === 30
        }"
      ></div>
    </div>
  </div>

  <!-- RIGHT: Stats -->
  <div class="flex-1 min-w-[220px] flex items-center gap-6 text-sm text-gray-600 whitespace-nowrap">
    <div class="flex items-center gap-2">
      <img src="/people.png" class="w-4 h-4" />
      <span>{{ cls.students.toLocaleString() }} student</span>
    </div>
    <div class="flex items-center gap-2">
      <img src="/file.png" class="w-4 h-4" />
      <span>{{ cls.total }} modul</span>
    </div>
  </div>
</div>
    </div>
    </div>
    </section>


<!-- PERFORMANCE STUDENT SECTION -->
<div class="w-full md:w-1/3 bg-orange p-6 rounded-2xl shadow-md text-black h-fit">
  <h2 class="text-2xl font-semibold mb-4">Performance Students</h2>

  <!-- DROPDOWN -->
  <select v-model="selectedClass" class="w-full p-3 rounded-lg border border-white mb-4 text-lg font-semibold text-black focus:outline-none">
    <option disabled value="">Classes</option>
    <option v-for="cls in classes" :key="cls" :value="cls">{{ cls }}</option>
  </select>

  <!-- TABLE -->
  <div class="bg-orange border border-white rounded-xl px-4 py-3 text-sm">
    <h3 class="text-md font-semibold mb-2">{{ selectedClass }}</h3>
    <div class="border-t border-white pt-2">
      <div class="flex justify-between font-semibold mb-2">
        <span>Name</span>
        <span>Mark</span>
      </div>
      <div v-for="student in filteredStudents" :key="student.name" class="flex justify-between mb-1">
        <span>{{ student.name }}</span>
        <span>{{ student.mark }}</span>
      </div>
    </div>
  </div>
</div>
</div>



    </section>
  </div>
</template>
