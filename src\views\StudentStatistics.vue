<template>
  <StudentLayoutWrapper contentClass="bg-gray-50">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-800">Statistics</h1>
      <p class="text-gray-600 mt-1">Track your learning progress and performance</p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      <!-- Loading State for Dashboard Cards -->
      <template v-if="isLoading">
        <div v-for="i in 4" :key="i" class="bg-white p-4 rounded-lg shadow-sm border border-gray-100 animate-pulse">
          <div class="flex justify-between items-center">
            <div class="w-full">
              <div class="h-4 bg-gray-200 rounded w-24 mb-2"></div>
              <div class="h-8 bg-gray-200 rounded w-16"></div>
            </div>
            <div class="w-10 h-10 rounded-full bg-gray-200 flex-shrink-0"></div>
          </div>
        </div>
      </template>

      <template v-else>
        <!-- Overall Progress Card -->
        <div
          class="bg-white p-4 rounded-lg shadow-sm border border-gray-100 transition-all duration-300 hover:shadow-md hover:border-blue-200"
          role="region"
          aria-label="Overall Progress"
        >
          <div class="flex justify-between items-center">
            <div>
              <p class="text-gray-500 text-sm font-medium">Overall Progress</p>
              <h2 class="text-3xl font-bold mt-1">{{ overallProgress }}%</h2>
            </div>
            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center z-10">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
        </div>

        <!-- Completed Classes Card -->
        <div
          class="bg-white p-4 rounded-lg shadow-sm border border-gray-100 transition-all duration-300 hover:shadow-md hover:border-orange-200"
          role="region"
          aria-label="Completed Classes"
        >
          <div class="flex justify-between items-center">
            <div>
              <p class="text-gray-500 text-sm font-medium">Completed Classes</p>
              <h2 class="text-3xl font-bold mt-1">{{ completedClassesCount }}/{{ totalClasses }}</h2>
            </div>
            <div class="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center z-10">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
          </div>
        </div>

        <!-- Completed Lessons Card -->
        <div
          class="bg-white p-4 rounded-lg shadow-sm border border-gray-100 transition-all duration-300 hover:shadow-md hover:border-green-200"
          role="region"
          aria-label="Completed Lessons"
        >
          <div class="flex justify-between items-center">
            <div>
              <p class="text-gray-500 text-sm font-medium">Completed Lessons</p>
              <h2 class="text-3xl font-bold mt-1">{{ completedLessonsCount }}</h2>
            </div>
            <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center z-10">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <!-- Average Score Card -->
        <div
          class="bg-white p-4 rounded-lg shadow-sm border border-gray-100 transition-all duration-300 hover:shadow-md hover:border-purple-200"
          role="region"
          aria-label="Average Score"
        >
          <div class="flex justify-between items-center">
            <div>
              <p class="text-gray-500 text-sm font-medium">Average Score</p>
              <h2 class="text-3xl font-bold mt-1">{{ averageScore }}%</h2>
            </div>
            <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center z-10">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
              </svg>
            </div>
          </div>
        </div>
      </template>
    </div>

    <!-- Monthly Activity Chart -->
    <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100 transition-all duration-300 hover:shadow-md mb-8">
      <div class="flex justify-between items-center mb-4">
        <h3 class="font-semibold text-gray-800">Monthly Learning Activity</h3>
        <div class="bg-gray-100 rounded-md px-3 py-1 text-xs font-medium">
          Monthly
        </div>
      </div>
      <p class="text-xs text-gray-500 mb-4">Your learning activity over the past year</p>

      <!-- Loading State for Chart -->
      <div v-if="isLoading" class="h-64 w-full bg-gray-100 rounded-md animate-pulse flex items-center justify-center">
        <svg class="w-10 h-10 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      </div>

      <!-- Empty State for Chart -->
      <div v-else-if="!hasChartData" class="h-64 w-full bg-gray-50 rounded-md flex flex-col items-center justify-center">
        <svg class="w-12 h-12 text-gray-400 mb-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
        <p class="text-gray-500">No activity data available yet</p>
        <p class="text-sm text-gray-400">Start learning to see your progress</p>
      </div>

      <!-- Chart -->
      <div v-else class="h-64 w-full relative">
        <!-- Loading message that will be hidden when chart loads -->
        <div v-if="isChartLoading" class="absolute inset-0 flex items-center justify-center text-gray-400 text-sm">
          <span>Loading chart...</span>
        </div>
        <canvas id="activityChart" class="w-full h-full"></canvas>
      </div>

      <!-- Chart Legend -->
      <div v-if="!isLoading && hasChartData" class="flex flex-wrap gap-4 mt-4 text-xs">
        <div class="flex items-center">
          <div class="w-3 h-3 rounded-full bg-orange-500 mr-1.5" style="background-color: #F2720C;"></div>
          <span>Active Classes</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 rounded-full bg-purple-600 mr-1.5"></div>
          <span>Learning Progress</span>
        </div>
      </div>
    </div>

    <!-- Performance by Category -->
    <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-100 transition-all duration-300 hover:shadow-md mb-8">
      <div class="flex justify-between items-center mb-4">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-purple-600 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <h3 class="font-semibold text-gray-800">Performance by Category</h3>
        </div>

        <!-- Sort Controls -->
        <div class="flex items-center space-x-2">
          <button
            @click="sortCategoryBy('name')"
            class="text-xs px-2 py-1 rounded transition-colors duration-200"
            :class="sortBy === 'name' ? 'bg-purple-100 text-purple-700' : 'text-gray-500 hover:bg-gray-100'"
          >
            Sort by Name
          </button>
          <button
            @click="sortCategoryBy('score')"
            class="text-xs px-2 py-1 rounded transition-colors duration-200"
            :class="sortBy === 'score' ? 'bg-purple-100 text-purple-700' : 'text-gray-500 hover:bg-gray-100'"
          >
            Sort by Score
          </button>
        </div>
      </div>
      <p class="text-xs text-gray-500 mb-5">Your performance across different learning categories</p>

      <!-- Loading State -->
      <div v-if="isLoading" class="space-y-5">
        <div v-for="i in 4" :key="i" class="animate-pulse">
          <div class="flex justify-between items-center mb-2">
            <div class="flex items-center">
              <div class="h-8 w-8 bg-gray-200 rounded-full mr-3"></div>
              <div class="h-4 bg-gray-200 rounded w-32"></div>
            </div>
            <div class="h-5 bg-gray-200 rounded w-14"></div>
          </div>
          <div class="h-3 bg-gray-200 rounded-full w-full mb-6"></div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else-if="categoryPerformance.length === 0" class="h-48 w-full bg-gray-50 rounded-md flex flex-col items-center justify-center">
        <svg class="w-14 h-14 text-gray-400 mb-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
        </svg>
        <p class="text-gray-500 font-medium">No performance data available yet</p>
        <p class="text-sm text-gray-400 mt-1">Complete tasks to see your performance</p>
        <button
          @click="navigateToAvailableClasses"
          class="mt-4 px-4 py-2 bg-purple-600 text-white rounded-md text-sm hover:bg-purple-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50"
        >
          Explore Classes
        </button>
      </div>

      <!-- Performance Bars -->
      <div v-else class="space-y-6">
        <div
          v-for="(category, index) in sortedCategoryPerformance"
          :key="index"
          class="mb-6 p-3 rounded-lg hover:bg-gray-50 transition-all duration-200 transform hover:-translate-y-1"
        >
          <div class="flex justify-between items-center mb-2">
            <div class="flex items-center">
              <div class="w-8 h-8 rounded-full flex items-center justify-center mr-3" :class="getCategoryIconBgClass(category.name)">
                <svg class="w-4 h-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path :d="getCategoryIconPath(category.name)" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
                </svg>
              </div>
              <span class="text-sm font-medium text-gray-700">{{ category.name }}</span>
            </div>
            <div class="flex items-center">
              <span
                class="text-sm font-medium px-2 py-1 rounded-full"
                :class="getScoreBadgeClass(category.score)"
              >
                {{ category.score }}%
              </span>
              <span
                class="ml-2 text-xs px-2 py-0.5 rounded-full"
                :class="getPerformanceLevelClass(category.score)"
              >
                {{ getPerformanceLevel(category.score) }}
              </span>
            </div>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-3 relative overflow-hidden">
            <div
              class="h-3 rounded-full transition-all duration-1000 ease-out"
              :class="getScoreBackgroundClass(category.score)"
              :style="{ width: `${animateProgress ? category.score : 0}%` }"
            ></div>
          </div>
          <div class="mt-2 flex flex-wrap justify-between text-xs text-gray-500">
            <div class="flex space-x-4">
              <span>Tasks completed: {{ category.totalTasks || 'N/A' }}</span>
              <span>Classes: {{ category.classCount || 0 }}</span>
            </div>
            <span>Last updated: {{ getLastUpdatedText(category) }}</span>
          </div>
        </div>
      </div>
    </div>
  </StudentLayoutWrapper>
</template>

<script setup>
import { ref, computed, onMounted, watch, onUnmounted, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import StudentLayoutWrapper from '@/components/@student/StudentLayoutWrapper.vue';
import { useClassStore } from '@/data/availableClasses';
import Chart from 'chart.js/auto';

const router = useRouter();

const classStore = useClassStore();

// Loading state
const isLoading = ref(true);

// Chart instance reference
const chartInstance = ref(null);
// Chart loading state
const isChartLoading = ref(true);
// Category sorting
const sortBy = ref('score'); // Default sort by score
// Animation control for progress bars
const animateProgress = ref(false);

// Dashboard statistics
const completedClassesCount = ref(0);
const overallProgress = ref(0);
const totalClasses = ref(0);
const completedLessonsCount = ref(0);
const averageScore = ref(0);

// Computed properties for class data
const classes = computed(() => {
  return classStore.classes?.value || [];
});

const completedClasses = computed(() => {
  return classes.value.filter(c => c.progress === 100);
});

// Count total lessons/materials across all classes
const totalLessons = computed(() => {
  return classes.value.reduce((total, cls) => {
    return total + (cls.materials?.length || 0);
  }, 0);
});

// Count completed lessons/materials across all classes
const completedLessons = computed(() => {
  return classes.value.reduce((total, cls) => {
    const completedMaterials = cls.materials?.filter(m => m.isComplete || (m.isRead && (!m.hasTask || (m.hasTask && m.taskStatus && m.taskStatus !== 'pending' && m.taskStatus !== 'past_due')))) || [];
    return total + completedMaterials.length;
  }, 0);
});

// Calculate average score from all completed tasks
const calculateAverageScore = computed(() => {
  let totalScore = 0;
  let totalTasks = 0;

  classes.value.forEach(cls => {
    if (cls.materials) {
      cls.materials.forEach(material => {
        if (material.hasTask && material.taskStatus === 'completed' && material.taskScore !== undefined) {
          totalScore += material.taskScore;
          totalTasks++;
        }
      });
    }
  });

  return totalTasks > 0 ? Math.round(totalScore / totalTasks) : 0;
});

// Monthly class activity data for chart
const monthlyClassData = computed(() => {
  const monthlyData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    activeClasses: Array(12).fill(0),
    learningProgress: Array(12).fill(0)
  };

  // Return default data if no classes
  if (!classes.value || classes.value.length === 0) {
    console.log('No classes available for monthly data calculation');
    return monthlyData;
  }

  // Valid task statuses for completed materials
  const validTaskStatuses = ['completed', 'turned_in', 'reviewed'];

  // Count active classes and track learning progress by month
  classes.value.forEach(cls => {
    // Use joinedDate instead of startDate (which doesn't exist in the data)
    const dateToUse = cls.joinedDate || cls.postedDate;

    if (dateToUse) {
      try {
        const date = new Date(dateToUse);

        // Validate date is valid
        if (isNaN(date.getTime())) {
          console.warn(`Invalid date format for class ${cls.id}: ${dateToUse}`);
          return; // Skip this class
        }

        const month = date.getMonth();

        // Increment active classes for this month
        monthlyData.activeClasses[month]++;

        // Track learning progress (completed materials) by month
        if (cls.materials && cls.materials.length > 0) {
          // Count completed materials with more robust checking
          const completedMaterials = cls.materials.filter(m =>
            m.isComplete ||
            (m.isRead && m.taskStatus &&
              (validTaskStatuses.includes(m.taskStatus.toLowerCase()) ||
               m.taskStatus !== 'pending' && m.taskStatus !== 'past_due'))
          ).length;

          // Calculate progress percentage for this class
          const progressPercentage = (completedMaterials / cls.materials.length) * 100;

          // Add to the monthly progress (we'll average it later)
          monthlyData.learningProgress[month] += progressPercentage;
        }
      } catch (error) {
        console.error(`Error processing date for class ${cls.id}:`, error);
      }
    }
  });

  // Calculate average progress for each month
  for (let i = 0; i < 12; i++) {
    if (monthlyData.activeClasses[i] > 0) {
      // Average the progress across active classes
      monthlyData.learningProgress[i] = Math.round(monthlyData.learningProgress[i] / monthlyData.activeClasses[i]);
    }
  }

  // If no data is available, add some sample data for testing
  const hasData = monthlyData.activeClasses.some(value => value > 0) ||
                  monthlyData.learningProgress.some(value => value > 0);

  if (!hasData) {
    console.log('No real data available, adding sample data for testing');
    // Add sample data for testing (will be shown only when no real data is available)
    monthlyData.activeClasses = [1, 2, 3, 2, 4, 3, 2, 1, 3, 4, 2, 1];
    monthlyData.learningProgress = [25, 40, 60, 45, 75, 80, 65, 50, 70, 85, 60, 40];
  }

  return monthlyData;
});

// Check if there's any chart data to display
const hasChartData = computed(() => {
  const hasActiveData = monthlyClassData.value.activeClasses.some(value => value > 0);
  const hasProgressData = monthlyClassData.value.learningProgress.some(value => value > 0);

  console.log('Chart data availability check:', { hasActiveData, hasProgressData });

  return hasActiveData || hasProgressData;
});

// Performance by category
const categoryPerformance = computed(() => {
  // Return empty array if no classes
  if (!classes.value || classes.value.length === 0) {
    console.log('No classes available for category performance calculation');
    return [];
  }

  const categories = {};
  const validTaskStatuses = ['completed', 'turned_in', 'reviewed']; // Include all valid completion statuses

  classes.value.forEach(cls => {
    // Ensure category is properly set, default to 'General' if missing
    const category = cls.category || 'General';

    // Initialize category if not already present
    if (!categories[category]) {
      categories[category] = {
        totalScore: 0,
        totalTasks: 0,
        lastUpdated: new Date(),
        classCount: 0
      };
    }

    // Increment class count for this category
    categories[category].classCount++;

    // Process materials if available
    if (cls.materials && cls.materials.length > 0) {
      cls.materials.forEach(material => {
        // Check for completed tasks with scores
        if (material.hasTask &&
            validTaskStatuses.includes(material.taskStatus?.toLowerCase()) &&
            material.taskScore !== undefined) {

          // Add to total score and task count
          categories[category].totalScore += material.taskScore;
          categories[category].totalTasks++;

          // Update last updated timestamp if available
          if (material.completedDate || material.reviewedDate || material.submissionDate) {
            // Use the most recent date available
            const dateToUse = material.reviewedDate || material.completedDate || material.submissionDate;
            const completedDate = new Date(dateToUse);

            if (completedDate > categories[category].lastUpdated) {
              categories[category].lastUpdated = completedDate;
            }
          }
        }
      });
    }
  });

  // Convert to array and calculate average scores
  return Object.keys(categories).map(name => {
    const { totalScore, totalTasks, lastUpdated, classCount } = categories[name];

    // Calculate score, ensuring we don't divide by zero
    const score = totalTasks > 0 ? Math.round(totalScore / totalTasks) : 0;

    return {
      name,
      score,
      totalTasks,
      lastUpdated,
      classCount
    };
  });
});

// Sorted category performance based on selected sort method
const sortedCategoryPerformance = computed(() => {
  if (sortBy.value === 'name') {
    return [...categoryPerformance.value].sort((a, b) => a.name.localeCompare(b.name));
  } else {
    return [...categoryPerformance.value].sort((a, b) => b.score - a.score);
  }
});

// Helper functions for styling
const getScoreBackgroundClass = (score) => {
  if (score >= 90) return 'bg-green-600';
  if (score >= 75) return 'bg-blue-600';
  if (score >= 60) return 'bg-orange-600';
  return 'bg-red-600';
};

// New helper functions for enhanced UI
const getScoreBadgeClass = (score) => {
  if (score >= 90) return 'bg-green-100 text-green-800';
  if (score >= 75) return 'bg-blue-100 text-blue-800';
  if (score >= 60) return 'bg-orange-100 text-orange-800';
  return 'bg-red-100 text-red-800';
};

const getPerformanceLevel = (score) => {
  if (score >= 90) return 'Excellent';
  if (score >= 75) return 'Good';
  if (score >= 60) return 'Average';
  return 'Needs Improvement';
};

const getPerformanceLevelClass = (score) => {
  if (score >= 90) return 'bg-green-50 text-green-700 border border-green-200';
  if (score >= 75) return 'bg-blue-50 text-blue-700 border border-blue-200';
  if (score >= 60) return 'bg-orange-50 text-orange-700 border border-orange-200';
  return 'bg-red-50 text-red-700 border border-red-200';
};

const getCategoryIconBgClass = (category) => {
  const categoryLower = category.toLowerCase();
  if (categoryLower.includes('front') || categoryLower.includes('ui')) return 'bg-blue-500';
  if (categoryLower.includes('back') || categoryLower.includes('server')) return 'bg-green-500';
  if (categoryLower.includes('mobile') || categoryLower.includes('app')) return 'bg-purple-500';
  if (categoryLower.includes('data') || categoryLower.includes('analytics')) return 'bg-yellow-500';
  if (categoryLower.includes('design')) return 'bg-pink-500';
  return 'bg-gray-500';
};

const getCategoryIconPath = (category) => {
  const categoryLower = category.toLowerCase();
  if (categoryLower.includes('front') || categoryLower.includes('ui'))
    return 'M14 10l-2 1m0 0l-2-1m2 1v2.5M20 7l-2 1m2-1l-2-1m2 1v2.5M14 4l-2-1-2 1M4 7l2-1M4 7l2 1M4 7v2.5M12 21l-2-1m2 1l2-1m-2 1v-2.5M6 18l-2-1v-2.5M18 18l2-1v-2.5';
  if (categoryLower.includes('back') || categoryLower.includes('server'))
    return 'M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01';
  if (categoryLower.includes('mobile') || categoryLower.includes('app'))
    return 'M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z';
  if (categoryLower.includes('data') || categoryLower.includes('analytics'))
    return 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z';
  if (categoryLower.includes('design'))
    return 'M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z';
  return 'M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z';
};

const getLastUpdatedText = (category) => {
  // Use the category's lastUpdated date if available, otherwise use current date
  const dateToFormat = category && category.lastUpdated ? category.lastUpdated : new Date();

  return new Date(dateToFormat).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric'
  });
};

// Function to handle sorting
const sortCategoryBy = (sortType) => {
  sortBy.value = sortType;
};

// Function to navigate to available classes
const navigateToAvailableClasses = () => {
  router.push({ name: 'AvailableClasses' });
};

// Function to initialize chart
const initializeChart = () => {
  console.log('Initializing chart...');
  const chartElement = document.getElementById('activityChart');
  if (!chartElement) {
    console.warn('Chart element not found - DOM element might not be ready yet');
    // Try again after a short delay
    setTimeout(() => {
      const retryElement = document.getElementById('activityChart');
      if (retryElement) {
        console.log('Chart element found on retry');
        initializeChartWithElement(retryElement);
      } else {
        console.error('Chart element still not found after retry');
      }
    }, 100);
    return;
  }

  console.log('Chart element found, proceeding with initialization');
  initializeChartWithElement(chartElement);
};

// Separate function to initialize chart with element
const initializeChartWithElement = (chartElement) => {
  // Ensure we have some data to display
  const hasActiveClassData = monthlyClassData.value.activeClasses.some(value => value > 0);
  const hasLearningProgressData = monthlyClassData.value.learningProgress.some(value => value > 0);

  console.log('Chart data check:', {
    hasActiveClassData,
    hasLearningProgressData,
    activeClasses: monthlyClassData.value.activeClasses,
    learningProgress: monthlyClassData.value.learningProgress
  });

  // Create empty state data if no real data is available
  let chartData = { ...monthlyClassData.value };
  if (!hasActiveClassData && !hasLearningProgressData) {
    console.log('No real data available, using empty arrays');
    // Use empty arrays instead of sample data for proper empty state
    chartData = {
      ...chartData,
      activeClasses: Array(12).fill(0),
      learningProgress: Array(12).fill(0)
    };
  }

  // Check if we're on a small screen for responsive adjustments
  const isSmallScreen = window.innerWidth < 640; // sm breakpoint in Tailwind

  try {
    console.log('Getting 2D context from chart element');
    const ctx = chartElement.getContext('2d');

    // Destroy existing chart instance if it exists
    if (chartInstance.value) {
      console.log('Destroying existing chart instance');
      chartInstance.value.destroy();
      chartInstance.value = null;
    }

    console.log('Creating new Chart instance');
    chartInstance.value = new Chart(ctx, {
      type: 'line',
      data: {
        labels: chartData.labels,
        datasets: [
          {
            label: 'Active Classes',
            data: chartData.activeClasses,
            borderColor: '#F2720C', // Orange color
            backgroundColor: 'rgba(242, 114, 12, 0.1)',
            tension: 0.4,
            fill: true,
            borderWidth: 2,
            pointBackgroundColor: '#F2720C',
            pointRadius: 3,
            pointHoverRadius: 5
          },
          {
            label: 'Learning Progress',
            data: chartData.learningProgress,
            borderColor: '#9333ea', // Purple color
            backgroundColor: 'rgba(147, 51, 234, 0.1)',
            tension: 0.4,
            fill: true,
            borderWidth: 2,
            pointBackgroundColor: '#9333ea',
            pointRadius: 3,
            pointHoverRadius: 5
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        onResize: (chart, size) => {
          // Update rotation of x-axis labels based on screen size
          const isSmall = size.width < 640;
          chart.options.scales.x.ticks.maxRotation = isSmall ? 45 : 0;
          chart.options.scales.x.ticks.minRotation = isSmall ? 45 : 0;
        },
        plugins: {
          legend: {
            display: false // We're using our custom legend below the chart
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            titleColor: '#1f2937',
            bodyColor: '#4b5563',
            borderColor: '#e5e7eb',
            borderWidth: 1,
            padding: 12,
            boxPadding: 6,
            usePointStyle: true,
            callbacks: {
              // Format the tooltip title (month)
              title: function(tooltipItems) {
                return tooltipItems[0].label;
              },
              // Format the tooltip label (value)
              label: function(context) {
                let label = context.dataset.label || '';
                if (label) {
                  label += ': ';
                }
                if (context.parsed.y !== null) {
                  // Add % sign for Learning Progress values
                  if (context.dataset.label === 'Learning Progress') {
                    label += context.parsed.y + '%';
                  } else {
                    // For Active Classes, show the value as is
                    label += context.parsed.y;
                  }
                }
                return label;
              }
            }
          }
        },
        scales: {
          x: {
            grid: {
              display: false
            },
            ticks: {
              color: '#9ca3af',
              maxRotation: isSmallScreen ? 45 : 0,
              minRotation: isSmallScreen ? 45 : 0
            }
          },
          y: {
            beginAtZero: true,
            grid: {
              borderDash: [2, 4],
              color: '#e5e7eb'
            },
            ticks: {
              color: '#9ca3af',
              // Format y-axis labels
              callback: function(value) {
                return value;
              }
            }
          }
        }
      }
    });

    console.log('Chart instance created successfully');

    // Add resize event listener for better responsiveness
    const handleResize = () => {
      if (chartInstance.value) {
        const isSmall = window.innerWidth < 640;
        chartInstance.value.options.scales.x.ticks.maxRotation = isSmall ? 45 : 0;
        chartInstance.value.options.scales.x.ticks.minRotation = isSmall ? 45 : 0;
        chartInstance.value.update();
      }
    };

    window.addEventListener('resize', handleResize);

    // Store the event listener for cleanup
    chartInstance.value._resizeListener = handleResize;

    // Hide the loading message
    isChartLoading.value = false;

    return true; // Indicate successful chart creation
  } catch (error) {
    console.error('Error creating chart:', error);
    // Set chart loading to false even on error to hide loading message
    isChartLoading.value = false;
    return false; // Indicate failed chart creation
  }
};

// Lifecycle hooks
onMounted(() => {
  console.log('StudentStatistics component mounted');

  // Load data from class store
  console.log('Classes from store:', classStore.classes.value);

  // Ensure we have sample data for testing if needed
  if (!classStore.classes.value || classStore.classes.value.length === 0) {
    console.log('No classes found in store, consider adding sample data for testing');
  }

  // Simulate data loading with a shorter timeout
  setTimeout(() => {
    console.log('Loading timeout completed, updating statistics');

    // Update statistics based on actual data
    completedClassesCount.value = completedClasses.value.length;
    totalClasses.value = classes.value.length;
    completedLessonsCount.value = completedLessons.value;
    averageScore.value = calculateAverageScore.value;

    // Calculate overall progress based on completed lessons vs total lessons
    if (totalLessons.value > 0) {
      overallProgress.value = Math.round((completedLessons.value / totalLessons.value) * 100);
    } else {
      overallProgress.value = 0;
    }

    // Hide loading state before initializing chart
    isLoading.value = false;

    // Make sure chart loading state is true before initializing
    isChartLoading.value = true;

    // Use nextTick to ensure the DOM is updated before initializing the chart
    nextTick(() => {
      console.log('DOM updated, initializing chart');
      initializeChart();

      // Only trigger progress bar animations if we have category data
      if (categoryPerformance.value.length > 0) {
        // Use a slightly longer delay to ensure chart is fully rendered first
        setTimeout(() => {
          animateProgress.value = true;
        }, 800);
      } else {
        // If no data, still set animate to true to avoid issues if data loads later
        animateProgress.value = true;
      }
    });
  }, 500); // Reduced timeout for better responsiveness
});

// Watch for changes in class data and update statistics
watch([classes, completedClasses, completedLessons, totalLessons], () => {
  console.log('Class data changed, updating statistics and chart');

  // Update statistics
  completedClassesCount.value = completedClasses.value.length;
  totalClasses.value = classes.value.length;
  completedLessonsCount.value = completedLessons.value;
  averageScore.value = calculateAverageScore.value;

  // Calculate overall progress based on completed lessons vs total lessons
  if (totalLessons.value > 0) {
    overallProgress.value = Math.round((completedLessons.value / totalLessons.value) * 100);
  } else {
    overallProgress.value = 0;
  }

  // Reset animation state when data changes
  animateProgress.value = false;

  // Update chart when class data changes
  if (chartInstance.value) {
    console.log('Updating existing chart instance');

    // Ensure we have some data to display
    const hasActiveClassData = monthlyClassData.value.activeClasses.some(value => value > 0);
    const hasLearningProgressData = monthlyClassData.value.learningProgress.some(value => value > 0);

    console.log('Chart data update check:', {
      hasActiveClassData,
      hasLearningProgressData,
      activeClasses: monthlyClassData.value.activeClasses,
      learningProgress: monthlyClassData.value.learningProgress
    });

    // Use actual data or empty arrays
    const activeClasses = hasActiveClassData ? monthlyClassData.value.activeClasses : Array(12).fill(0);
    const learningProgress = hasLearningProgressData ? monthlyClassData.value.learningProgress : Array(12).fill(0);

    // Update chart datasets
    chartInstance.value.data.datasets[0].data = activeClasses;
    chartInstance.value.data.datasets[1].data = learningProgress;

    // Update chart options for better responsiveness on small screens
    if (window.innerWidth < 640) { // sm breakpoint in Tailwind
      chartInstance.value.options.scales.x.ticks.maxRotation = 45;
      chartInstance.value.options.scales.x.ticks.minRotation = 45;
    } else {
      chartInstance.value.options.scales.x.ticks.maxRotation = 0;
      chartInstance.value.options.scales.x.ticks.minRotation = 0;
    }

    // Apply updates to the chart
    chartInstance.value.update();
    console.log('Chart updated successfully');

    // Make sure loading message is hidden
    isChartLoading.value = false;
  } else {
    console.log('No chart instance found, initializing new chart');
    // If chart instance doesn't exist, initialize it
    isChartLoading.value = true; // Show loading message
    nextTick(() => {
      initializeChart();
    });
  }

  // Re-trigger animations after a short delay
  setTimeout(() => {
    animateProgress.value = true;
  }, 300);
});

// Clean up chart instance and event listeners on component unmount
onUnmounted(() => {
  console.log('StudentStatistics component unmounting, cleaning up resources');

  // Reset chart loading state
  isChartLoading.value = true;

  if (chartInstance.value) {
    // Remove the resize event listener if it exists
    if (chartInstance.value._resizeListener) {
      console.log('Removing resize event listener');
      window.removeEventListener('resize', chartInstance.value._resizeListener);
    }

    try {
      // Destroy the chart instance
      console.log('Destroying chart instance');
      chartInstance.value.destroy();
    } catch (error) {
      console.error('Error destroying chart instance:', error);
    } finally {
      // Always set the chart instance to null to prevent memory leaks
      chartInstance.value = null;
    }
  }
});
</script>
