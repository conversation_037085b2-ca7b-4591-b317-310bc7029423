<template>
    <div class="p-6 space-y-6 max-w-6xl mx-auto">
      <!-- Breadcrumbs -->
      <div class="text-sm text-gray-600 flex items-center gap-2">
        <router-link to="/AllClasses" class="text-primary hover:underline font-semibold">Class Camp</router-link>
        <span>&gt;</span>
        <span class="text-gray-900 font-semibold">Design UI/UX dengan menggunakan Figma</span>
      </div>

      <!-- Progress Box -->
      <div class="bg-white p-6 rounded-lg border shadow flex items-start gap-4">
        <img src="/progress.png" alt="Progress Icon" class="w-6 h-6 mt-1" />
        <div class="flex-1">
          <p class="text-primary font-semibold mb-2">Your class is still in progress!</p>
          <p class="text-sm text-gray-500 mb-2">15 Modul</p>
          <div class="h-2 rounded bg-gray-300 overflow-hidden">
            <div class="h-full bg-primary w-3/4"></div>
          </div>
          <p class="text-sm text-right mt-1 text-gray-700">75%</p>
        </div>
      </div>

      <!-- Modules & Rating -->
      <div class="grid md:grid-cols-2 gap-4">
        <!-- Modul -->
        <div class="bg-white p-6 rounded-lg border shadow space-y-3 flex flex-col">
          <div class="flex gap-2 items-center mb-2">
            <img src="/folder.png" class="w-5 h-5" />
            <h2 class="text-base font-semibold">Modul</h2>
          </div>
          <p class="text-xs text-gray-500 mb-3">Access all completed structured modules</p>

          <!-- Manual Module Cards -->
          <div class="space-y-3 flex-1">
            <div class="bg-gray-100 rounded-md shadow px-5 py-4 text-sm text-gray-800 space-y-1">
              <h3 class="font-semibold text-base">Pengenalan Golang & Setup</h3>
              <p class="text-sm text-gray-600">Modul ini membahas dasar-dasar Golang serta cara setup lingkungan development.</p>
            </div>
            <div class="bg-gray-100 rounded-md shadow px-5 py-4 text-sm text-gray-800 space-y-1">
              <h3 class="font-semibold text-base">Struktur Data dalam Golang</h3>
              <p class="text-sm text-gray-600">Pelajari slice, map, array, dan bagaimana penggunaannya dalam program Golang.</p>
            </div>
            <div class="bg-gray-100 rounded-md shadow px-5 py-4 text-sm text-gray-800 space-y-1">
              <h3 class="font-semibold text-base">Penerapan OOP dalam Golang</h3>
              <p class="text-sm text-gray-600">Mengenal struct, interface, dan bagaimana konsep OOP diterapkan di Golang.</p>
            </div>
          </div>

          <div class="mt-auto pt-4">
            <button class="bg-primary text-white rounded px-4 py-2 text-sm">See More</button>
          </div>
        </div>

        <!-- Rating & Comment -->
        <div class="bg-white p-6 rounded-lg border shadow flex flex-col">
          <div class="flex gap-2 items-center mb-2">
            <img src="/comment.png" class="w-5 h-5" />
            <h2 class="text-base font-semibold">Rating & Comment</h2>
          </div>
          <p class="text-sm text-gray-500 mt-3">You have no rating & comment yet.</p>
        </div>
      </div>
    </div>
  </template>

  <script setup>
  import { useRoute } from 'vue-router'

  const route = useRoute()
  const classTitle = route.params.title

  // Dummy data
  const allClassData = [
    {
      title: "Mastery In Golang: From Zero to Hero",
      image: "/mentorCard1.png",
      modules: 20,
      completedModules: 15,
      students: 21342,
      status: "studied",
      moduleTitles: [
  {
    title: "Pengenalan Golang & Setup",
    content: "Modul ini membahas dasar-dasar Golang serta cara setup lingkungan development."
  },
  {
    title: "Struktur Data dalam Golang",
    content: "Pelajari slice, map, array, dan bagaimana penggunaannya dalam program Golang."
  },
  {
    title: "Penerapan OOP dalam Golang",
    content: "Mengenal struct, interface, dan bagaimana konsep OOP diterapkan di Golang."
  },
]
,
      comments: []
    }
  ]

  const classData = allClassData.find(c => c.title === classTitle) || {}
  const modules = classData.moduleTitles || []
  const progressPercent = Math.round((classData.completedModules / classData.modules) * 100)
  </script>
