<template>
  <component
    :is="isLink ? 'router-link' : 'div'"
    :to="linkTarget"
    class="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-transform duration-200 w-full max-w-[360px] mx-auto flex flex-col h-[520px] cursor-pointer hover:-translate-y-1 border border-gray-100"
  >
    <!-- IMAGE -->
    <div class="flex justify-center items-center px-4 pt-5 bg-white" style="height:260px;">
      <img :src="data.image" :alt="data.title" class="h-full max-h-[240px] w-auto object-contain rounded-xl shadow-sm mx-auto" @error="e => e.target.src = '/mentorCard1.png'" />
    </div>

    <!-- TITLE -->
    <div class="px-4 mt-4 text-center">
      <h3 class="text-xl font-bold text-gray-900 leading-snug line-clamp-2 mb-3">
        {{ data.title }}
      </h3>
      <div
        v-if="data.status === 'studied'"
        class="mt-1 bg-yellow-100 text-yellow-700 text-xs font-semibold px-3 py-1 rounded inline-block"
      >
        Kelas sedang berlangsung
      </div>
    </div>

    <!-- INFO BOX -->
    <div class="mt-auto border-t border-gray-100 px-4 py-3 text-sm text-gray-700">
      <div class="flex items-center justify-center gap-6">
        <!-- Rating -->
        <div v-if="data.rating" class="flex items-center gap-1 text-yellow-500 font-semibold">
          <img src="/star.png" class="w-4 h-4" />
          <span>{{ data.rating }}</span>
          <span class="text-gray-500 font-normal ml-1">Rating</span>
        </div>
        <!-- Modul -->
        <div class="flex items-center gap-1">
          <img src="/file.png" class="w-4 h-4" />
          <span>{{ data.modules }}</span>
          <span class="text-gray-500 font-normal ml-1">Modul</span>
        </div>
        <!-- Student -->
        <div class="flex items-center gap-1">
          <img src="/user.png" class="w-4 h-4" />
          <span>{{ data.students.toLocaleString() }}</span>
          <span class="text-gray-500 font-normal ml-1">Student</span>
        </div>
      </div>
    </div>
  </component>
</template>

<script setup>
const props = defineProps({
  data: {
    type: Object,
    required: true
  }
})

const isLink = ['studied', 'completed'].includes(props.data.status)

const linkTarget =
  props.data.status === 'studied'
    ? { name: 'DetailClassProgress', params: { title: props.data.title } }
    : props.data.status === 'completed'
    ? { name: 'DetailClassCompleted', params: { title: props.data.title } }
    : null
</script>
