<template>
    <button class="flex items-center justify-center space-x-2 px-4 sm:px-8 md:px-12 py-2 bg-white border border-gray-300 rounded-md shadow-md hover:bg-gray-100 active:bg-gray-200 focus:outline-none w-full">
      <!-- <PERSON><PERSON> TailwindCSS menyediakan ikon Google -->
      <span class="text-2xl">
        <i class="fab fa-google"></i>
      </span>

      <!-- <PERSON><PERSON> tidak, gunakan gambar -->
      <img v-if="!hasGoogleIcon" src="/Google.png" alt="Google Logo" class="w-6 h-6" />

      <span class="text-black font-semibold">Google</span>
    </button>
  </template>

  <script>
  export default {
    data() {
      return {
        // Mengecek apakah icon google tersedia di fontawesome
        hasGoogleIcon: typeof window !== 'undefined' && window.FontAwesome && window.FontAwesome.icons['google'] !== undefined
      };
    }
  }
  </script>

  <style scoped>
  /* Custom CSS jika perlu */
  </style>
