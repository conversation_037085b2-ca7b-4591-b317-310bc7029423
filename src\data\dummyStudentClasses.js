/**
 * Dummy Student Classes Data
 *
 * This file contains dummy data for classes that students have joined,
 * including both studied and completed classes.
 */

// Get current date for joined dates
const now = new Date();
const year = now.getFullYear();
const month = String(now.getMonth() + 1).padStart(2, '0');
const day = String(now.getDate()).padStart(2, '0');
const hours = String(now.getHours()).padStart(2, '0');
const minutes = String(now.getMinutes()).padStart(2, '0');
const currentDate = `${year}-${month}-${day}`;
const currentTime = `${hours}:${minutes}`;

// Note: We're now using manually defined materials for each class
// instead of generating them with a function

// Dummy data for classes being studied
export const studiedClassesData = [
  {
    id: 1001,
    title: "Mastery In Golang: From Zero to Hero",
    rating: 4.8,
    studentsEnrolled: 50,
    modules: 6,
    category: "backend",
    description: "Master the Go programming language from basics to advanced concepts. Learn to build efficient and scalable applications with Go's powerful features. This comprehensive course covers everything from basic syntax to complex architectural patterns, concurrency models, and microservices development.",
    postedDate: "2025-04-10",
    postedTime: "10:00 AM",
    imageUrl: "/class-golang.png",
    joinedDate: currentDate,
    joinedTime: currentTime,
    status: "studied",
    progress: 33,
    materials: [
      {
        id: 100101,
        title: "Introduction to Golang",
        description: "Learn the basics of Go programming language",
        content: "Go is a statically typed, compiled programming language designed at Google. It is syntactically similar to C, but with memory safety, garbage collection, structural typing, and CSP-style concurrency. This module will introduce you to the basics of Go programming.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "reviewed",
        taskDescription: "Create a simple Go program that prints 'Hello, World!' and demonstrates basic variable declarations.",
        postedDate: currentDate,
        postedTime: "08:30 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "10:15 AM",
        feedback: "Great work! Your Go program demonstrates a good understanding of the basic syntax.",
        taskScore: 92,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "01:45 PM",
        objectives: [
          "Understand the Go programming language and its features",
          "Learn how to set up a Go development environment",
          "Write and run your first Go program"
        ],
        topics: [
          "Introduction to Go",
          "Setting Up the Go Environment",
          "Basic Syntax and Data Types"
        ],
        files: {
          presentation: "abc.com",
          video: "youtube.com"
        }
      },
      {
        id: 100102,
        title: "Control Structures and Functions",
        description: "Master control structures and functions in Go",
        content: "Control structures and functions are fundamental building blocks of any program. In this module, you'll learn how to use control structures like if, for, and switch, as well as how to define and call functions in Go.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "reviewed",
        taskDescription: "Implement various control structures and functions to solve algorithmic problems in Go.",
        postedDate: currentDate,
        postedTime: "08:45 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "10:30 AM",
        feedback: "Excellent implementation of control structures and functions. Code is clean and well-documented.",
        taskScore: 95,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "02:00 PM",
        objectives: [
          "Understand control structures in Go",
          "Learn how to define and use functions",
          "Implement error handling in Go functions"
        ],
        topics: [
          "Control Structures (if, for, switch)",
          "Functions and Methods",
          "Error Handling"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100103,
        title: "Data Structures in Go",
        description: "Explore various data structures in Go",
        content: "Go provides several built-in data structures like arrays, slices, maps, and structs. In this module, you'll learn how to use these data structures effectively in your Go programs.",
        isComplete: false,
        isRead: true,
        hasTask: true,
        taskStatus: "under_review",
        taskDescription: "Implement a program that demonstrates the use of arrays, slices, maps, and structs in Go.",
        postedDate: currentDate,
        postedTime: "09:00 AM",
        dueDate: `${year}-${Number(month) + 1}-${day}`,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "02:30 PM",
        objectives: [
          "Understand arrays and slices in Go",
          "Learn how to use maps for key-value pairs",
          "Implement structs for custom data types"
        ],
        topics: [
          "Arrays and Slices",
          "Maps",
          "Structs and Interfaces"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100104,
        title: "Concurrency in Go",
        description: "Master Go's powerful concurrency features",
        content: "Concurrency is one of Go's strongest features. In this module, you'll learn how to use goroutines and channels to write concurrent programs in Go.",
        isComplete: false,
        isRead: false,
        hasTask: true,
        taskStatus: "pending",
        taskDescription: "Implement a concurrent web scraper using goroutines and channels.",
        postedDate: currentDate,
        postedTime: "09:15 AM",
        dueDate: `${year}-${Number(month) + 1}-${day}`,
        dueTime: "11:59 PM",
        objectives: [
          "Understand concurrency and parallelism",
          "Learn how to use goroutines for concurrent execution",
          "Implement channels for communication between goroutines"
        ],
        topics: [
          "Concurrency vs. Parallelism",
          "Goroutines",
          "Channels and Select"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100105,
        title: "Web Development with Go",
        description: "Build web applications and APIs with Go",
        content: "Go's standard library provides everything you need to build web applications and APIs. In this module, you'll learn how to use the net/http package to build web servers and how to use popular frameworks like Gin and Echo.",
        isComplete: false,
        isRead: false,
        hasTask: true,
        taskStatus: "pending",
        taskDescription: "Create a RESTful API with Go using the standard library.",
        postedDate: currentDate,
        postedTime: "09:30 AM",
        dueDate: `${year}-${Number(month) + 1}-${day}`,
        dueTime: "11:59 PM",
        objectives: [
          "Understand web development concepts in Go",
          "Learn how to use the net/http packag e",
          "Implement RESTful APIs with Go"
        ],
        topics: [
          "Web Servers with net/http",
          "Routing and Middleware",
          "RESTful API Development"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100106,
        title: "Advanced Go Concepts",
        description: "Explore advanced concepts in Go programming",
        content: "This module covers advanced Go concepts like reflection, code generation, and performance optimization. You'll learn how to write more efficient and maintainable Go code.",
        isComplete: false,
        isRead: false,
        hasTask: true,
        taskStatus: "past_due",
        taskDescription: "Implement a production-ready Go application implementing advanced concepts and best practices.",
        postedDate: currentDate,
        postedTime: "09:45 AM",
        dueDate: `${year}-${Number(month) + 1}-${day}`,
        dueTime: "11:59 PM",
        objectives: [
          "Understand advanced Go concepts",
          "Learn how to optimize Go code for performance",
          "Implement best practices for Go development"
        ],
        topics: [
          "Reflection and Interfaces",
          "Code Generation",
          "Performance Optimization"
        ],
        files: {
          presentation: null,
          video: null
        }
      }
    ]
  },
  {
    id: 1003,
    title: "Node.js: Building Scalable Backend Services",
    rating: 4.6,
    studentsEnrolled: 92,
    modules: 5,
    category: "backend",
    description: "Learn to build high-performance, scalable APIs with Node.js. Master Express.js, authentication, database integration, and deployment strategies. This course combines theoretical knowledge with practical applications to prepare you for real-world backend development challenges.",
    postedDate: "2025-04-18",
    postedTime: "9:45 AM",
    imageUrl: "/class-business.png",
    joinedDate: currentDate,
    joinedTime: currentTime,
    status: "studied",
    progress: 40,
    materials: [
      {
        id: 100301,
        title: "Introduction to Node.js",
        description: "Learn the basics of Node.js and its architecture",
        content: "Node.js is a JavaScript runtime built on Chrome's V8 JavaScript engine. It uses an event-driven, non-blocking I/O model that makes it lightweight and efficient. In this module, you'll learn the fundamentals of Node.js and how it works under the hood.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "Reviewed",
        taskDescription: "Create a simple Node.js server that responds with 'Hello, World!' on the root route.",
        postedDate: currentDate,
        postedTime: "09:00 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "10:30 AM",
        feedback: "Excellent work! Your server implementation is clean and follows best practices.",
        taskScore: 95,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "02:15 PM",
        objectives: [
          "Understand the Node.js architecture and event loop",
          "Learn how to create a basic Node.js server",
          "Understand the module system in Node.js"
        ],
        topics: [
          "Introduction to Node.js",
          "Event Loop and Asynchronous Programming",
          "Creating a Basic HTTP Server"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100302,
        title: "Express.js Framework",
        description: "Master Express.js for building web applications with Node.js",
        content: "Express.js is a minimal and flexible Node.js web application framework that provides a robust set of features for web and mobile applications. In this module, you'll learn how to use Express.js to build RESTful APIs and web applications.",
        isComplete: true,
        isRead: true,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "09:15 AM",
        objectives: [
          "Understand the Express.js framework and its features",
          "Learn how to create routes and middleware",
          "Build a RESTful API with Express.js"
        ],
        topics: [
          "Introduction to Express.js",
          "Routing and Middleware",
          "Building RESTful APIs"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100303,
        title: "Database Integration with MongoDB",
        description: "Learn how to integrate MongoDB with Node.js applications",
        content: "MongoDB is a popular NoSQL database that works well with Node.js. In this module, you'll learn how to integrate MongoDB with your Node.js applications using Mongoose, an elegant MongoDB object modeling for Node.js.",
        isComplete: false,
        isRead: true,
        hasTask: true,
        taskStatus: "past_due",
        taskDescription: "Create a REST API that performs CRUD operations on a MongoDB database.",
        postedDate: currentDate,
        postedTime: "09:30 AM",
        dueDate: `${year}-${Number(month) + 1}-${day}`,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "03:45 PM",
        objectives: [
          "Understand MongoDB and its features",
          "Learn how to use Mongoose for object modeling",
          "Build a CRUD API with Node.js and MongoDB"
        ],
        topics: [
          "Introduction to MongoDB",
          "Mongoose ODM",
          "Building a CRUD API"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100304,
        title: "Authentication and Authorization",
        description: "Implement user authentication and authorization in Node.js applications",
        content: "Security is a critical aspect of web applications. In this module, you'll learn how to implement user authentication and authorization in your Node.js applications using JSON Web Tokens (JWT) and Passport.js.",
        isComplete: false,
        isRead: false,
        hasTask: true,
        taskStatus: "pending",
        taskDescription: "Implement JWT authentication in your REST API.",
        postedDate: currentDate,
        postedTime: "09:45 AM",
        dueDate: `${year}-${Number(month) + 1}-${day}`,
        dueTime: "11:59 PM",
        objectives: [
          "Understand authentication and authorization concepts",
          "Learn how to use JWT for authentication",
          "Implement Passport.js for authentication strategies"
        ],
        topics: [
          "Authentication vs. Authorization",
          "JSON Web Tokens (JWT)",
          "Passport.js Authentication Strategies"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100305,
        title: "Deployment and Scaling",
        description: "Learn how to deploy and scale Node.js applications",
        content: "Deploying and scaling Node.js applications is essential for production environments. In this module, you'll learn how to deploy your Node.js applications to various platforms and how to scale them to handle high traffic.",
        isComplete: false,
        isRead: false,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "10:00 AM",
        objectives: [
          "Understand deployment options for Node.js applications",
          "Learn how to scale Node.js applications",
          "Implement best practices for production environments"
        ],
        topics: [
          "Deployment Options (Heroku, AWS, DigitalOcean)",
          "Scaling Strategies",
          "Production Best Practices"
        ],
        files: {
          presentation: null,
          video: null
        }
      }
    ]
  },
  {
    id: 1005,
    title: "Python for Data Science and Machine Learning",
    rating: 4.9,
    studentsEnrolled: 120,
    modules: 8,
    category: "backend",
    description: "Master Python for data analysis, visualization, and machine learning. Learn to use NumPy, Pandas, Matplotlib, and Scikit-Learn to analyze data and build predictive models. This course will equip you with the skills needed to pursue a career in data science.",
    postedDate: "2025-03-15",
    postedTime: "11:00 AM",
    imageUrl: "/class-data.png",
    joinedDate: currentDate,
    joinedTime: currentTime,
    status: "studied",
    progress: 25,
    materials: [
      {
        id: 100501,
        title: "Introduction to Python for Data Science",
        description: "Learn the basics of Python programming for data science",
        content: "Python is one of the most popular programming languages for data science and machine learning. In this module, you'll learn the basics of Python programming and how to use it for data analysis.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a Python script that reads a CSV file, performs basic data cleaning, and outputs summary statistics.",
        postedDate: currentDate,
        postedTime: "08:00 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "09:45 AM",
        feedback: "Excellent work! Your Python script demonstrates good understanding of data manipulation techniques.",
        taskScore: 90,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "01:30 PM",
        objectives: [
          "Understand the Python programming language and its ecosystem",
          "Learn how to use Python for data manipulation",
          "Implement basic data cleaning and analysis techniques"
        ],
        topics: [
          "Python Basics",
          "Data Types and Structures",
          "File I/O and Data Manipulation"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100502,
        title: "NumPy and Scientific Computing",
        description: "Master NumPy for numerical computing in Python",
        content: "NumPy is the fundamental package for scientific computing in Python. In this module, you'll learn how to use NumPy for efficient numerical computations and array operations.",
        isComplete: true,
        isRead: true,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "08:15 AM",
        objectives: [
          "Understand NumPy arrays and operations",
          "Learn how to perform efficient numerical computations",
          "Implement scientific computing algorithms with NumPy"
        ],
        topics: [
          "NumPy Arrays",
          "Array Operations and Broadcasting",
          "Linear Algebra and Random Number Generation"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100503,
        title: "Pandas for Data Analysis",
        description: "Learn how to use Pandas for data manipulation and analysis",
        content: "Pandas is a powerful data analysis and manipulation library for Python. In this module, you'll learn how to use Pandas to clean, transform, and analyze data.",
        isComplete: false,
        isRead: true,
        hasTask: true,
        taskStatus: "submitted",
        taskDescription: "Perform exploratory data analysis on a real-world dataset using Pandas.",
        postedDate: currentDate,
        postedTime: "08:30 AM",
        dueDate: `${year}-${Number(month) + 1}-${day}`,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "03:15 PM",
        objectives: [
          "Understand Pandas data structures (Series and DataFrame)",
          "Learn how to clean and transform data",
          "Implement exploratory data analysis techniques"
        ],
        topics: [
          "Pandas Data Structures",
          "Data Cleaning and Transformation",
          "Exploratory Data Analysis"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100504,
        title: "Data Visualization with Matplotlib and Seaborn",
        description: "Create stunning visualizations with Matplotlib and Seaborn",
        content: "Data visualization is a critical aspect of data analysis. In this module, you'll learn how to use Matplotlib and Seaborn to create informative and visually appealing plots.",
        isComplete: false,
        isRead: false,
        hasTask: true,
        taskStatus: "pending",
        taskDescription: "Create a comprehensive data visualization dashboard for a given dataset.",
        postedDate: currentDate,
        postedTime: "08:45 AM",
        dueDate: `${year}-${Number(month) + 1}-${day}`,
        dueTime: "11:59 PM",
        objectives: [
          "Understand data visualization principles",
          "Learn how to use Matplotlib for basic plotting",
          "Implement advanced visualizations with Seaborn"
        ],
        topics: [
          "Matplotlib Basics",
          "Seaborn for Statistical Visualization",
          "Creating Interactive Dashboards"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100505,
        title: "Machine Learning with Scikit-Learn",
        description: "Introduction to machine learning with Scikit-Learn",
        content: "Scikit-Learn is a simple and efficient tool for data mining and data analysis. In this module, you'll learn the basics of machine learning and how to implement various algorithms using Scikit-Learn.",
        isComplete: false,
        isRead: false,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "09:00 AM",
        objectives: [
          "Understand machine learning concepts",
          "Learn how to use Scikit-Learn for model building",
          "Implement supervised and unsupervised learning algorithms"
        ],
        topics: [
          "Introduction to Machine Learning",
          "Supervised Learning Algorithms",
          "Model Evaluation and Validation"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100506,
        title: "Deep Learning with TensorFlow and Keras",
        description: "Build neural networks with TensorFlow and Keras",
        content: "Deep learning is a subset of machine learning that uses neural networks with multiple layers. In this module, you'll learn how to build and train neural networks using TensorFlow and Keras.",
        isComplete: false,
        isRead: false,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "09:15 AM",
        objectives: [
          "Understand neural networks and deep learning",
          "Learn how to use TensorFlow and Keras",
          "Implement various neural network architectures"
        ],
        topics: [
          "Neural Networks Basics",
          "TensorFlow and Keras",
          "Convolutional and Recurrent Neural Networks"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100507,
        title: "Natural Language Processing",
        description: "Process and analyze text data with NLP techniques",
        content: "Natural Language Processing (NLP) is a field of AI that focuses on the interaction between computers and human language. In this module, you'll learn how to process and analyze text data using various NLP techniques.",
        isComplete: false,
        isRead: false,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "09:30 AM",
        objectives: [
          "Understand natural language processing concepts",
          "Learn how to preprocess text data",
          "Implement text classification and sentiment analysis"
        ],
        topics: [
          "Text Preprocessing",
          "Text Classification",
          "Sentiment Analysis"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100508,
        title: "Time Series Analysis and Forecasting",
        description: "Analyze and forecast time series data",
        content: "Time series analysis is a statistical technique that deals with time-ordered data points. In this module, you'll learn how to analyze and forecast time series data using various statistical and machine learning techniques.",
        isComplete: false,
        isRead: false,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "09:45 AM",
        objectives: [
          "Understand time series data and its characteristics",
          "Learn how to analyze time series data",
          "Implement forecasting models for time series data"
        ],
        topics: [
          "Time Series Decomposition",
          "ARIMA Models",
          "Prophet and Deep Learning for Time Series"
        ],
        files: {
          presentation: null,
          video: null
        }
      }
    ]
  },
  {
    id: 1007,
    title: "UI/UX Design for Frontend Developers",
    rating: 4.7,
    studentsEnrolled: 65,
    modules: 5,
    category: "frontend",
    description: "Learn the principles of effective UI/UX design and create stunning user interfaces. This course covers design theory, user research, wireframing, prototyping, and usability testing to help you create exceptional user experiences.",
    postedDate: "2025-04-12",
    postedTime: "10:15 AM",
    imageUrl: "/class-design.png",
    joinedDate: currentDate,
    joinedTime: currentTime,
    status: "studied",
    progress: 60,
    materials: [
      {
        id: 100701,
        title: "Principles of UI/UX Design",
        description: "Learn the fundamental principles of UI/UX design",
        content: "User Interface (UI) and User Experience (UX) design are critical aspects of modern web and mobile applications. In this module, you'll learn the fundamental principles of UI/UX design and how to apply them to your projects.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create wireframes for a simple e-commerce website following UI/UX best practices.",
        postedDate: currentDate,
        postedTime: "10:00 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "02:30 PM",
        feedback: "Excellent work! Your wireframes demonstrate a good understanding of UI/UX principles.",
        taskScore: 95,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "04:15 PM",
        objectives: [
          "Understand the difference between UI and UX",
          "Learn the fundamental principles of design",
          "Apply design thinking to solve user problems"
        ],
        topics: [
          "UI vs. UX",
          "Design Principles",
          "Design Thinking Process"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100702,
        title: "User Research and Personas",
        description: "Learn how to conduct user research and create personas",
        content: "User research is the foundation of effective UX design. In this module, you'll learn how to conduct user research and create personas to guide your design decisions.",
        isComplete: true,
        isRead: true,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "10:15 AM",
        objectives: [
          "Understand the importance of user research",
          "Learn different user research methods",
          "Create effective user personas"
        ],
        topics: [
          "User Research Methods",
          "User Interviews and Surveys",
          "Creating and Using Personas"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100703,
        title: "Wireframing and Prototyping",
        description: "Create wireframes and prototypes for web and mobile applications",
        content: "Wireframing and prototyping are essential steps in the design process. In this module, you'll learn how to create wireframes and prototypes for web and mobile applications using various tools.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a high-fidelity prototype for a mobile app using Figma or Adobe XD.",
        postedDate: currentDate,
        postedTime: "10:30 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "03:45 PM",
        feedback: "Great job! Your prototype is well-designed and demonstrates good interaction design principles.",
        taskScore: 90,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "05:00 PM",
        objectives: [
          "Understand the purpose of wireframing and prototyping",
          "Learn how to use design tools like Figma and Adobe XD",
          "Create effective wireframes and prototypes"
        ],
        topics: [
          "Wireframing Basics",
          "Prototyping Tools",
          "Interactive Prototypes"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100704,
        title: "Visual Design and Typography",
        description: "Learn the principles of visual design and typography",
        content: "Visual design and typography play a crucial role in creating aesthetically pleasing and readable interfaces. In this module, you'll learn the principles of visual design and typography and how to apply them to your designs.",
        isComplete: false,
        isRead: true,
        hasTask: true,
        taskStatus: "submitted",
        taskDescription: "Redesign a website homepage with a focus on typography and visual hierarchy.",
        postedDate: currentDate,
        postedTime: "10:45 AM",
        dueDate: `${year}-${Number(month) + 1}-${day}`,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "04:30 PM",
        objectives: [
          "Understand the principles of visual design",
          "Learn typography basics and best practices",
          "Apply visual hierarchy to improve usability"
        ],
        topics: [
          "Color Theory and Psychology",
          "Typography Fundamentals",
          "Visual Hierarchy and Layout"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100705,
        title: "Usability Testing and Iteration",
        description: "Learn how to conduct usability testing and iterate on your designs",
        content: "Usability testing is a critical part of the design process that helps identify issues and improve the user experience. In this module, you'll learn how to conduct usability testing and iterate on your designs based on user feedback.",
        isComplete: false,
        isRead: false,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "11:00 AM",
        objectives: [
          "Understand the importance of usability testing",
          "Learn different usability testing methods",
          "Apply user feedback to improve designs"
        ],
        topics: [
          "Usability Testing Methods",
          "Analyzing User Feedback",
          "Design Iteration Process"
        ],
        files: {
          presentation: null,
          video: null
        }
      }
    ]
  },
  {
    id: 1009,
    title: "Flutter: Cross-Platform Mobile Development",
    rating: 4.7,
    studentsEnrolled: 70,
    modules: 5,
    category: "mobile",
    description: "Learn to build beautiful, natively compiled applications for mobile, web, and desktop from a single codebase using Flutter. This course covers the entire mobile app development lifecycle, from initial concept to final deployment.",
    postedDate: "2025-04-22",
    postedTime: "1:15 PM",
    imageUrl: "/header.jpg",
    joinedDate: currentDate,
    joinedTime: currentTime,
    status: "studied",
    progress: 20,
    materials: [
      {
        id: 100901,
        title: "Introduction to Flutter",
        description: "Learn the basics of Flutter and Dart programming",
        content: "Flutter is Google's UI toolkit for building beautiful, natively compiled applications for mobile, web, and desktop from a single codebase. In this module, you'll learn the basics of Flutter and the Dart programming language.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a simple Flutter app that displays a list of items and allows navigation to a detail screen.",
        postedDate: currentDate,
        postedTime: "11:00 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "03:30 PM",
        feedback: "Great work! Your Flutter app demonstrates a good understanding of basic Flutter concepts.",
        taskScore: 88,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "05:15 PM",
        objectives: [
          "Understand Flutter and its advantages",
          "Learn the basics of Dart programming",
          "Create your first Flutter application"
        ],
        topics: [
          "Introduction to Flutter",
          "Dart Programming Basics",
          "Flutter Development Environment"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100902,
        title: "Flutter Widgets and UI",
        description: "Master Flutter widgets and UI components",
        content: "Widgets are the building blocks of Flutter applications. In this module, you'll learn about different types of widgets and how to use them to create beautiful user interfaces.",
        isComplete: false,
        isRead: true,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "11:15 AM",
        objectives: [
          "Understand the widget tree and widget lifecycle",
          "Learn about different types of widgets",
          "Create complex layouts with Flutter widgets"
        ],
        topics: [
          "Widget Tree and Lifecycle",
          "Stateless vs. Stateful Widgets",
          "Layout Widgets and UI Components"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100903,
        title: "State Management in Flutter",
        description: "Learn different state management approaches in Flutter",
        content: "State management is a critical aspect of Flutter development. In this module, you'll learn about different state management approaches and how to choose the right one for your application.",
        isComplete: false,
        isRead: false,
        hasTask: true,
        taskStatus: "pending",
        taskDescription: "Implement a Flutter app using Provider for state management.",
        postedDate: currentDate,
        postedTime: "11:30 AM",
        dueDate: `${year}-${Number(month) + 1}-${day}`,
        dueTime: "11:59 PM",
        objectives: [
          "Understand state management concepts",
          "Learn different state management approaches",
          "Implement Provider for state management"
        ],
        topics: [
          "State Management Concepts",
          "Provider Package",
          "BLoC Pattern"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100904,
        title: "Navigation and Routing",
        description: "Implement navigation and routing in Flutter applications",
        content: "Navigation and routing are essential for creating multi-screen applications. In this module, you'll learn how to implement navigation and routing in Flutter applications.",
        isComplete: false,
        isRead: false,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "11:45 AM",
        objectives: [
          "Understand navigation concepts in Flutter",
          "Learn how to use Navigator 1.0 and 2.0",
          "Implement deep linking and web URLs"
        ],
        topics: [
          "Navigator 1.0",
          "Navigator 2.0 and Router",
          "Deep Linking and Web URLs"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100905,
        title: "Flutter Animations",
        description: "Create beautiful animations in Flutter",
        content: "Animations can enhance the user experience of your application. In this module, you'll learn how to create beautiful animations in Flutter using various animation controllers and widgets.",
        isComplete: false,
        isRead: false,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "12:00 PM",
        objectives: [
          "Understand animation concepts in Flutter",
          "Learn how to use AnimationController and Tween",
          "Implement complex animations with Hero and Staggered Animations"
        ],
        topics: [
          "Animation Basics",
          "Implicit and Explicit Animations",
          "Hero and Staggered Animations"
        ],
        files: {
          presentation: null,
          video: null
        }
      }
    ]
  },
  {
    id: 1011,
    title: "Docker and Kubernetes for DevOps",
    rating: 4.8,
    studentsEnrolled: 85,
    modules: 6,
    category: "backend",
    description: "Master containerization with Docker and orchestration with Kubernetes. Learn to deploy, scale, and manage containerized applications in production environments. This course will prepare you for a career in DevOps and cloud engineering.",
    postedDate: "2025-03-25",
    postedTime: "9:30 AM",
    imageUrl: "/class-golang.png",
    joinedDate: currentDate,
    joinedTime: currentTime,
    status: "studied",
    progress: 50,
    materials: [
      {
        id: 101101,
        title: "Introduction to DevOps",
        description: "Learn the fundamentals of DevOps practices",
        content: "DevOps is a set of practices that combines software development and IT operations. It aims to shorten the systems development life cycle and provide continuous delivery with high software quality. This module introduces you to the core concepts of DevOps.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a document outlining DevOps practices for a fictional company.",
        postedDate: currentDate,
        postedTime: "9:00 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "2:30 PM",
        feedback: "Excellent work! Your understanding of DevOps principles is solid.",
        taskScore: 92,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "4:15 PM",
        objectives: [
          "Understand DevOps principles and practices",
          "Learn about the DevOps culture and mindset",
          "Explore the DevOps lifecycle"
        ],
        topics: [
          "Introduction to DevOps",
          "DevOps Culture and Mindset",
          "DevOps Lifecycle"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101102,
        title: "Docker Fundamentals",
        description: "Learn the basics of containerization with Docker",
        content: "Docker is a platform for developing, shipping, and running applications in containers. In this module, you'll learn the fundamentals of Docker and how to use it to containerize applications.",
        isComplete: true,
        isRead: true,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "9:15 AM",
        objectives: [
          "Understand containerization concepts",
          "Learn Docker architecture and components",
          "Create and manage Docker containers"
        ],
        topics: [
          "Containerization Basics",
          "Docker Architecture",
          "Docker Commands and Workflow"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101103,
        title: "Docker Compose and Networking",
        description: "Learn to manage multi-container applications with Docker Compose",
        content: "Docker Compose is a tool for defining and running multi-container Docker applications. In this module, you'll learn how to use Docker Compose to manage complex applications and how Docker networking works.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a Docker Compose file for a multi-container application.",
        postedDate: currentDate,
        postedTime: "9:30 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:45 PM",
        feedback: "Great job! Your Docker Compose file is well-structured and follows best practices.",
        taskScore: 90,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:00 PM",
        objectives: [
          "Understand Docker Compose syntax and usage",
          "Learn Docker networking concepts",
          "Implement multi-container applications"
        ],
        topics: [
          "Docker Compose",
          "Docker Networking",
          "Multi-Container Applications"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101104,
        title: "Kubernetes Fundamentals",
        description: "Introduction to container orchestration with Kubernetes",
        content: "Kubernetes is an open-source container orchestration platform for automating deployment, scaling, and management of containerized applications. In this module, you'll learn the fundamentals of Kubernetes.",
        isComplete: false,
        isRead: true,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "9:45 AM",
        objectives: [
          "Understand Kubernetes architecture",
          "Learn Kubernetes objects and resources",
          "Deploy applications to Kubernetes"
        ],
        topics: [
          "Kubernetes Architecture",
          "Kubernetes Objects",
          "Deploying to Kubernetes"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101105,
        title: "Kubernetes Services and Networking",
        description: "Learn about Kubernetes services and networking",
        content: "Kubernetes services enable communication between different parts of your application. In this module, you'll learn about Kubernetes services, networking, and how to expose your applications.",
        isComplete: false,
        isRead: false,
        hasTask: true,
        taskStatus: "pending",
        taskDescription: "Configure Kubernetes services for a sample application.",
        postedDate: currentDate,
        postedTime: "10:00 AM",
        dueDate: `${year}-${Number(month) + 1}-${day}`,
        dueTime: "11:59 PM",
        objectives: [
          "Understand Kubernetes services",
          "Learn Kubernetes networking concepts",
          "Implement service discovery and load balancing"
        ],
        topics: [
          "Kubernetes Services",
          "Kubernetes Networking",
          "Service Discovery and Load Balancing"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101106,
        title: "CI/CD with Kubernetes",
        description: "Implement continuous integration and deployment with Kubernetes",
        content: "Continuous Integration and Continuous Deployment (CI/CD) are essential practices in DevOps. In this module, you'll learn how to implement CI/CD pipelines for Kubernetes applications.",
        isComplete: false,
        isRead: false,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "10:15 AM",
        objectives: [
          "Understand CI/CD concepts",
          "Learn how to implement CI/CD for Kubernetes",
          "Explore GitOps workflows"
        ],
        topics: [
          "CI/CD Basics",
          "CI/CD for Kubernetes",
          "GitOps Workflows"
        ],
        files: {
          presentation: null,
          video: null
        }
      }
    ]
  },
  {
    id: 1013,
    title: "GraphQL: Modern API Development",
    rating: 4.6,
    studentsEnrolled: 60,
    modules: 4,
    category: "backend",
    description: "Learn to build efficient APIs with GraphQL. This course covers schema design, resolvers, mutations, and integration with various backend technologies. You'll master the skills needed to create flexible and performant APIs for modern applications.",
    postedDate: "2025-04-05",
    postedTime: "2:00 PM",
    imageUrl: "/class-business.png",
    joinedDate: currentDate,
    joinedTime: currentTime,
    status: "studied",
    progress: 75,
    materials: [
      {
        id: 101301,
        title: "Introduction to GraphQL",
        description: "Learn the basics of GraphQL and its advantages over REST",
        content: "GraphQL is a query language for APIs and a runtime for executing those queries with your existing data. In this module, you'll learn the basics of GraphQL and why it's becoming a popular alternative to REST APIs.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a simple GraphQL schema for a blog application.",
        postedDate: currentDate,
        postedTime: "10:00 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:30 PM",
        feedback: "Excellent work! Your GraphQL schema is well-structured and follows best practices.",
        taskScore: 95,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:15 PM",
        objectives: [
          "Understand GraphQL and its advantages over REST",
          "Learn GraphQL core concepts",
          "Create your first GraphQL schema"
        ],
        topics: [
          "Introduction to GraphQL",
          "GraphQL vs. REST",
          "GraphQL Schema Basics"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101302,
        title: "GraphQL Queries and Resolvers",
        description: "Master GraphQL queries and resolvers",
        content: "GraphQL queries allow clients to request exactly the data they need. Resolvers are functions that return the data for a specific field. In this module, you'll learn how to write GraphQL queries and implement resolvers.",
        isComplete: true,
        isRead: true,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "10:15 AM",
        objectives: [
          "Understand GraphQL queries and their structure",
          "Learn how to implement resolvers",
          "Master query variables and directives"
        ],
        topics: [
          "GraphQL Queries",
          "Resolvers",
          "Query Variables and Directives"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101303,
        title: "GraphQL Mutations and Subscriptions",
        description: "Learn to modify data with mutations and implement real-time updates with subscriptions",
        content: "GraphQL mutations allow clients to modify data on the server. Subscriptions enable real-time updates. In this module, you'll learn how to implement mutations and subscriptions in your GraphQL API.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Implement mutations and subscriptions for a chat application.",
        postedDate: currentDate,
        postedTime: "10:30 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "4:15 PM",
        feedback: "Great job! Your implementation of mutations and subscriptions is clean and efficient.",
        taskScore: 90,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "6:00 PM",
        objectives: [
          "Understand GraphQL mutations",
          "Learn how to implement subscriptions",
          "Master error handling in GraphQL"
        ],
        topics: [
          "GraphQL Mutations",
          "GraphQL Subscriptions",
          "Error Handling"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101304,
        title: "GraphQL with Node.js and Apollo",
        description: "Build a complete GraphQL API with Node.js and Apollo Server",
        content: "Apollo Server is a community-driven GraphQL server that works with any GraphQL schema. In this module, you'll learn how to build a complete GraphQL API using Node.js and Apollo Server.",
        isComplete: false,
        isRead: true,
        hasTask: true,
        taskStatus: "pending",
        taskDescription: "Build a complete GraphQL API for a bookstore application.",
        postedDate: currentDate,
        postedTime: "10:45 AM",
        dueDate: `${year}-${Number(month) + 1}-${day}`,
        dueTime: "11:59 PM",
        objectives: [
          "Understand Apollo Server",
          "Learn how to integrate GraphQL with Node.js",
          "Implement authentication and authorization in GraphQL"
        ],
        topics: [
          "Apollo Server",
          "GraphQL with Node.js",
          "Authentication and Authorization"
        ],
        files: {
          presentation: null,
          video: null
        }
      }
    ]
  },
  {
    id: 1015,
    title: "Responsive Web Design with CSS Grid and Flexbox",
    rating: 4.7,
    studentsEnrolled: 95,
    modules: 5,
    category: "frontend",
    description: "Master modern CSS layout techniques with Grid and Flexbox. Learn to create responsive, accessible, and beautiful web layouts that work across all devices. This course will transform your approach to web design and development.",
    postedDate: "2025-03-10",
    postedTime: "10:45 AM",
    imageUrl: "/class-design.png",
    joinedDate: currentDate,
    joinedTime: currentTime,
    status: "studied",
    progress: 40,
    materials: [
      {
        id: 101501,
        title: "Introduction to Modern CSS Layout",
        description: "Learn the evolution of CSS layout techniques",
        content: "CSS layout has evolved significantly over the years. In this module, you'll learn about the history of CSS layout techniques and why Grid and Flexbox are revolutionary for web design.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a comparison document of different CSS layout techniques.",
        postedDate: currentDate,
        postedTime: "11:00 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:00 PM",
        feedback: "Excellent work! Your comparison of layout techniques is comprehensive and insightful.",
        taskScore: 94,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:00 PM",
        objectives: [
          "Understand the evolution of CSS layout",
          "Learn the limitations of traditional layout techniques",
          "Explore the benefits of modern layout methods"
        ],
        topics: [
          "CSS Layout History",
          "Traditional Layout Techniques",
          "Introduction to Modern Layout"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101502,
        title: "CSS Flexbox Fundamentals",
        description: "Master the basics of CSS Flexbox",
        content: "Flexbox is a one-dimensional layout method designed for laying out items in rows or columns. In this module, you'll learn the fundamentals of Flexbox and how to use it for various layout scenarios.",
        isComplete: true,
        isRead: true,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "11:15 AM",
        objectives: [
          "Understand Flexbox concepts and terminology",
          "Learn how to create flexible layouts with Flexbox",
          "Master Flexbox properties for containers and items"
        ],
        topics: [
          "Flexbox Concepts",
          "Flex Container Properties",
          "Flex Item Properties"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101503,
        title: "Advanced Flexbox Techniques",
        description: "Learn advanced Flexbox techniques for complex layouts",
        content: "Flexbox can be used to create complex layouts with nested flex containers. In this module, you'll learn advanced Flexbox techniques and how to solve common layout challenges.",
        isComplete: false,
        isRead: true,
        hasTask: true,
        taskStatus: "pending",
        taskDescription: "Create a responsive navigation bar using Flexbox.",
        postedDate: currentDate,
        postedTime: "11:30 AM",
        dueDate: `${year}-${Number(month) + 1}-${day}`,
        dueTime: "11:59 PM",
        objectives: [
          "Understand nested flex containers",
          "Learn how to create complex layouts with Flexbox",
          "Implement responsive designs with Flexbox"
        ],
        topics: [
          "Nested Flex Containers",
          "Complex Flexbox Layouts",
          "Responsive Design with Flexbox"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101504,
        title: "CSS Grid Fundamentals",
        description: "Master the basics of CSS Grid",
        content: "CSS Grid is a two-dimensional layout system designed for laying out items in rows and columns. In this module, you'll learn the fundamentals of CSS Grid and how to use it for various layout scenarios.",
        isComplete: false,
        isRead: false,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "11:45 AM",
        objectives: [
          "Understand Grid concepts and terminology",
          "Learn how to create grid layouts",
          "Master Grid properties for containers and items"
        ],
        topics: [
          "Grid Concepts",
          "Grid Container Properties",
          "Grid Item Properties"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101505,
        title: "Advanced Grid Techniques and Combining Grid with Flexbox",
        description: "Learn advanced Grid techniques and how to combine Grid with Flexbox",
        content: "CSS Grid and Flexbox can be used together to create powerful layouts. In this module, you'll learn advanced Grid techniques and how to combine Grid with Flexbox for optimal layouts.",
        isComplete: false,
        isRead: false,
        hasTask: true,
        taskStatus: "pending",
        taskDescription: "Create a responsive website layout using both Grid and Flexbox.",
        postedDate: currentDate,
        postedTime: "12:00 PM",
        dueDate: `${year}-${Number(month) + 1}-${day}`,
        dueTime: "11:59 PM",
        objectives: [
          "Understand advanced Grid techniques",
          "Learn when to use Grid vs. Flexbox",
          "Implement layouts that combine Grid and Flexbox"
        ],
        topics: [
          "Advanced Grid Techniques",
          "Grid vs. Flexbox",
          "Combining Grid and Flexbox"
        ],
        files: {
          presentation: null,
          video: null
        }
      }
    ]
  },
  {
    id: 1017,
    title: "TypeScript: Type-Safe JavaScript Development",
    rating: 4.8,
    studentsEnrolled: 75,
    modules: 6,
    category: "frontend",
    description: "Learn to write safer, more maintainable JavaScript code with TypeScript. This course covers type systems, interfaces, generics, and advanced TypeScript features. You'll gain the skills needed to build robust applications with fewer bugs.",
    postedDate: "2025-04-15",
    postedTime: "11:15 AM",
    imageUrl: "/bootcampHero.png",
    joinedDate: currentDate,
    joinedTime: currentTime,
    status: "studied",
    progress: 15,
    materials: [
      {
        id: 101701,
        title: "Introduction to TypeScript",
        description: "Learn the basics of TypeScript and its advantages over JavaScript",
        content: "TypeScript is a typed superset of JavaScript that compiles to plain JavaScript. In this module, you'll learn the basics of TypeScript and why it's becoming increasingly popular for large-scale JavaScript applications.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Set up a TypeScript project and convert a simple JavaScript application to TypeScript.",
        postedDate: currentDate,
        postedTime: "1:00 PM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "4:30 PM",
        feedback: "Excellent work! Your TypeScript conversion is clean and follows best practices.",
        taskScore: 95,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "6:15 PM",
        objectives: [
          "Understand TypeScript and its advantages over JavaScript",
          "Learn how to set up a TypeScript project",
          "Convert JavaScript code to TypeScript"
        ],
        topics: [
          "Introduction to TypeScript",
          "TypeScript vs. JavaScript",
          "Setting Up a TypeScript Project"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101702,
        title: "TypeScript Basic Types",
        description: "Learn about TypeScript's basic types and type annotations",
        content: "TypeScript provides several basic types that you can use to make your code more type-safe. In this module, you'll learn about TypeScript's basic types and how to use type annotations.",
        isComplete: false,
        isRead: true,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "1:15 PM",
        objectives: [
          "Understand TypeScript's basic types",
          "Learn how to use type annotations",
          "Implement type checking in your code"
        ],
        topics: [
          "Basic Types",
          "Type Annotations",
          "Type Inference"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101703,
        title: "Interfaces and Type Aliases",
        description: "Master TypeScript interfaces and type aliases",
        content: "Interfaces and type aliases are powerful features in TypeScript that allow you to define custom types. In this module, you'll learn how to use interfaces and type aliases to create complex types.",
        isComplete: false,
        isRead: false,
        hasTask: true,
        taskStatus: "pending",
        taskDescription: "Create interfaces and type aliases for a web application's data models.",
        postedDate: currentDate,
        postedTime: "1:30 PM",
        dueDate: `${year}-${Number(month) + 1}-${day}`,
        dueTime: "11:59 PM",
        objectives: [
          "Understand interfaces in TypeScript",
          "Learn how to use type aliases",
          "Implement complex types in your code"
        ],
        topics: [
          "Interfaces",
          "Type Aliases",
          "Complex Types"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101704,
        title: "Functions and Classes in TypeScript",
        description: "Learn how to use TypeScript with functions and classes",
        content: "TypeScript enhances JavaScript functions and classes with type annotations and additional features. In this module, you'll learn how to use TypeScript with functions and classes.",
        isComplete: false,
        isRead: false,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "1:45 PM",
        objectives: [
          "Understand function types in TypeScript",
          "Learn how to use classes with TypeScript",
          "Implement inheritance and access modifiers"
        ],
        topics: [
          "Function Types",
          "Classes in TypeScript",
          "Inheritance and Access Modifiers"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101705,
        title: "Generics in TypeScript",
        description: "Master generics for reusable, type-safe code",
        content: "Generics allow you to create reusable components that work with a variety of types. In this module, you'll learn how to use generics in TypeScript to create flexible, type-safe code.",
        isComplete: false,
        isRead: false,
        hasTask: true,
        taskStatus: "pending",
        taskDescription: "Implement generic functions and classes for a data processing library.",
        postedDate: currentDate,
        postedTime: "2:00 PM",
        dueDate: `${year}-${Number(month) + 1}-${day}`,
        dueTime: "11:59 PM",
        objectives: [
          "Understand generics in TypeScript",
          "Learn how to create generic functions and classes",
          "Implement constraints on generics"
        ],
        topics: [
          "Introduction to Generics",
          "Generic Functions and Classes",
          "Generic Constraints"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101706,
        title: "Advanced TypeScript Features",
        description: "Explore advanced TypeScript features and patterns",
        content: "TypeScript includes several advanced features that can help you write even more robust code. In this module, you'll learn about advanced TypeScript features and design patterns.",
        isComplete: false,
        isRead: false,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "2:15 PM",
        objectives: [
          "Understand advanced TypeScript features",
          "Learn about TypeScript design patterns",
          "Implement advanced type checking"
        ],
        topics: [
          "Union and Intersection Types",
          "Conditional Types",
          "Mapped Types and Utility Types"
        ],
        files: {
          presentation: null,
          video: null
        }
      }
    ]
  },
  {
    id: 1019,
    title: "React Native: Build Mobile Apps with JavaScript",
    rating: 4.9,
    studentsEnrolled: 78,
    modules: 4,
    category: "mobile",
    description: "Create native mobile applications for iOS and Android using React Native. Learn to build cross-platform apps with a single codebase. This course will equip you with the skills needed to develop professional mobile applications.",
    postedDate: "2025-04-15",
    postedTime: "2:00 PM",
    imageUrl: "/class-data.png",
    joinedDate: currentDate,
    joinedTime: currentTime,
    status: "studied",
    progress: 25,
    materials: [
      {
        id: 101901,
        title: "Introduction to React Native",
        description: "Learn the basics of React Native and its architecture",
        content: "React Native is a framework for building native mobile applications using JavaScript and React. In this module, you'll learn the basics of React Native and how it works under the hood.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Set up a React Native development environment and create a simple 'Hello World' app.",
        postedDate: currentDate,
        postedTime: "2:30 PM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "5:00 PM",
        feedback: "Excellent work! Your React Native setup is correct and the app runs smoothly.",
        taskScore: 93,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "7:00 PM",
        objectives: [
          "Understand React Native and its architecture",
          "Learn how to set up a React Native development environment",
          "Create your first React Native application"
        ],
        topics: [
          "Introduction to React Native",
          "React Native Architecture",
          "Setting Up the Development Environment"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101902,
        title: "React Native Components and Styling",
        description: "Learn about React Native components and how to style them",
        content: "React Native provides a set of built-in components that map to native UI elements. In this module, you'll learn about React Native components and how to style them using StyleSheet.",
        isComplete: false,
        isRead: true,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "2:45 PM",
        objectives: [
          "Understand React Native core components",
          "Learn how to style components with StyleSheet",
          "Implement responsive layouts"
        ],
        topics: [
          "Core Components",
          "StyleSheet and Styling",
          "Responsive Layouts"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101903,
        title: "Navigation in React Native",
        description: "Learn how to implement navigation in React Native applications",
        content: "Navigation is a key aspect of mobile applications. In this module, you'll learn how to implement navigation in React Native using React Navigation.",
        isComplete: false,
        isRead: false,
        hasTask: true,
        taskStatus: "pending",
        taskDescription: "Implement a multi-screen navigation flow in a React Native application.",
        postedDate: currentDate,
        postedTime: "3:00 PM",
        dueDate: `${year}-${Number(month) + 1}-${day}`,
        dueTime: "11:59 PM",
        objectives: [
          "Understand navigation concepts in React Native",
          "Learn how to use React Navigation",
          "Implement stack, tab, and drawer navigation"
        ],
        topics: [
          "React Navigation",
          "Stack Navigation",
          "Tab and Drawer Navigation"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101904,
        title: "Working with APIs and Device Features",
        description: "Learn how to work with APIs and access device features in React Native",
        content: "React Native allows you to access device features and integrate with APIs. In this module, you'll learn how to work with APIs and access device features like camera, location, and storage.",
        isComplete: false,
        isRead: false,
        hasTask: false,
        postedDate: currentDate,
        postedTime: "3:15 PM",
        objectives: [
          "Understand how to make API requests in React Native",
          "Learn how to access device features",
          "Implement data persistence"
        ],
        topics: [
          "Networking and API Requests",
          "Accessing Device Features",
          "Data Persistence"
        ],
        files: {
          presentation: null,
          video: null
        }
      }
    ]
  }
];

// Dummy data for completed classes
export const completedClassesData = [
  {
    id: 1002,
    title: "React.js: Building Modern Frontend Applications",
    rating: 4.8,
    studentsEnrolled: 85,
    modules: 6,
    category: "frontend",
    description: "Learn to build responsive and interactive user interfaces with React.js. This comprehensive course covers component-based architecture, state management, hooks, and routing. You'll master modern frontend development tools and techniques.",
    postedDate: "2025-04-20",
    postedTime: "11:30 AM",
    imageUrl: "/bootcampHero.png",
    joinedDate: currentDate,
    joinedTime: currentTime,
    status: "completed",
    progress: 100,
    completionDate: currentDate,
    certificateDownloaded: true,
    materials: [
      {
        id: 100201,
        title: "Introduction to React",
        description: "Learn the basics of React and its core concepts",
        content: "React is a JavaScript library for building user interfaces. In this module, you'll learn the basics of React and its core concepts, including components, JSX, and the virtual DOM.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a simple React application that displays a list of items and allows filtering.",
        postedDate: currentDate,
        postedTime: "09:00 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "02:00 PM",
        feedback: "Excellent work! Your React application demonstrates a good understanding of components and state management.",
        taskScore: 95,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "04:00 PM",
        objectives: [
          "Understand React and its advantages",
          "Learn JSX syntax and how it works",
          "Create your first React component"
        ],
        topics: [
          "Introduction to React",
          "JSX Syntax",
          "Components and Props"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100202,
        title: "State and Lifecycle",
        description: "Master React state and component lifecycle",
        content: "State and lifecycle methods are fundamental concepts in React. In this module, you'll learn how to manage state in React components and how to use lifecycle methods to perform actions at specific points in a component's life.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a React application that demonstrates the use of state and lifecycle methods.",
        postedDate: currentDate,
        postedTime: "09:15 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "03:00 PM",
        feedback: "Great job! Your application shows a good understanding of state management and lifecycle methods.",
        taskScore: 92,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "04:30 PM",
        objectives: [
          "Understand state in React",
          "Learn component lifecycle methods",
          "Implement state management in React applications"
        ],
        topics: [
          "State and SetState",
          "Component Lifecycle Methods",
          "Controlled vs. Uncontrolled Components"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100203,
        title: "Hooks in React",
        description: "Learn to use React Hooks for state and side effects",
        content: "Hooks are a new addition in React 16.8 that let you use state and other React features without writing a class. In this module, you'll learn how to use various hooks like useState, useEffect, and custom hooks.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Refactor a class-based React application to use hooks.",
        postedDate: currentDate,
        postedTime: "09:30 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "02:30 PM",
        feedback: "Excellent work! Your refactored application demonstrates a good understanding of React hooks.",
        taskScore: 98,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "05:00 PM",
        objectives: [
          "Understand React hooks and their advantages",
          "Learn how to use useState and useEffect",
          "Create custom hooks for reusable logic"
        ],
        topics: [
          "useState and useEffect",
          "useContext and useReducer",
          "Custom Hooks"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100204,
        title: "Routing in React",
        description: "Implement routing in React applications",
        content: "Routing is essential for creating multi-page applications in React. In this module, you'll learn how to use React Router to implement navigation in your React applications.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a multi-page React application with React Router.",
        postedDate: currentDate,
        postedTime: "09:45 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "03:30 PM",
        feedback: "Great job! Your application demonstrates a good understanding of React Router and navigation concepts.",
        taskScore: 90,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "05:30 PM",
        objectives: [
          "Understand routing concepts in React",
          "Learn how to use React Router",
          "Implement nested routes and route parameters"
        ],
        topics: [
          "React Router Basics",
          "Route Parameters and Query Strings",
          "Nested Routes and Protected Routes"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100205,
        title: "State Management with Redux",
        description: "Learn to use Redux for state management",
        content: "Redux is a predictable state container for JavaScript apps. In this module, you'll learn how to use Redux for state management in React applications.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Implement Redux in a React application for state management.",
        postedDate: currentDate,
        postedTime: "10:00 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "04:00 PM",
        feedback: "Excellent work! Your application demonstrates a good understanding of Redux and state management concepts.",
        taskScore: 94,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "06:00 PM",
        objectives: [
          "Understand Redux and its principles",
          "Learn how to use actions, reducers, and the store",
          "Implement Redux in React applications"
        ],
        topics: [
          "Redux Principles",
          "Actions and Reducers",
          "Redux Middleware and Async Actions"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100206,
        title: "Testing React Applications",
        description: "Learn to test React applications",
        content: "Testing is an essential part of developing robust React applications. In this module, you'll learn how to test React components using Jest and React Testing Library.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Write tests for a React application using Jest and React Testing Library.",
        postedDate: currentDate,
        postedTime: "10:15 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "04:30 PM",
        feedback: "Great job! Your tests demonstrate a good understanding of testing React components.",
        taskScore: 91,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "06:30 PM",
        objectives: [
          "Understand testing concepts in React",
          "Learn how to use Jest and React Testing Library",
          "Write unit and integration tests for React components"
        ],
        topics: [
          "Testing with Jest",
          "React Testing Library",
          "Mocking and Test Coverage"
        ],
        files: {
          presentation: null,
          video: null
        }
      }
    ]
  },
  {
    id: 1004,
    title: "Vue.js: Progressive JavaScript Framework",
    rating: 4.7,
    studentsEnrolled: 70,
    modules: 5,
    category: "frontend",
    description: "Master Vue.js, the progressive JavaScript framework for building user interfaces. Learn component-based architecture, state management with Vuex, routing with Vue Router, and more. This course will prepare you for modern frontend development.",
    postedDate: "2025-03-15",
    postedTime: "9:00 AM",
    imageUrl: "/class-design.png",
    joinedDate: currentDate,
    joinedTime: currentTime,
    status: "completed",
    progress: 100,
    completionDate: currentDate,
    certificateDownloaded: true,
    materials: [
      {
        id: 100401,
        title: "Introduction to Vue.js",
        description: "Learn the basics of Vue.js and its core concepts",
        content: "Vue.js is a progressive JavaScript framework for building user interfaces. In this module, you'll learn the basics of Vue.js and its core concepts, including the Vue instance, directives, and template syntax.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a simple Vue.js application that displays a list of items and allows filtering.",
        postedDate: currentDate,
        postedTime: "9:00 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "2:00 PM",
        feedback: "Excellent work! Your Vue.js application demonstrates a good understanding of the core concepts.",
        taskScore: 95,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "4:00 PM",
        objectives: [
          "Understand Vue.js and its advantages",
          "Learn Vue.js core concepts",
          "Create your first Vue.js application"
        ],
        topics: [
          "Introduction to Vue.js",
          "Vue Instance and Directives",
          "Template Syntax"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100402,
        title: "Vue Components",
        description: "Master Vue.js components and component communication",
        content: "Components are a core feature of Vue.js that allows you to build reusable UI elements. In this module, you'll learn how to create and use Vue components, and how to communicate between them.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a Vue.js application with multiple components and implement component communication.",
        postedDate: currentDate,
        postedTime: "9:15 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:00 PM",
        feedback: "Great job! Your component implementation shows a good understanding of component communication.",
        taskScore: 92,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "4:30 PM",
        objectives: [
          "Understand Vue.js components",
          "Learn component registration and usage",
          "Implement component communication"
        ],
        topics: [
          "Component Basics",
          "Props and Events",
          "Slots and Scoped Slots"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100403,
        title: "Vue Router",
        description: "Learn to implement routing in Vue.js applications",
        content: "Vue Router is the official router for Vue.js. In this module, you'll learn how to implement routing in your Vue.js applications to create multi-page experiences.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a multi-page Vue.js application with Vue Router.",
        postedDate: currentDate,
        postedTime: "9:30 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "2:30 PM",
        feedback: "Excellent work! Your routing implementation is clean and follows best practices.",
        taskScore: 98,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:00 PM",
        objectives: [
          "Understand routing concepts in Vue.js",
          "Learn how to use Vue Router",
          "Implement nested routes and route guards"
        ],
        topics: [
          "Vue Router Basics",
          "Route Parameters and Query",
          "Nested Routes and Navigation Guards"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100404,
        title: "Vuex for State Management",
        description: "Master state management with Vuex",
        content: "Vuex is a state management pattern and library for Vue.js applications. In this module, you'll learn how to use Vuex to manage the state of your Vue.js applications.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Implement Vuex for state management in a Vue.js application.",
        postedDate: currentDate,
        postedTime: "9:45 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:30 PM",
        feedback: "Great job! Your Vuex implementation demonstrates a good understanding of state management concepts.",
        taskScore: 90,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:30 PM",
        objectives: [
          "Understand state management concepts",
          "Learn Vuex core concepts",
          "Implement Vuex in Vue.js applications"
        ],
        topics: [
          "Vuex Core Concepts",
          "State and Getters",
          "Mutations and Actions"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100405,
        title: "Vue.js Best Practices and Advanced Concepts",
        description: "Learn Vue.js best practices and advanced concepts",
        content: "In this module, you'll learn Vue.js best practices and advanced concepts, including performance optimization, testing, and deployment.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Optimize and deploy a Vue.js application.",
        postedDate: currentDate,
        postedTime: "10:00 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "4:00 PM",
        feedback: "Excellent work! Your optimized application shows a good understanding of Vue.js best practices.",
        taskScore: 94,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "6:00 PM",
        objectives: [
          "Understand Vue.js best practices",
          "Learn performance optimization techniques",
          "Implement testing and deployment strategies"
        ],
        topics: [
          "Performance Optimization",
          "Testing Vue.js Applications",
          "Deployment Strategies"
        ],
        files: {
          presentation: null,
          video: null
        }
      }
    ]
  },
  {
    id: 1006,
    title: "JavaScript: From Fundamentals to Advanced",
    rating: 4.9,
    studentsEnrolled: 110,
    modules: 8,
    category: "frontend",
    description: "Master JavaScript from the ground up. This comprehensive course covers everything from basic syntax to advanced concepts like closures, prototypes, and asynchronous programming. You'll gain a deep understanding of the language that powers the web.",
    postedDate: "2025-02-20",
    postedTime: "10:30 AM",
    imageUrl: "/bootcampHero.png",
    joinedDate: currentDate,
    joinedTime: currentTime,
    status: "completed",
    progress: 100,
    completionDate: currentDate,
    certificateDownloaded: false,
    materials: [
      {
        id: 100601,
        title: "JavaScript Fundamentals",
        description: "Learn the basics of JavaScript programming",
        content: "JavaScript is the programming language of the web. In this module, you'll learn the fundamentals of JavaScript, including variables, data types, operators, and control structures.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a simple JavaScript program that demonstrates variables, data types, and control structures.",
        postedDate: currentDate,
        postedTime: "10:00 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "2:00 PM",
        feedback: "Excellent work! Your JavaScript program demonstrates a good understanding of the fundamentals.",
        taskScore: 95,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "4:00 PM",
        objectives: [
          "Understand JavaScript and its role in web development",
          "Learn JavaScript syntax and data types",
          "Implement control structures and operators"
        ],
        topics: [
          "JavaScript Introduction",
          "Variables and Data Types",
          "Operators and Control Structures"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100602,
        title: "Functions and Scope",
        description: "Master JavaScript functions and scope",
        content: "Functions are a fundamental building block in JavaScript. In this module, you'll learn about functions, scope, and closures in JavaScript.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a JavaScript program that demonstrates different types of functions and scope concepts.",
        postedDate: currentDate,
        postedTime: "10:15 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:00 PM",
        feedback: "Great job! Your implementation shows a good understanding of functions and scope.",
        taskScore: 92,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "4:30 PM",
        objectives: [
          "Understand JavaScript functions",
          "Learn about scope and closures",
          "Implement different types of functions"
        ],
        topics: [
          "Function Declarations and Expressions",
          "Scope and Closures",
          "Arrow Functions"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100603,
        title: "Objects and Arrays",
        description: "Learn about JavaScript objects and arrays",
        content: "Objects and arrays are essential data structures in JavaScript. In this module, you'll learn how to work with objects and arrays in JavaScript.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a JavaScript program that demonstrates objects, arrays, and their methods.",
        postedDate: currentDate,
        postedTime: "10:30 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "2:30 PM",
        feedback: "Excellent work! Your implementation demonstrates a good understanding of objects and arrays.",
        taskScore: 98,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:00 PM",
        objectives: [
          "Understand JavaScript objects",
          "Learn about arrays and their methods",
          "Implement object-oriented programming concepts"
        ],
        topics: [
          "Objects and Properties",
          "Arrays and Array Methods",
          "Object-Oriented Programming"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100604,
        title: "DOM Manipulation",
        description: "Learn to manipulate the Document Object Model (DOM)",
        content: "The Document Object Model (DOM) is a programming interface for web documents. In this module, you'll learn how to manipulate the DOM using JavaScript.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a web page that demonstrates DOM manipulation techniques.",
        postedDate: currentDate,
        postedTime: "10:45 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:30 PM",
        feedback: "Great job! Your DOM manipulation techniques are well-implemented.",
        taskScore: 90,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:30 PM",
        objectives: [
          "Understand the Document Object Model",
          "Learn DOM manipulation techniques",
          "Implement event handling"
        ],
        topics: [
          "DOM Basics",
          "DOM Manipulation",
          "Event Handling"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100605,
        title: "Asynchronous JavaScript",
        description: "Master asynchronous programming in JavaScript",
        content: "Asynchronous programming is essential for handling operations that take time to complete. In this module, you'll learn about asynchronous JavaScript, including callbacks, promises, and async/await.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a JavaScript program that demonstrates asynchronous programming techniques.",
        postedDate: currentDate,
        postedTime: "11:00 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "4:00 PM",
        feedback: "Excellent work! Your asynchronous programming implementation is clean and follows best practices.",
        taskScore: 94,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "6:00 PM",
        objectives: [
          "Understand asynchronous programming concepts",
          "Learn about callbacks, promises, and async/await",
          "Implement asynchronous operations"
        ],
        topics: [
          "Callbacks",
          "Promises",
          "Async/Await"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100606,
        title: "Error Handling and Debugging",
        description: "Learn error handling and debugging techniques in JavaScript",
        content: "Error handling and debugging are essential skills for JavaScript developers. In this module, you'll learn how to handle errors and debug JavaScript code.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a JavaScript program that demonstrates error handling techniques and debug common issues.",
        postedDate: currentDate,
        postedTime: "11:15 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:15 PM",
        feedback: "Great job! Your error handling implementation is robust and your debugging approach is methodical.",
        taskScore: 93,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:15 PM",
        objectives: [
          "Understand error handling in JavaScript",
          "Learn debugging techniques",
          "Implement try-catch blocks and custom errors"
        ],
        topics: [
          "Error Handling",
          "Debugging Techniques",
          "Try-Catch Blocks"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100607,
        title: "Modern JavaScript Features",
        description: "Learn modern JavaScript features (ES6+)",
        content: "JavaScript has evolved significantly with the introduction of ES6 and later versions. In this module, you'll learn about modern JavaScript features that make your code more concise and powerful.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a JavaScript program that demonstrates modern JavaScript features.",
        postedDate: currentDate,
        postedTime: "11:30 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "4:30 PM",
        feedback: "Excellent work! Your implementation of modern JavaScript features is clean and effective.",
        taskScore: 96,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "6:30 PM",
        objectives: [
          "Understand modern JavaScript features",
          "Learn about ES6+ syntax",
          "Implement modern JavaScript in your code"
        ],
        topics: [
          "Arrow Functions and Template Literals",
          "Destructuring and Spread/Rest Operators",
          "Modules and Classes"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100608,
        title: "JavaScript Design Patterns",
        description: "Master common JavaScript design patterns",
        content: "Design patterns are reusable solutions to common programming problems. In this module, you'll learn about common JavaScript design patterns and how to implement them in your code.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a JavaScript program that demonstrates common design patterns.",
        postedDate: currentDate,
        postedTime: "11:45 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "4:45 PM",
        feedback: "Great job! Your implementation of design patterns shows a good understanding of their applications.",
        taskScore: 91,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "6:45 PM",
        objectives: [
          "Understand JavaScript design patterns",
          "Learn about creational, structural, and behavioral patterns",
          "Implement common design patterns in your code"
        ],
        topics: [
          "Module and Singleton Patterns",
          "Observer and Factory Patterns",
          "MVC and MVVM Patterns"
        ],
        files: {
          presentation: null,
          video: null
        }
      }
    ]
  },
  {
    id: 1008,
    title: "MongoDB: NoSQL Database for Modern Applications",
    rating: 4.6,
    studentsEnrolled: 65,
    modules: 5,
    category: "backend",
    description: "Learn to design, build, and maintain MongoDB databases. This course covers document modeling, indexing, aggregation, and performance optimization. You'll master the skills needed to work with NoSQL databases in modern applications.",
    postedDate: "2025-03-05",
    postedTime: "1:00 PM",
    imageUrl: "/class-business.png",
    joinedDate: currentDate,
    joinedTime: currentTime,
    status: "completed",
    progress: 100,
    completionDate: currentDate,
    certificateDownloaded: true,
    materials: [
      {
        id: 100801,
        title: "Introduction to NoSQL and MongoDB",
        description: "Learn the basics of NoSQL databases and MongoDB",
        content: "NoSQL databases provide a mechanism for storage and retrieval of data that is modeled in means other than the tabular relations used in relational databases. In this module, you'll learn about NoSQL databases and MongoDB in particular.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Install MongoDB and create your first database and collection.",
        postedDate: currentDate,
        postedTime: "1:00 PM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:00 PM",
        feedback: "Excellent work! Your MongoDB setup is correct and your first database is well-structured.",
        taskScore: 95,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:00 PM",
        objectives: [
          "Understand NoSQL databases and their advantages",
          "Learn MongoDB concepts and terminology",
          "Install and configure MongoDB"
        ],
        topics: [
          "NoSQL vs. Relational Databases",
          "MongoDB Concepts",
          "Installation and Configuration"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100802,
        title: "CRUD Operations in MongoDB",
        description: "Learn to perform CRUD operations in MongoDB",
        content: "CRUD operations (Create, Read, Update, Delete) are the basic operations for working with any database. In this module, you'll learn how to perform CRUD operations in MongoDB.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a MongoDB application that performs CRUD operations on a collection.",
        postedDate: currentDate,
        postedTime: "1:15 PM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "4:00 PM",
        feedback: "Great job! Your CRUD operations are well-implemented and your code is clean.",
        taskScore: 92,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:30 PM",
        objectives: [
          "Understand CRUD operations in MongoDB",
          "Learn MongoDB query syntax",
          "Implement CRUD operations in a MongoDB application"
        ],
        topics: [
          "Create and Insert Operations",
          "Read Operations and Queries",
          "Update and Delete Operations"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100803,
        title: "MongoDB Schema Design",
        description: "Learn to design MongoDB schemas",
        content: "Schema design is a critical aspect of MongoDB development. In this module, you'll learn how to design MongoDB schemas that are efficient and scalable.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Design a MongoDB schema for an e-commerce application.",
        postedDate: currentDate,
        postedTime: "1:30 PM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:30 PM",
        feedback: "Excellent work! Your schema design is well-thought-out and follows best practices.",
        taskScore: 98,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "6:00 PM",
        objectives: [
          "Understand MongoDB schema design principles",
          "Learn about embedding vs. referencing",
          "Implement efficient schema designs"
        ],
        topics: [
          "Schema Design Principles",
          "Embedding vs. Referencing",
          "Schema Validation"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100804,
        title: "MongoDB Aggregation Framework",
        description: "Master the MongoDB Aggregation Framework",
        content: "The MongoDB Aggregation Framework is a powerful tool for data processing and analysis. In this module, you'll learn how to use the Aggregation Framework to perform complex operations on your data.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create aggregation pipelines for data analysis in a MongoDB application.",
        postedDate: currentDate,
        postedTime: "1:45 PM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "4:30 PM",
        feedback: "Great job! Your aggregation pipelines are well-designed and your analysis is insightful.",
        taskScore: 90,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "6:30 PM",
        objectives: [
          "Understand the MongoDB Aggregation Framework",
          "Learn aggregation pipeline stages",
          "Implement complex data analysis with aggregation"
        ],
        topics: [
          "Aggregation Framework Basics",
          "Pipeline Stages",
          "Complex Aggregations"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 100805,
        title: "MongoDB Performance Optimization",
        description: "Learn to optimize MongoDB performance",
        content: "Performance optimization is essential for MongoDB applications at scale. In this module, you'll learn how to optimize MongoDB performance through indexing, query optimization, and other techniques.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Optimize a MongoDB application for performance.",
        postedDate: currentDate,
        postedTime: "2:00 PM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "5:00 PM",
        feedback: "Excellent work! Your performance optimizations have significantly improved the application's efficiency.",
        taskScore: 94,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "7:00 PM",
        objectives: [
          "Understand MongoDB performance considerations",
          "Learn indexing strategies",
          "Implement query optimization techniques"
        ],
        topics: [
          "Indexing Strategies",
          "Query Optimization",
          "Performance Monitoring"
        ],
        files: {
          presentation: null,
          video: null
        }
      }
    ]
  },
  {
    id: 1010,
    title: "CSS Animation and Transitions",
    rating: 4.7,
    studentsEnrolled: 75,
    modules: 4,
    category: "frontend",
    description: "Master the art of creating smooth, engaging animations and transitions with CSS. Learn keyframe animations, transitions, transforms, and advanced techniques for creating interactive user experiences without JavaScript.",
    postedDate: "2025-04-01",
    postedTime: "11:45 AM",
    imageUrl: "/class-design.png",
    joinedDate: currentDate,
    joinedTime: currentTime,
    status: "completed",
    progress: 100,
    completionDate: currentDate,
    certificateDownloaded: false,
    materials: [
      {
        id: 101001,
        title: "CSS Transitions Fundamentals",
        description: "Learn the basics of CSS transitions",
        content: "CSS transitions allow you to change property values smoothly over a given duration. In this module, you'll learn the fundamentals of CSS transitions and how to use them to create smooth effects.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a web page with various CSS transition effects.",
        postedDate: currentDate,
        postedTime: "11:00 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:00 PM",
        feedback: "Excellent work! Your CSS transitions are smooth and well-implemented.",
        taskScore: 95,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:00 PM",
        objectives: [
          "Understand CSS transitions and their properties",
          "Learn how to create smooth transitions",
          "Implement various transition effects"
        ],
        topics: [
          "Transition Properties",
          "Timing Functions",
          "Transition Delays"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101002,
        title: "CSS Transforms",
        description: "Master CSS transforms for 2D and 3D effects",
        content: "CSS transforms allow you to modify the appearance and position of elements in 2D and 3D space. In this module, you'll learn how to use CSS transforms to create various effects.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a web page with 2D and 3D transform effects.",
        postedDate: currentDate,
        postedTime: "11:15 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "4:00 PM",
        feedback: "Great job! Your transform effects are creative and well-executed.",
        taskScore: 92,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:30 PM",
        objectives: [
          "Understand CSS transforms",
          "Learn 2D and 3D transform functions",
          "Implement transform effects"
        ],
        topics: [
          "2D Transforms",
          "3D Transforms",
          "Transform Origin"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101003,
        title: "CSS Keyframe Animations",
        description: "Learn to create complex animations with CSS keyframes",
        content: "CSS keyframe animations allow you to create complex, multi-step animations. In this module, you'll learn how to use keyframes to create sophisticated animations.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a web page with keyframe animations.",
        postedDate: currentDate,
        postedTime: "11:30 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:30 PM",
        feedback: "Excellent work! Your keyframe animations are creative and well-implemented.",
        taskScore: 98,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "6:00 PM",
        objectives: [
          "Understand CSS keyframe animations",
          "Learn animation properties",
          "Implement complex animations"
        ],
        topics: [
          "Keyframe Syntax",
          "Animation Properties",
          "Animation Timing"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101004,
        title: "Advanced CSS Animation Techniques",
        description: "Master advanced CSS animation techniques",
        content: "In this module, you'll learn advanced CSS animation techniques, including performance optimization, responsive animations, and creating interactive animations without JavaScript.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a web page with advanced CSS animations and transitions.",
        postedDate: currentDate,
        postedTime: "11:45 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "4:30 PM",
        feedback: "Great job! Your advanced animations are impressive and perform well across devices.",
        taskScore: 94,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "6:30 PM",
        objectives: [
          "Understand animation performance considerations",
          "Learn responsive animation techniques",
          "Implement interactive animations without JavaScript"
        ],
        topics: [
          "Performance Optimization",
          "Responsive Animations",
          "Interactive Animations"
        ],
        files: {
          presentation: null,
          video: null
        }
      }
    ]
  },
  {
    id: 1012,
    title: "Swift Programming for iOS Development",
    rating: 4.8,
    studentsEnrolled: 60,
    modules: 6,
    category: "mobile",
    description: "Learn Swift programming and iOS app development from the ground up. This course covers Swift syntax, UIKit, SwiftUI, and the iOS app lifecycle. You'll build real-world iOS applications and publish them to the App Store.",
    postedDate: "2025-03-20",
    postedTime: "10:00 AM",
    imageUrl: "/class-data.png",
    joinedDate: currentDate,
    joinedTime: currentTime,
    status: "completed",
    progress: 100,
    completionDate: currentDate,
    certificateDownloaded: true,
    materials: [
      {
        id: 101201,
        title: "Introduction to Swift and iOS Development",
        description: "Learn the basics of Swift programming and iOS development",
        content: "Swift is a powerful and intuitive programming language for iOS, macOS, watchOS, and tvOS. In this module, you'll learn the basics of Swift programming and get an overview of iOS development.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Set up Xcode and create your first Swift playground.",
        postedDate: currentDate,
        postedTime: "10:00 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "2:00 PM",
        feedback: "Excellent work! Your Swift playground demonstrates a good understanding of the basics.",
        taskScore: 95,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "4:00 PM",
        objectives: [
          "Understand Swift and its role in iOS development",
          "Learn Swift syntax and basic concepts",
          "Set up the development environment"
        ],
        topics: [
          "Introduction to Swift",
          "Swift Syntax Basics",
          "Xcode and Development Environment"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101202,
        title: "Swift Fundamentals",
        description: "Master Swift fundamentals and core concepts",
        content: "In this module, you'll dive deeper into Swift fundamentals, including variables, constants, data types, control flow, and functions.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a Swift program that demonstrates variables, control flow, and functions.",
        postedDate: currentDate,
        postedTime: "10:15 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:00 PM",
        feedback: "Great job! Your Swift program shows a good understanding of the language fundamentals.",
        taskScore: 92,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "4:30 PM",
        objectives: [
          "Understand Swift variables and constants",
          "Learn control flow and functions",
          "Implement Swift fundamentals in a program"
        ],
        topics: [
          "Variables and Constants",
          "Control Flow",
          "Functions and Closures"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101203,
        title: "Object-Oriented Programming in Swift",
        description: "Learn object-oriented programming with Swift",
        content: "Swift supports object-oriented programming with classes, structures, and protocols. In this module, you'll learn how to use these features to create well-structured, reusable code.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a Swift program that demonstrates classes, structures, and protocols.",
        postedDate: currentDate,
        postedTime: "10:30 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "2:30 PM",
        feedback: "Excellent work! Your implementation of object-oriented concepts in Swift is clean and effective.",
        taskScore: 98,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:00 PM",
        objectives: [
          "Understand classes and structures in Swift",
          "Learn about protocols and extensions",
          "Implement object-oriented programming concepts"
        ],
        topics: [
          "Classes and Structures",
          "Protocols and Extensions",
          "Inheritance and Polymorphism"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101204,
        title: "UIKit Fundamentals",
        description: "Learn the basics of UIKit for iOS development",
        content: "UIKit is a framework that provides the core components of an iOS app's user interface. In this module, you'll learn how to use UIKit to create user interfaces for iOS applications.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a simple iOS app with UIKit components.",
        postedDate: currentDate,
        postedTime: "10:45 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:30 PM",
        feedback: "Great job! Your UIKit implementation is well-structured and follows best practices.",
        taskScore: 90,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:30 PM",
        objectives: [
          "Understand UIKit and its components",
          "Learn view controllers and navigation",
          "Implement user interfaces with UIKit"
        ],
        topics: [
          "UIKit Components",
          "View Controllers",
          "Navigation and Tab Bars"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101205,
        title: "SwiftUI Fundamentals",
        description: "Learn the basics of SwiftUI for iOS development",
        content: "SwiftUI is a modern way to declare user interfaces for any Apple platform. In this module, you'll learn how to use SwiftUI to create user interfaces for iOS applications.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a simple iOS app with SwiftUI.",
        postedDate: currentDate,
        postedTime: "11:00 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "4:00 PM",
        feedback: "Excellent work! Your SwiftUI implementation is clean and follows the declarative paradigm effectively.",
        taskScore: 94,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "6:00 PM",
        objectives: [
          "Understand SwiftUI and its advantages",
          "Learn SwiftUI views and modifiers",
          "Implement user interfaces with SwiftUI"
        ],
        topics: [
          "SwiftUI Views",
          "Modifiers and State",
          "Layout and Navigation"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101206,
        title: "iOS App Development and Deployment",
        description: "Learn to develop and deploy iOS applications",
        content: "In this module, you'll learn about the iOS app lifecycle, data persistence, networking, and how to prepare your app for submission to the App Store.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Develop a complete iOS application and prepare it for App Store submission.",
        postedDate: currentDate,
        postedTime: "11:15 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "4:30 PM",
        feedback: "Great job! Your iOS application is well-designed and ready for submission to the App Store.",
        taskScore: 96,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "6:30 PM",
        objectives: [
          "Understand the iOS app lifecycle",
          "Learn data persistence and networking",
          "Prepare an app for App Store submission"
        ],
        topics: [
          "App Lifecycle",
          "Data Persistence and Networking",
          "App Store Submission"
        ],
        files: {
          presentation: null,
          video: null
        }
      }
    ]
  },
  {
    id: 1014,
    title: "AWS Cloud Practitioner Certification",
    rating: 4.9,
    studentsEnrolled: 90,
    modules: 5,
    category: "backend",
    description: "Prepare for the AWS Certified Cloud Practitioner exam. Learn about AWS cloud concepts, services, security, architecture, pricing, and support. This course will give you a solid foundation in cloud computing with AWS.",
    postedDate: "2025-02-15",
    postedTime: "9:15 AM",
    imageUrl: "/class-golang.png",
    joinedDate: currentDate,
    joinedTime: currentTime,
    status: "completed",
    progress: 100,
    completionDate: currentDate,
    certificateDownloaded: true,
    materials: [
      {
        id: 101401,
        title: "Introduction to Cloud Computing and AWS",
        description: "Learn the basics of cloud computing and AWS",
        content: "Cloud computing has transformed how businesses deploy and manage IT resources. In this module, you'll learn the fundamentals of cloud computing and get an overview of Amazon Web Services (AWS).",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create an AWS account and explore the AWS Management Console.",
        postedDate: currentDate,
        postedTime: "9:00 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "2:00 PM",
        feedback: "Excellent work! Your understanding of cloud computing concepts and AWS is solid.",
        taskScore: 95,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "4:00 PM",
        objectives: [
          "Understand cloud computing concepts",
          "Learn about AWS and its global infrastructure",
          "Navigate the AWS Management Console"
        ],
        topics: [
          "Cloud Computing Concepts",
          "AWS Global Infrastructure",
          "AWS Management Console"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101402,
        title: "AWS Core Services",
        description: "Learn about AWS core services",
        content: "AWS offers a wide range of services across compute, storage, database, networking, and more. In this module, you'll learn about the core AWS services that form the foundation of most AWS solutions.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create and configure basic AWS resources using core services.",
        postedDate: currentDate,
        postedTime: "9:15 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:00 PM",
        feedback: "Great job! Your implementation of AWS core services demonstrates a good understanding of their capabilities.",
        taskScore: 92,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "4:30 PM",
        objectives: [
          "Understand AWS compute services",
          "Learn about AWS storage and database services",
          "Implement networking with AWS VPC"
        ],
        topics: [
          "EC2, Lambda, and ECS",
          "S3, EBS, and RDS",
          "VPC and Networking"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101403,
        title: "AWS Security and Compliance",
        description: "Learn about AWS security and compliance",
        content: "Security is a top priority for AWS. In this module, you'll learn about AWS security services, identity and access management, and compliance frameworks.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Implement security best practices for AWS resources.",
        postedDate: currentDate,
        postedTime: "9:30 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "2:30 PM",
        feedback: "Excellent work! Your implementation of AWS security best practices is thorough and follows AWS recommendations.",
        taskScore: 98,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:00 PM",
        objectives: [
          "Understand AWS shared responsibility model",
          "Learn about IAM and security services",
          "Implement security best practices"
        ],
        topics: [
          "Shared Responsibility Model",
          "IAM and Security Services",
          "Compliance Frameworks"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101404,
        title: "AWS Pricing, Support, and Architecture",
        description: "Learn about AWS pricing, support, and architecture best practices",
        content: "Understanding AWS pricing, support options, and architecture best practices is essential for cost-effective and reliable cloud solutions. In this module, you'll learn about these important aspects of AWS.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a cost-optimized architecture for a sample application.",
        postedDate: currentDate,
        postedTime: "9:45 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:30 PM",
        feedback: "Great job! Your cost-optimized architecture demonstrates a good understanding of AWS pricing and best practices.",
        taskScore: 90,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:30 PM",
        objectives: [
          "Understand AWS pricing models",
          "Learn about AWS support plans",
          "Implement AWS Well-Architected Framework"
        ],
        topics: [
          "AWS Pricing and TCO",
          "AWS Support Plans",
          "Well-Architected Framework"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101405,
        title: "AWS Cloud Practitioner Exam Preparation",
        description: "Prepare for the AWS Certified Cloud Practitioner exam",
        content: "The AWS Certified Cloud Practitioner exam validates your understanding of AWS Cloud concepts, services, security, architecture, pricing, and support. In this module, you'll prepare for the exam with practice questions and review materials.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Complete a practice exam for AWS Certified Cloud Practitioner.",
        postedDate: currentDate,
        postedTime: "10:00 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "4:00 PM",
        feedback: "Excellent work! Your practice exam results show that you're well-prepared for the AWS Certified Cloud Practitioner exam.",
        taskScore: 94,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "6:00 PM",
        objectives: [
          "Review AWS Cloud concepts and services",
          "Practice exam questions",
          "Develop exam-taking strategies"
        ],
        topics: [
          "Exam Domains and Topics",
          "Practice Questions",
          "Exam Strategies"
        ],
        files: {
          presentation: null,
          video: null
        }
      }
    ]
  },
  {
    id: 1016,
    title: "SQL Database Design and Optimization",
    rating: 4.7,
    studentsEnrolled: 70,
    modules: 6,
    category: "backend",
    description: "Master SQL database design, normalization, indexing, and query optimization. Learn to create efficient, scalable database schemas and write performant SQL queries. This course covers MySQL, PostgreSQL, and SQL Server.",
    postedDate: "2025-03-10",
    postedTime: "1:30 PM",
    imageUrl: "/class-business.png",
    joinedDate: currentDate,
    joinedTime: currentTime,
    status: "completed",
    progress: 100,
    completionDate: currentDate,
    certificateDownloaded: false,
    materials: [
      {
        id: 101601,
        title: "Introduction to Relational Databases",
        description: "Learn the fundamentals of relational databases and SQL",
        content: "Relational databases are the foundation of most data-driven applications. In this module, you'll learn the fundamentals of relational databases and SQL, the language used to interact with them.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Install a database management system and create your first database.",
        postedDate: currentDate,
        postedTime: "1:00 PM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:00 PM",
        feedback: "Excellent work! Your database setup is correct and your first database is well-structured.",
        taskScore: 95,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:00 PM",
        objectives: [
          "Understand relational database concepts",
          "Learn SQL basics",
          "Install and configure a database management system"
        ],
        topics: [
          "Relational Database Concepts",
          "SQL Basics",
          "Database Management Systems"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101602,
        title: "Database Design and Normalization",
        description: "Learn to design efficient database schemas",
        content: "Database design is a critical aspect of application development. In this module, you'll learn how to design efficient database schemas using normalization techniques.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Design a normalized database schema for an e-commerce application.",
        postedDate: currentDate,
        postedTime: "1:15 PM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "4:00 PM",
        feedback: "Great job! Your database schema is well-normalized and follows best practices.",
        taskScore: 92,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:30 PM",
        objectives: [
          "Understand database design principles",
          "Learn normalization forms",
          "Implement efficient database schemas"
        ],
        topics: [
          "Entity-Relationship Diagrams",
          "Normalization Forms",
          "Schema Design Best Practices"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101603,
        title: "Advanced SQL Queries",
        description: "Master advanced SQL query techniques",
        content: "SQL is a powerful language for querying databases. In this module, you'll learn advanced SQL query techniques to retrieve and manipulate data efficiently.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Write advanced SQL queries for data analysis.",
        postedDate: currentDate,
        postedTime: "1:30 PM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:30 PM",
        feedback: "Excellent work! Your SQL queries are well-written and efficient.",
        taskScore: 98,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "6:00 PM",
        objectives: [
          "Understand advanced SQL query techniques",
          "Learn joins, subqueries, and aggregations",
          "Implement complex data retrieval operations"
        ],
        topics: [
          "Joins and Subqueries",
          "Aggregations and Window Functions",
          "Common Table Expressions"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101604,
        title: "Indexing and Query Optimization",
        description: "Learn to optimize database performance with indexing",
        content: "Database performance is critical for application responsiveness. In this module, you'll learn how to optimize database performance through indexing and query optimization techniques.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Optimize a database with appropriate indexes and query improvements.",
        postedDate: currentDate,
        postedTime: "1:45 PM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "4:30 PM",
        feedback: "Great job! Your indexing strategy and query optimizations have significantly improved performance.",
        taskScore: 90,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "6:30 PM",
        objectives: [
          "Understand database indexing",
          "Learn query optimization techniques",
          "Implement performance improvements"
        ],
        topics: [
          "Index Types and Usage",
          "Query Execution Plans",
          "Performance Tuning"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101605,
        title: "Transactions and Concurrency",
        description: "Learn about database transactions and concurrency control",
        content: "Database transactions ensure data integrity in multi-user environments. In this module, you'll learn about transactions, concurrency control, and isolation levels.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Implement transaction management in a database application.",
        postedDate: currentDate,
        postedTime: "2:00 PM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "5:00 PM",
        feedback: "Excellent work! Your transaction implementation ensures data integrity in concurrent scenarios.",
        taskScore: 94,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "7:00 PM",
        objectives: [
          "Understand database transactions",
          "Learn concurrency control mechanisms",
          "Implement transaction management"
        ],
        topics: [
          "ACID Properties",
          "Isolation Levels",
          "Locking and Deadlocks"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101606,
        title: "Database Comparison: MySQL, PostgreSQL, and SQL Server",
        description: "Compare popular database management systems",
        content: "Different database management systems have different features and capabilities. In this module, you'll learn about the similarities and differences between MySQL, PostgreSQL, and SQL Server.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a comparison document for MySQL, PostgreSQL, and SQL Server for a specific use case.",
        postedDate: currentDate,
        postedTime: "2:15 PM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "4:45 PM",
        feedback: "Great job! Your comparison is comprehensive and provides valuable insights for database selection.",
        taskScore: 96,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "6:45 PM",
        objectives: [
          "Understand the features of different database systems",
          "Learn when to use each database system",
          "Implement database-specific optimizations"
        ],
        topics: [
          "MySQL Features and Use Cases",
          "PostgreSQL Features and Use Cases",
          "SQL Server Features and Use Cases"
        ],
        files: {
          presentation: null,
          video: null
        }
      }
    ]
  },
  {
    id: 1018,
    title: "Tailwind CSS: Utility-First CSS Framework",
    rating: 4.8,
    studentsEnrolled: 85,
    modules: 4,
    category: "frontend",
    description: "Learn to build modern user interfaces with Tailwind CSS. This utility-first CSS framework allows you to build custom designs without leaving your HTML. Master responsive design, component extraction, and theme customization.",
    postedDate: "2025-04-05",
    postedTime: "10:30 AM",
    imageUrl: "/class-design.png",
    joinedDate: currentDate,
    joinedTime: currentTime,
    status: "completed",
    progress: 100,
    completionDate: currentDate,
    certificateDownloaded: true,
    materials: [
      {
        id: 101801,
        title: "Introduction to Tailwind CSS",
        description: "Learn the basics of Tailwind CSS and utility-first CSS",
        content: "Tailwind CSS is a utility-first CSS framework that allows you to build custom designs without leaving your HTML. In this module, you'll learn the fundamentals of Tailwind CSS and the utility-first approach to styling.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Set up a project with Tailwind CSS and create a simple webpage.",
        postedDate: currentDate,
        postedTime: "10:00 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "2:00 PM",
        feedback: "Excellent work! Your Tailwind CSS setup is correct and your webpage demonstrates a good understanding of utility classes.",
        taskScore: 95,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "4:00 PM",
        objectives: [
          "Understand utility-first CSS and its advantages",
          "Learn Tailwind CSS installation and configuration",
          "Implement basic styling with utility classes"
        ],
        topics: [
          "Utility-First CSS",
          "Tailwind CSS Installation",
          "Basic Utility Classes"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101802,
        title: "Responsive Design with Tailwind CSS",
        description: "Learn to create responsive layouts with Tailwind CSS",
        content: "Responsive design is essential for modern web applications. In this module, you'll learn how to create responsive layouts using Tailwind CSS's responsive utilities.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a responsive webpage with Tailwind CSS.",
        postedDate: currentDate,
        postedTime: "10:15 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:00 PM",
        feedback: "Great job! Your responsive design implementation shows a good understanding of Tailwind's responsive utilities.",
        taskScore: 92,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "4:30 PM",
        objectives: [
          "Understand responsive design principles",
          "Learn Tailwind CSS responsive utilities",
          "Implement responsive layouts"
        ],
        topics: [
          "Responsive Breakpoints",
          "Responsive Utilities",
          "Mobile-First Design"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101803,
        title: "Component Design and Extraction",
        description: "Learn to design and extract reusable components with Tailwind CSS",
        content: "Component-based design is a powerful approach to building user interfaces. In this module, you'll learn how to design and extract reusable components with Tailwind CSS.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a component library with Tailwind CSS.",
        postedDate: currentDate,
        postedTime: "10:30 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "2:30 PM",
        feedback: "Excellent work! Your component library is well-designed and follows best practices for component extraction.",
        taskScore: 98,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:00 PM",
        objectives: [
          "Understand component-based design",
          "Learn component extraction techniques",
          "Implement reusable components"
        ],
        topics: [
          "Component Design Principles",
          "Component Extraction",
          "Component Composition"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 101804,
        title: "Customization and Advanced Techniques",
        description: "Learn to customize Tailwind CSS and implement advanced techniques",
        content: "Tailwind CSS is highly customizable and can be extended to fit your project's needs. In this module, you'll learn how to customize Tailwind CSS and implement advanced techniques.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Customize Tailwind CSS for a project and implement advanced techniques.",
        postedDate: currentDate,
        postedTime: "10:45 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:30 PM",
        feedback: "Great job! Your customizations and advanced implementations demonstrate a deep understanding of Tailwind CSS.",
        taskScore: 94,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:30 PM",
        objectives: [
          "Understand Tailwind CSS configuration",
          "Learn theme customization",
          "Implement advanced techniques"
        ],
        topics: [
          "Configuration Options",
          "Theme Customization",
          "Plugins and Extensions"
        ],
        files: {
          presentation: null,
          video: null
        }
      }
    ]
  },
  {
    id: 1020,
    title: "Git and GitHub: Version Control Mastery",
    rating: 4.6,
    studentsEnrolled: 95,
    modules: 5,
    category: "backend",
    description: "Master Git and GitHub for effective version control and collaboration. Learn branching strategies, merge conflict resolution, pull requests, and advanced Git features. This course will transform how you manage code and collaborate with others.",
    postedDate: "2025-03-25",
    postedTime: "11:00 AM",
    imageUrl: "/class-golang.png",
    joinedDate: currentDate,
    joinedTime: currentTime,
    status: "completed",
    progress: 100,
    completionDate: currentDate,
    certificateDownloaded: false,
    materials: [
      {
        id: 102001,
        title: "Introduction to Version Control with Git",
        description: "Learn the basics of version control and Git",
        content: "Version control is essential for modern software development. In this module, you'll learn the fundamentals of version control and how to use Git for tracking changes in your code.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Install Git and create your first repository.",
        postedDate: currentDate,
        postedTime: "11:00 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "2:00 PM",
        feedback: "Excellent work! Your Git setup is correct and your first repository is well-structured.",
        taskScore: 95,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "4:00 PM",
        objectives: [
          "Understand version control concepts",
          "Learn Git basics",
          "Create and manage repositories"
        ],
        topics: [
          "Version Control Concepts",
          "Git Installation and Configuration",
          "Basic Git Commands"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 102002,
        title: "Branching and Merging",
        description: "Master Git branching and merging strategies",
        content: "Branching is a powerful feature of Git that allows you to work on different versions of your code simultaneously. In this module, you'll learn how to use branches effectively and merge changes between them.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create branches, make changes, and merge them in a Git repository.",
        postedDate: currentDate,
        postedTime: "11:15 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:00 PM",
        feedback: "Great job! Your branching and merging implementation shows a good understanding of Git workflows.",
        taskScore: 92,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "4:30 PM",
        objectives: [
          "Understand branching concepts",
          "Learn merging strategies",
          "Implement effective branching workflows"
        ],
        topics: [
          "Creating and Switching Branches",
          "Merging Branches",
          "Resolving Merge Conflicts"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 102003,
        title: "GitHub and Remote Repositories",
        description: "Learn to use GitHub for collaboration",
        content: "GitHub is a platform for hosting Git repositories and collaborating with others. In this module, you'll learn how to use GitHub for remote repositories, collaboration, and code sharing.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Create a GitHub repository and collaborate with others.",
        postedDate: currentDate,
        postedTime: "11:30 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "2:30 PM",
        feedback: "Excellent work! Your GitHub repository is well-organized and your collaboration workflow is effective.",
        taskScore: 98,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:00 PM",
        objectives: [
          "Understand remote repositories",
          "Learn GitHub features",
          "Implement collaboration workflows"
        ],
        topics: [
          "Remote Repositories",
          "GitHub Features",
          "Pull Requests and Code Reviews"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 102004,
        title: "Advanced Git Techniques",
        description: "Master advanced Git features and techniques",
        content: "Git has many advanced features that can help you manage your code more effectively. In this module, you'll learn about rebasing, cherry-picking, interactive rebase, and other advanced Git techniques.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Use advanced Git techniques to manage a complex repository.",
        postedDate: currentDate,
        postedTime: "11:45 AM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "3:30 PM",
        feedback: "Great job! Your use of advanced Git techniques demonstrates a deep understanding of Git's capabilities.",
        taskScore: 90,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "5:30 PM",
        objectives: [
          "Understand advanced Git concepts",
          "Learn rebasing and cherry-picking",
          "Implement advanced Git workflows"
        ],
        topics: [
          "Rebasing vs. Merging",
          "Cherry-Picking",
          "Interactive Rebase"
        ],
        files: {
          presentation: null,
          video: null
        }
      },
      {
        id: 102005,
        title: "Git Best Practices and Workflows",
        description: "Learn Git best practices and effective workflows",
        content: "Using Git effectively requires following best practices and implementing efficient workflows. In this module, you'll learn about Git best practices, common workflows, and how to integrate Git with your development process.",
        isComplete: true,
        isRead: true,
        hasTask: true,
        taskStatus: "turned_in",
        taskDescription: "Implement a Git workflow for a team project.",
        postedDate: currentDate,
        postedTime: "12:00 PM",
        dueDate: currentDate,
        dueTime: "11:59 PM",
        submissionDate: currentDate,
        submissionTime: "4:00 PM",
        feedback: "Excellent work! Your Git workflow implementation demonstrates a good understanding of best practices and team collaboration.",
        taskScore: 94,
        taskMaxScore: 100,
        reviewedDate: currentDate,
        reviewedTime: "6:00 PM",
        objectives: [
          "Understand Git best practices",
          "Learn common Git workflows",
          "Implement effective team collaboration"
        ],
        topics: [
          "Git Best Practices",
          "Git Flow and GitHub Flow",
          "Continuous Integration with Git"
        ],
        files: {
          presentation: null,
          video: null
        }
      }
    ]
  }
];

// Export all dummy classes
export const dummyStudentClasses = [...studiedClassesData, ...completedClassesData];
