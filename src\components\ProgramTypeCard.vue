<template>
  <div class="w-full md:w-1/3 px-4">
    <div
      class="bg-white p-4 rounded-lg h-full flex flex-col transition-all duration-300 cursor-pointer"
      :class="[
        {
          'bg-teal text-white': isSelected && color === 'teal',
          'bg-blue-500 text-white': isSelected && color === 'blue',
          'bg-pink-500 text-white': isSelected && color === 'pink'
        },
        !isSelected && {
          'hover:bg-teal hover:text-white': color === 'teal',
          'hover:bg-blue-500 hover:text-white': color === 'blue',
          'hover:bg-pink-500 hover:text-white': color === 'pink'
        }
      ]"
      @click="handleClick"
    >
      <div class="flex items-center mb-3">
        <div class="w-8 h-8 mr-3 rounded flex justify-center items-center"
          :class="{
            'bg-teal/10': color === 'teal',
            'bg-blue-500/10': color === 'blue',
            'bg-pink-500/10': color === 'pink',
            'bg-white/20': isSelected
          }">
          <i :class="[
              `bi ${icon}`,
              {
                'text-teal': color === 'teal' && !isSelected,
                'text-blue-500': color === 'blue' && !isSelected,
                'text-pink-500': color === 'pink' && !isSelected,
                'text-white': isSelected
              }
            ]"></i>
        </div>
        <h5 class="font-bold">{{ title }}</h5>
      </div>
      <p class="text-left mb-4">{{ description }}</p>
      <div class="mt-auto text-left">
        <button
          class="font-bold flex items-center transition-colors duration-300 focus:outline-none group"
          :class="[
            isSelected ? 'text-white' : {
              'text-teal hover:text-teal-dark': color === 'teal',
              'text-blue-500 hover:text-blue-600': color === 'blue',
              'text-pink-500 hover:text-pink-600': color === 'pink'
            }
          ]"
          @click.stop="handleClick">
          Learn More
          <i class="bi bi-arrow-right ml-2 transition-transform group-hover:translate-x-1"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';

const props = defineProps({
  type: String,
  title: String,
  description: String,
  icon: String,
  color: String,
  isSelected: Boolean
});

const emit = defineEmits(['select']);
const router = useRouter();

function handleClick() {
  emit('select', props.type);
  router.push({
    path: '/bootcamp'
  });
}
</script>

