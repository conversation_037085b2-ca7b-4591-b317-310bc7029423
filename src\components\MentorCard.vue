<template>
  <div class="bg-white p-6 rounded-2xl shadow-sm h-full text-center transition-all duration-300 hover:translate-y-[-10px] hover:shadow-xl border border-gray-100">
    <img :src="mentor.image" :alt="mentor.name" class="w-[120px] h-[120px] rounded-full object-cover mx-auto mb-6" />
    <h5 class="font-bold mb-2 text-xl text-[#2D3748] font-poppins">{{ mentor.name }}</h5>
    <p class="text-[#006D77] text-base mb-3">{{ mentor.role }}</p>
    <p class="text-gray-500 text-sm mb-6 leading-relaxed">{{ mentor.bio }}</p>
    <div class="flex justify-center gap-4">
      <a :href="mentor.socialLinks.twitter" class="text-gray-400 hover:text-[#006D77] transition-colors duration-300">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
        </svg>
      </a>
      <a :href="mentor.socialLinks.linkedin" class="text-gray-400 hover:text-[#006D77] transition-colors duration-300">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M20.5 2h-17A1.5 1.5 0 002 3.5v17A1.5 1.5 0 003.5 22h17a1.5 1.5 0 001.5-1.5v-17A1.5 1.5 0 0020.5 2zM8 19H5v-9h3zM6.5 8.25A1.75 1.75 0 118.3 6.5a1.78 1.78 0 01-1.8 1.75zM19 19h-3v-4.74c0-1.42-.6-1.93-1.38-1.93A1.74 1.74 0 0013 14.19a.66.66 0 000 .14V19h-3v-9h2.9v1.3a3.11 3.11 0 012.7-1.4c1.55 0 3.36.86 3.36 3.66z"/>
        </svg>
      </a>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MentorCard',
  props: {
    mentor: {
      type: Object,
      required: true,
      validator: (prop) => {
        return [
          'id',
          'name',
          'role',
          'image',
          'bio',
          'socialLinks'
        ].every(key => key in prop)
      }
    }
  }
}
</script> 