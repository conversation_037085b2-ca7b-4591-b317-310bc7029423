<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white rounded-lg p-4 sm:p-6 max-w-md w-full" @click.stop>
      <div class="flex justify-between items-center mb-4 border-b pb-3">
        <h3 class="text-lg sm:text-xl font-semibold">Give Us Your Feedback!</h3>
        <button @click="$emit('close')" class="text-gray-500 hover:text-gray-700 transition-colors duration-200 p-1 rounded-full hover:bg-gray-100">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <p class="text-sm text-gray-600 mb-5">
        We greatly value your opinion. Your feedback will help us improve our services.
      </p>

      <!-- Rating Stars -->
      <div class="mb-5">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Rate Your Experience <span class="text-red-500">*</span>
        </label>
        <div class="flex space-x-2">
          <button
            v-for="star in 5"
            :key="star"
            @click="rating = star"
            class="focus:outline-none"
          >
            <svg
              class="w-6 h-6 sm:w-7 sm:h-7"
              :class="star <= rating ? 'text-orange' : 'text-gray-300'"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
            </svg>
          </button>
        </div>
        <p v-if="showValidation && !rating" class="mt-1 text-sm text-red-500">
          Please rate your experience
        </p>
      </div>

      <!-- Comment Box -->
      <div class="mb-5">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Leave a Comment <span class="text-red-500">*</span>
        </label>
        <p class="text-xs text-gray-500 mb-2">What can we improve? Tell us about your experience with our service.</p>
        <textarea
          v-model="comment"
          class="w-full border border-gray-300 rounded-md p-3 text-sm focus:ring-orange focus:border-orange resize-none"
          :class="{'border-red-500': showValidation && !comment.trim()}"
          rows="4"
          placeholder="Please write your comments here..."
        ></textarea>
        <p v-if="showValidation && !comment.trim()" class="mt-1 text-sm text-red-500">
          Please leave a comment
        </p>
      </div>

      <!-- Send Button -->
      <div class="flex justify-end">
        <button
          @click="validateAndSubmit"
          :disabled="!isFormValid"
          class="px-4 py-2 rounded-md transition-colors"
          :class="isFormValid
            ? 'bg-orange text-white hover:bg-orange-dark cursor-pointer'
            : 'bg-gray-300 text-gray-500 cursor-not-allowed'"
        >
          Send
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FeedbackDialog',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    classId: {
      type: [String, Number],
      required: true
    }
  },
  emits: ['close', 'feedback-sent'],
  data() {
    return {
      rating: 0,
      comment: '',
      showValidation: false
    }
  },
  computed: {
    isFormValid() {
      return this.rating > 0 && this.comment.trim() !== '';
    }
  },
  methods: {
    validateAndSubmit() {
      // Show validation messages if form is not valid
      if (!this.isFormValid) {
        this.showValidation = true;
        return;
      }

      // If form is valid, submit the feedback
      this.submitFeedback();
    },

    submitFeedback() {
      // In a real application, this would send the feedback to a backend
      // For now, we'll just emit an event to show the success dialog

      // Validate class ID
      if (!this.classId) {
        console.error('Cannot submit feedback: Missing class ID');
        return;
      }

      // Create feedback object
      const feedback = {
        classId: this.classId,
        rating: this.rating,
        comment: this.comment,
        submittedDate: new Date().toISOString().split('T')[0], // YYYY-MM-DD format
        submittedTime: new Date().toTimeString().split(' ')[0].substring(0, 5) // HH:MM format
      };

      // Log the feedback for debugging
      console.log(`Submitting feedback for class ${this.classId}:`, feedback);

      // Reset form
      this.rating = 0;
      this.comment = '';
      this.showValidation = false;

      // Emit event to parent component
      this.$emit('feedback-sent', feedback);
    }
  },
  watch: {
    show(newValue) {
      if (newValue) {
        // Reset form when dialog is opened
        this.rating = 0;
        this.comment = '';
        this.showValidation = false;
      }
    }
  }
}
</script>
