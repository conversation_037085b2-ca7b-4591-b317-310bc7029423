<template>
  <div>
    <HeroSection
      badge-text="Bootcamp"
      :title-text="'What Our ' + (selectedCategory === 'all' ? 'Alumni' : categoryLabels[selectedCategory]) + ' Say?'"
      subtitle-text="Discover how our Bootcamps have transformed careers and boosted skills across different tech specializations!"
      description-text="Ready to grow?"
      whatsappText="Try Free Consultation"
      hero-image="/testimonials_header.png"
      :is-dark="true"
    />

    <div class="bg-gray-50 py-16">
      <div class="container mx-auto px-4">
        <!-- Page Header -->
        <div class="text-center mb-10">
          <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Our Student Feedback</h1>
          <p class="text-gray-600 text-lg mb-8">Read what our graduates have to say about their learning journey with us</p>

          <!-- Category Filter Tabs -->
          <div class="flex flex-wrap justify-center gap-2 mb-8">
            <button
              v-for="category in categories"
              :key="category.value"
              @click="selectedCategory = category.value"
              class="px-6 py-2 rounded-lg transition-all duration-200 text-sm md:text-base"
              :class="selectedCategory === category.value
                ? 'bg-[#FF8C00] text-white font-medium shadow-md'
                : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50'"
            >
              {{ category.label }}
            </button>
          </div>
        </div>

        <!-- Testimonials Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <StudentCard
            v-for="testimonial in visibleTestimonials"
            :key="testimonial.id"
            :testimonial="testimonial"
          />
        </div>

        <!-- Loading Indicator -->
        <div
          v-if="isLoading"
          class="flex justify-center items-center py-8"
          ref="loadingElement"
        >
          <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF8C00]"></div>
        </div>

        <!-- Load More Trigger Element -->
        <div
          v-if="hasMoreToLoad && !isLoading"
          ref="loadMoreTrigger"
          class="h-4 w-full my-8"
        ></div>

        <!-- Empty State -->
        <div v-if="filteredTestimonials.length === 0" class="text-center py-16">
          <div class="inline-flex items-center bg-blue-100 text-blue-700 px-6 py-3 rounded-lg">
            No testimonials available for this category at the moment.
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import StudentCard from '@/components/StudentCard.vue';
import HeroSection from '@/components/HeroSection.vue';
import { testimonials } from '@/data/testimonials';

export default {
  name: 'TestimonialsView',
  components: {
    StudentCard,
    HeroSection
  },
  setup() {
    const testimonialsData = ref([]);
    const selectedCategory = ref('all');
    const visibleCount = ref(6); // Initially show 6 testimonials
    const isLoading = ref(false);
    const loadMoreTrigger = ref(null);
    const loadingElement = ref(null);
    let observer = null;

    // Categories for filtering
    const categories = [
      { label: 'All Testimonials', value: 'all' },
      { label: 'Back End Developer', value: 'backend' },
      { label: 'Front End Developer', value: 'frontend' },
      { label: 'Mobile App Developer', value: 'mobile' }
    ];

    // Category labels for dynamic title
    const categoryLabels = {
      'backend': 'Back-End Alumni',
      'frontend': 'Front-End Alumni',
      'mobile': 'Mobile App Alumni',
      'all': 'All Alumni'
    };

    // Filter testimonials based on selected category
    const filteredTestimonials = computed(() => {
      if (selectedCategory.value === 'all') {
        return testimonialsData.value;
      }
      return testimonialsData.value.filter(
        testimonial => testimonial.category === selectedCategory.value
      );
    });

    // Compute visible testimonials (limited by visibleCount)
    const visibleTestimonials = computed(() => {
      return filteredTestimonials.value.slice(0, visibleCount.value);
    });

    // Check if there are more testimonials to load
    const hasMoreToLoad = computed(() => {
      return visibleTestimonials.value.length < filteredTestimonials.value.length;
    });

    // Function to load more testimonials
    const loadMoreTestimonials = () => {
      if (isLoading.value || !hasMoreToLoad.value) return;

      isLoading.value = true;

      // Simulate loading delay (can be removed in production)
      setTimeout(() => {
        visibleCount.value += 6; // Load 6 more testimonials
        isLoading.value = false;

        // After loading more items, we need to check if we should observe again
        nextTick(() => {
          if (hasMoreToLoad.value) {
            setupIntersectionObserver();
          }
        });
      }, 800);
    };

    // Setup intersection observer to detect when user scrolls to the bottom
    const setupIntersectionObserver = () => {
      if (!loadMoreTrigger.value) return;

      // Disconnect previous observer if it exists
      if (observer) {
        observer.disconnect();
      }

      observer = new IntersectionObserver((entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          loadMoreTestimonials();
        }
      }, {
        root: null,
        rootMargin: '0px',
        threshold: 0.1
      });

      observer.observe(loadMoreTrigger.value);
    };

    // Reset visible count when category changes
    watch(selectedCategory, () => {
      visibleCount.value = 6;
      nextTick(() => {
        setupIntersectionObserver();
      });
    });

    onMounted(() => {
      testimonialsData.value = testimonials;

      // Check if there's a category in the URL
      if (window.location.search.includes('category=')) {
        const urlParams = new URLSearchParams(window.location.search);
        const category = urlParams.get('category');
        if (['backend', 'frontend', 'mobile', 'all'].includes(category)) {
          selectedCategory.value = category;
        }
      }

      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });

      // Setup intersection observer after component is mounted
      nextTick(() => {
        setupIntersectionObserver();
      });
    });

    onUnmounted(() => {
      // Clean up observer when component is unmounted
      if (observer) {
        observer.disconnect();
        observer = null;
      }
    });

    return {
      testimonialsData,
      selectedCategory,
      categories,
      categoryLabels,
      filteredTestimonials,
      visibleTestimonials,
      isLoading,
      hasMoreToLoad,
      loadMoreTrigger,
      loadingElement
    };
  }
};
</script>