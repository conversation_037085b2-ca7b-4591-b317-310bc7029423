<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
    <div class="bg-white rounded-md w-[700px] p-6 relative shadow-lg border border-gray-300">
      <!-- Header -->
      <div class="mb-6 flex justify-between items-start">
        <h2 class="text-lg font-semibold flex items-center gap-2">
          <img src="/taskicon.png" class="w-5 h-5" />
          Assignment
        </h2>
        <h3 class="text-sm text-right text-gray-700 font-medium">
          Class Mastery In Golang: From Zero to Hero
        </h3>
      </div>

      <!-- Modul Dropdown -->
      <select
        v-model="selectedModule"
        class="w-full border-2 border-orange rounded-md px-3 py-2 mb-4 text-sm focus:outline-none focus:border-orange-dark focus:ring-1 focus:ring-orange"
      >
        <option disabled value="">Modul</option>
        <option v-for="modul in modules" :key="modul" :value="modul">{{ modul }}</option>
      </select>

      <!-- Description -->
      <textarea
        v-model="description"
        rows="4"
        placeholder="Description"
        class="w-full border-2 border-orange rounded-md px-3 py-2 text-sm mb-4 focus:outline-none focus:border-orange-dark focus:ring-1 focus:ring-orange resize-none"
      ></textarea>

      <!-- Deadline & Time -->
      <div class="flex gap-4 mb-4">
        <!-- Date -->
        <div class="relative w-1/2">
          <input
            id="datepicker"
            type="text"
            placeholder="Assignment Deadline"
            class="w-full border-2 border-orange rounded-md px-3 py-2 text-sm focus:outline-none focus:border-orange-dark focus:ring-1 focus:ring-orange"
          />
          <span class="absolute right-3 top-2.5 text-gray-500 pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none"
              viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M8 7V3M16 7V3M4 11h16M4 19h16M4 15h16" />
            </svg>
          </span>
        </div>

        <!-- Time -->
        <div class="relative w-1/2">
          <input
            type="time"
            v-model="time"
            class="w-full border-2 border-orange rounded-md px-3 py-2 text-sm focus:outline-none focus:border-orange-dark focus:ring-1 focus:ring-orange"
          />
          <span class="absolute right-3 top-2.5 text-gray-500 pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none"
              viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 6v6l4 2M12 4a8 8 0 110 16 8 8 0 010-16z" />
            </svg>
          </span>
        </div>
      </div>

      <!-- File Drop -->
      <div
        class="border-2 border-orange rounded-md px-4 py-6 text-sm text-gray-500 text-center mb-6"
      >
        You can drag and drop files here to add them +
      </div>

      <!-- Post button -->
      <div class="flex justify-end">
        <button
          @click="postMaterial"
          class="bg-orange hover:bg-orange-dark text-white px-6 py-2 rounded-md text-sm transition-colors duration-200"
        >
          Post
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import flatpickr from 'flatpickr'
import 'flatpickr/dist/flatpickr.min.css'

const selectedModule = ref('')
const description = ref('')
const deadline = ref('')
const time = ref('')

const modules = [
  'Modul 1: Pengenalan Golang',
  'Modul 2: Struktur Data',
  'Modul 3: Goroutine dan Channel',
  'Modul 4: Error Handling',
  'Modul 5: Project Akhir'
]

const emit = defineEmits(['close', 'posted'])

const postMaterial = () => {
  console.log({
    module: selectedModule.value,
    description: description.value,
    deadline: deadline.value,
    time: time.value
  })
  emit('posted')
  emit('close')
}

onMounted(() => {
  flatpickr('#datepicker', {
    dateFormat: 'd/m/Y',
    onChange: ([date], formatted) => {
      deadline.value = formatted
    }
  })
})
</script>
