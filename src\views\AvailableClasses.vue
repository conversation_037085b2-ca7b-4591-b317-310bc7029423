<template>
  <StudentLayoutWrapper>
    <!-- Breadcrumbs -->
    <div class="flex items-center text-gray-600 mb-4">
      <span class="text-orange font-medium text-base sm:text-lg">Classes Camp</span>
    </div>

    <!-- Main Content -->
      <!-- Search and Filter -->
      <div class="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-6">
        <div class="relative flex-grow">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
            </svg>
          </div>
          <input
            type="text"
            v-model="searchQuery"
            placeholder="Search"
            class="pl-10 pr-4 py-2 w-full text-sm sm:text-base border border-gray-300 rounded-full focus:ring-orange focus:border-orange"
          />
        </div>
        <div class="relative category-dropdown">
          <button
            @click="toggleCategoryDropdown"
            class="flex items-center justify-between w-full sm:w-40 px-4 py-2 text-sm sm:text-base border border-gray-300 rounded-full focus:outline-none focus:ring-orange focus:border-orange"
          >
            <span>{{ categoryFilter || 'Category' }}</span>
            <svg
              class="w-4 h-4 ml-2 transition-transform duration-200"
              :class="{ 'rotate-180': categoryDropdownOpen }"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
          <div
            v-if="categoryDropdownOpen"
            class="absolute right-0 mt-2 w-full sm:w-40 bg-white rounded-lg shadow-lg z-10"
          >
            <div class="py-1">
              <button
                @click="selectCategory('')"
                class="block w-full text-left px-4 py-2 text-xs sm:text-sm text-gray-700 hover:bg-orange-50 hover:text-orange"
              >
                All Categories
              </button>
              <button
                @click="selectCategory('frontend')"
                class="block w-full text-left px-4 py-2 text-xs sm:text-sm text-gray-700 hover:bg-orange-50 hover:text-orange"
              >
                Frontend
              </button>
              <button
                @click="selectCategory('backend')"
                class="block w-full text-left px-4 py-2 text-xs sm:text-sm text-gray-700 hover:bg-orange-50 hover:text-orange"
              >
                Backend
              </button>
              <button
                @click="selectCategory('mobile')"
                class="block w-full text-left px-4 py-2 text-xs sm:text-sm text-gray-700 hover:bg-orange-50 hover:text-orange"
              >
                Mobile
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Classes Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 relative">
        <template v-if="filteredClasses.length > 0">
          <div v-for="classItem in visibleClasses" :key="classItem.id" class="class-card-wrapper">
            <ClassCard
              :class-data="classItem"
              button-text="View Class"
              @view-class="viewClassDetail"
            />
          </div>
        </template>
        <div v-else-if="availableClasses.length === 0" class="col-span-1 md:col-span-2 text-center py-6 sm:py-8">
          <div class="text-gray-500 text-sm sm:text-base">
            Loading available classes...
          </div>
        </div>
        <div v-else class="col-span-1 md:col-span-2 text-center py-6 sm:py-8">
          <div class="text-gray-500 text-sm sm:text-base">
            No classes found matching your criteria.
          </div>
        </div>
      </div>

      <!-- Loading Indicator -->
      <div
        v-if="visibleClasses.length >= INITIAL_ITEMS && hasMoreClasses"
        ref="observerTarget"
        class="py-3 sm:py-4 mt-3 sm:mt-4 text-center w-full"
      >
        <div v-if="loadingMore" class="flex justify-center items-center">
          <div class="w-5 h-5 sm:w-6 sm:h-6 border-2 sm:border-3 border-orange border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>

    <!-- We've removed the success modal since we're now navigating directly to the class detail page -->
  </StudentLayoutWrapper>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import StudentLayoutWrapper from '@/components/@student/StudentLayoutWrapper.vue';
import ClassCard from '@/components/@student/ClassCard.vue';
import availableClassesData, { useClassStore, STORAGE_KEYS } from '@/data/availableClasses.js';

const router = useRouter();

// Pagination and loading state
const INITIAL_ITEMS = 6;
const ITEMS_PER_PAGE = 6;
const currentPage = ref(1);
const loadingMore = ref(false);
const observerTarget = ref(null);
let observer = null;

// Filters
const searchQuery = ref('');
const categoryFilter = ref('');
const categoryDropdownOpen = ref(false);

// No modal state needed anymore

// Toggle category dropdown
const toggleCategoryDropdown = () => {
  categoryDropdownOpen.value = !categoryDropdownOpen.value;
};

// Select category and close dropdown
const selectCategory = (category) => {
  categoryFilter.value = category;
  categoryDropdownOpen.value = false;
};

// Close dropdown when clicking outside
const handleClickOutside = (event) => {
  const target = event.target;
  if (!target.closest('.category-dropdown') && categoryDropdownOpen.value) {
    categoryDropdownOpen.value = false;
  }
};

// Get the class store
const classStore = useClassStore();

// Create a reactive reference to the available classes data
// The status and materials properties are already included in the dummy data
console.log('Available classes data before ref:', availableClassesData);
// Make sure we're creating a deep copy to avoid reference issues
const availableClasses = ref(JSON.parse(JSON.stringify(availableClassesData)));
console.log('Available classes data after ref:', availableClasses.value);
console.log('Available classes count:', availableClasses.value.length);

// Filter classes based on search query, category, and joined status
const filteredClasses = computed(() => {
  // Make sure we have data to work with
  if (!availableClasses.value || availableClasses.value.length === 0) {
    console.warn('No available classes data found');
    return [];
  }

  let result = availableClasses.value;
  console.log('Initial available classes:', result.length);
  console.log('First class item:', result[0]);

  // Debug: Log all available class IDs and titles
  console.log('All available class IDs and titles:',
    result.map(c => ({ id: c.id, title: c.title })));

  // Get the list of joined classes
  const joinedClasses = classStore.classes.value;

  // Only filter joined classes if we have any
  if (joinedClasses && joinedClasses.length > 0) {
    // Get all joined class IDs and titles for more robust filtering
    const joinedClassIds = joinedClasses.map(c => c.id);
    const joinedClassTitles = joinedClasses.map(c => c.title);
    console.log('Joined class IDs:', joinedClassIds);
    console.log('Joined class titles:', joinedClassTitles);

    // Filter out classes that have already been joined
    // Check both ID and title to ensure no duplicates
    result = result.filter(classItem => {
      // If either ID matches or title matches, it's considered a duplicate
      const idMatches = joinedClassIds.includes(classItem.id);
      const titleMatches = joinedClassTitles.includes(classItem.title);

      // Keep only classes that don't match by ID or title
      return !idMatches && !titleMatches;
    });

    console.log('After filtering joined classes:', result.length);
  }

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(classItem =>
      classItem.title.toLowerCase().includes(query) ||
      classItem.description.toLowerCase().includes(query)
    );
    console.log('After search query filtering:', result.length);
  }

  // Filter by category
  if (categoryFilter.value) {
    result = result.filter(classItem => classItem.category === categoryFilter.value);
    console.log('After category filtering:', result.length);
  }

  return result;
});

// Paginate classes for infinite scroll
const visibleClasses = computed(() => {
  const result = filteredClasses.value.slice(0, currentPage.value * ITEMS_PER_PAGE);
  console.log('Visible classes count:', result.length);
  return result;
});

const hasMoreClasses = computed(() => {
  return visibleClasses.value.length < filteredClasses.value.length;
});

// View class detail
const viewClassDetail = (classId) => {
  console.log('=== VIEWING CLASS FROM AVAILABLE CLASSES ===');
  console.log('Viewing class with ID:', classId);
  console.log('ClassId type:', typeof classId);

  // Get the class data
  const classData = availableClasses.value.find(c => c.id === classId);
  if (classData) {
    console.log('Class title:', classData.title);
    console.log('Class category:', classData.category);
    console.log('Class modules:', classData.modules);
  } else {
    console.error('Class not found!');
    return; // Don't proceed if class not found
  }

  // Check if this class is already joined (double-check to prevent duplicates)
  const joinedClasses = classStore.classes.value;
  const isAlreadyJoined = joinedClasses.some(c =>
    c.id === classId || c.title === classData.title
  );

  if (isAlreadyJoined) {
    console.warn('This class is already joined! Redirecting to Academy instead.');
    // Redirect to Academy page instead
    router.push({ name: 'AcademyClassStudied' });
    return;
  }

  // Clear any existing class source from localStorage
  localStorage.removeItem(STORAGE_KEYS.CLASS_SOURCE);
  console.log('Cleared class source from localStorage');

  // Make sure classId is a number
  const numericClassId = Number(classId);
  console.log('Numeric ClassId:', numericClassId);

  // Navigate to the class detail page without forcing a reload
  router.push({
    name: 'ClassDetail',
    params: { classId: numericClassId.toString() }
  });
};

// We're now using viewClassDetail to navigate directly to the class detail page

// Load more classes when scrolling
const loadMoreClasses = () => {
  if (!loadingMore.value && hasMoreClasses.value) {
    loadingMore.value = true;
    // Simulate loading for 300ms
    setTimeout(() => {
      currentPage.value++;
      loadingMore.value = false;
    }, 300);
  }
};

// Set up intersection observer for infinite scroll
const setupIntersectionObserver = () => {
  if (observer) {
    observer.disconnect();
  }

  if (observerTarget.value) {
    observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMoreClasses.value) {
          loadMoreClasses();
        }
      },
      { threshold: 0.5 }
    );
    observer.observe(observerTarget.value);
  }
};

// Reset pagination when filters change
watch([searchQuery, categoryFilter], () => {
  currentPage.value = 1;
  nextTick(() => {
    setupIntersectionObserver();
  });
});

// Setup UI and event listeners when component is mounted
onMounted(() => {
  setupIntersectionObserver();
  document.addEventListener('click', handleClickOutside);

  // Note: We're now using dummy data from dummyStudentClasses.js
  // instead of adding sample classes here

  // Log that the component has been mounted for debugging
  console.log('AvailableClasses component mounted');
  console.log('Current route:', router.currentRoute.value);
  console.log('Available classes count:', availableClasses.value.length);

  // Log data for debugging
  console.log('=== AVAILABLE CLASSES DEBUG ===');
  console.log('Available classes data:', availableClasses.value);
  console.log('Joined classes:', classStore.classes.value);
  console.log('Filtered classes count:', filteredClasses.value.length);
  console.log('Visible classes count:', visibleClasses.value.length);
});

onUnmounted(() => {
  if (observer) {
    observer.disconnect();
  }
  document.removeEventListener('click', handleClickOutside);
});
</script>
