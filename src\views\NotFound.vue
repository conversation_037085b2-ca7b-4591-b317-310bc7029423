<template>
    <!-- Main content -->
    <div class="flex-grow flex items-center justify-center px-4 py-12">
      <div class="max-w-4xl w-full text-center">
        <!-- 404 illustration and message -->
        <div class="mb-8 flex flex-col items-center">
          <!-- 404 Number with gradient styling -->
          <div class="text-8xl font-bold bg-gradient-to-r from-orange to-teal bg-clip-text text-transparent mb-4">
            404
          </div>

          <!-- Illustration -->
          <div class="relative w-64 h-64 mb-6">
            <div class="absolute top-2 right-2 w-full h-full border-2 border-teal rounded-3xl z-0"></div>
            <div class="relative w-full h-full overflow-hidden rounded-3xl bg-gray-100 z-10 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-32 w-32 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>

          <h1 class="text-3xl font-bold text-gray-800 mb-2">Page Not Found</h1>
          <p class="text-gray-600 text-lg mb-8">The page you are looking for doesn't exist or has been moved.</p>

          <!-- Action buttons -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              @click="goBack"
              class="px-6 py-3 bg-white border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-200"
            >
              Go Back
            </button>
            <button
              @click="goHome"
              class="px-6 py-3 bg-orange text-white rounded-lg font-medium hover:bg-primary-hover transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange"
            >
              Go to Home
            </button>
          </div>
        </div>
      </div>
  </div>
</template>

<script setup>
import { useRouter} from 'vue-router';

const router = useRouter();
const goBack = () => {
  router.go(-1);
};

const goHome = () => {
    router.push('/');
};
</script>
