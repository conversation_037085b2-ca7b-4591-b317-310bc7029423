{"name": "flowcamp", "version": "1.0.0", "description": "Flowcamp ID", "license": "MIT", "repository": "https://github.com/themesberg/tailwind-vue-starter", "homepage": "https://flowbite.com/docs/getting-started/vue/", "contributors": ["<PERSON><PERSON><PERSON> (https://twitter.com/zoltanszogyenyi)"], "author": "Bergside Inc.", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@tailwindcss/forms": "^0.5.7", "autoprefixer": "^10.4.17", "flatpickr": "^4.6.13", "chart.js": "^4.4.9", "flowbite": "^3.1.2", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "vite": "^6.1.0"}}