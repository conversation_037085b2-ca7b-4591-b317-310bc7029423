<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
    <div class="bg-white rounded-md w-[700px] p-6 relative shadow-lg border border-gray-300">
      <!-- Header -->
      <div class="mb-6 flex justify-between items-start">
        <h2 class="text-lg font-semibold flex items-center gap-2">
          <img src="/videoicon.png" class="w-5 h-5" />
          Video Recording
        </h2>
        <h3 class="text-sm text-right text-gray-700 font-medium">
          Class Mastery In Golang: From Zero to Hero
        </h3>
      </div>

      <!-- Select Modul -->
      <select
        v-model="selectedModule"
        class="w-full border-2 border-orange rounded-md px-3 py-2 mb-4 text-sm focus:outline-none focus:border-orange-dark focus:ring-1 focus:ring-orange"
      >
        <option disabled value="">Modul</option>
        <option v-for="modul in modules" :key="modul" :value="modul">{{ modul }}</option>
      </select>

      <!-- File Drop -->
      <div
        class="border-2 border-orange rounded-md px-4 py-6 text-sm text-gray-500 text-center mb-6"
      >
        You can drag and drop files here to add them +
      </div>

      <!-- Button Post -->
      <div class="flex justify-end">
        <button
          @click="postMaterial"
          class="bg-orange hover:bg-orange-dark text-white px-6 py-2 rounded-md text-sm transition-colors duration-200"
        >
          Post
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { onMounted } from 'vue'

const selectedModule = ref('')
const modules = ref([
  'Modul 1: Pengenalan Golang',
  'Modul 2: Struktur Data',
  'Modul 3: Goroutine dan Channel',
  'Modul 4: Error Handling',
  'Modul 5: Project Akhir'
])

const emit = defineEmits(['close', 'posted'])

const postMaterial = () => {
  emit('posted')
  emit('close')
}
</script>
