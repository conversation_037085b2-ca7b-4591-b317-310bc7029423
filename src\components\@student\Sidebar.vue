<template>
  <div>
    <!-- Invisible backdrop for click-outside functionality (no gray overlay) -->
    <div
      v-if="isOpen"
      class="fixed inset-0 z-40"
      @click="toggleSidebar"
    ></div>

    <!-- Sidebar - Full width on mobile, fixed width on desktop - Improved mobile experience -->
    <div
      class="fixed top-0 left-0 h-full bg-white shadow-xl z-50 transition-all duration-300 ease-in-out transform overflow-hidden"
      :class="[
        isOpen ? 'w-[85vw] sm:w-80 md:w-64 translate-x-0' : 'w-0 -translate-x-full',
        isOpen ? 'border-r border-gray-200' : ''
      ]"
      @click.stop
    >
      <!-- Sidebar content wrapper with animation -->
      <div
        class="flex flex-col h-full transition-all duration-300 ease-in-out"
        :class="{
          'opacity-0 scale-[0.98]': !isOpen,
          'opacity-100 scale-100 delay-100': isOpen
        }"
      >
        <!-- Header with logo and close button - Improved for mobile -->
        <div class="flex items-center justify-between p-4 border-b relative">
          <div class="flex items-center transition-all duration-500 ease-in-out transform">
            <img src="/logo.png" alt="Flow Camp" class="h-7 sm:h-8 transition-transform duration-500 ease-in-out" />
            <span class="ml-2 text-orange-500 font-bold text-sm sm:text-base transition-all duration-500 ease-in-out">Flow Camp</span>
          </div>
          <!-- Close button positioned inside the sidebar at the top-right corner - Enhanced for mobile -->
          <button
            @click="toggleSidebar"
            class="p-2 sm:p-1.5 rounded-lg text-gray-500 hover:text-orange-500 hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-orange-300 active:scale-95 transition-all duration-200"
            aria-label="Close sidebar"
          >
            <!-- X icon -->
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Search input with improved mobile styling -->
        <div class="p-4 border-b">
          <div class="relative">
            <input
              type="text"
              placeholder="Search"
              class="w-full pl-10 pr-4 py-3 sm:py-2 text-base sm:text-sm border border-gray-300 rounded-lg focus:ring-orange-500 focus:border-orange-500 shadow-sm"
              aria-label="Search"
            />
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>

      <!-- Navigation sections with improved mobile styling -->
      <div class="flex-grow overflow-y-auto">
        <!-- MAIN section -->
        <div class="p-4">
          <div class="text-xs font-semibold text-gray-500 mb-3 px-1">MAIN</div>
          <ul class="space-y-2">
            <li>
              <router-link
                to="/student/dashboard"
                class="flex items-center px-4 py-3.5 sm:py-2.5 rounded-lg transition-colors"
                :class="isActive('/student/dashboard') ? 'bg-orange-100 text-orange-500' : 'text-gray-700 hover:bg-gray-100'"
                aria-label="Dashboard"
                @click="(event) => handleNavClick(event)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 sm:h-5 sm:w-5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                <span class="text-base sm:text-sm font-medium">Dashboard</span>
              </router-link>
            </li>
            <li>
              <router-link
                to="/student/academy"
                class="flex items-center px-4 py-3.5 sm:py-2.5 rounded-lg transition-colors"
                :class="isActive('/student/academy') ? 'bg-orange-100 text-orange-500' : 'text-gray-700 hover:bg-gray-100'"
                aria-label="Academy"
                @click="(event) => handleNavClick(event)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 sm:h-5 sm:w-5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                <span class="text-base sm:text-sm font-medium">Academy</span>
              </router-link>
            </li>
            <li>
              <router-link
                to="/student/classes"
                class="flex items-center px-4 py-3.5 sm:py-2.5 rounded-lg transition-colors"
                :class="isActive('/student/classes') ? 'bg-orange-100 text-orange-500' : 'text-gray-700 hover:bg-gray-100'"
                aria-label="Available Classes"
                @click="(event) => handleNavClick(event)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 sm:h-5 sm:w-5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                <span class="text-base sm:text-sm font-medium">Classes</span>
              </router-link>
            </li>
            <li>
              <router-link
                to="/student/exams"
                class="flex items-center px-4 py-3.5 sm:py-2.5 rounded-lg transition-colors"
                :class="isActive('/student/exams') ? 'bg-orange-100 text-orange-500' : 'text-gray-700 hover:bg-gray-100'"
                aria-label="Exams"
                @click="(event) => handleNavClick(event)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 sm:h-5 sm:w-5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <span class="text-base sm:text-sm font-medium">Exams</span>
              </router-link>
            </li>
          </ul>
        </div>

        <!-- MANAGEMENT section - Improved for mobile -->
        <div class="p-4">
          <div class="text-xs font-semibold text-gray-500 mb-3 px-1">MANAGEMENT</div>
          <ul class="space-y-2">
            <li>
              <router-link
                to="/student/statistics"
                class="flex items-center px-4 py-3.5 sm:py-2.5 rounded-lg transition-colors"
                :class="isActive('/student/statistics') ? 'bg-orange-100 text-orange-500' : 'text-gray-700 hover:bg-gray-100'"
                aria-label="Statistics"
                @click="(event) => handleNavClick(event)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 sm:h-5 sm:w-5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <span class="text-base sm:text-sm font-medium">Statistics</span>
              </router-link>
            </li>
            <li>
              <router-link
                to="/student/certificates"
                class="flex items-center px-4 py-3.5 sm:py-2.5 rounded-lg transition-colors"
                :class="isActive('/student/certificates') ? 'bg-orange-100 text-orange-500' : 'text-gray-700 hover:bg-gray-100'"
                aria-label="Certificates"
                @click="(event) => handleNavClick(event)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 sm:h-5 sm:w-5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                <span class="text-base sm:text-sm font-medium">Certificates</span>
              </router-link>
            </li>
            <li>
              <router-link
                to="/student/testimonials"
                class="flex items-center px-4 py-3.5 sm:py-2.5 rounded-lg transition-colors"
                :class="isActive('/student/testimonials') ? 'bg-orange-100 text-orange-500' : 'text-gray-700 hover:bg-gray-100'"
                aria-label="Testimonials"
                @click="(event) => handleNavClick(event)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 sm:h-5 sm:w-5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                </svg>
                <span class="text-base sm:text-sm font-medium">Testimonials</span>
              </router-link>
            </li>
          </ul>
        </div>

        <!-- SYSTEM section - Improved for mobile -->
        <div class="p-4">
          <div class="text-xs font-semibold text-gray-500 mb-3 px-1">SYSTEM</div>
          <ul class="space-y-2">
            <li>
              <router-link
                to="/student/settings"
                class="flex items-center px-4 py-3.5 sm:py-2.5 rounded-lg transition-colors"
                :class="isActive('/student/settings') ? 'bg-orange-100 text-orange-500' : 'text-gray-700 hover:bg-gray-100'"
                aria-label="Settings"
                @click="(event) => handleNavClick(event)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 sm:h-5 sm:w-5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span class="text-base sm:text-sm font-medium">Settings</span>
              </router-link>
            </li>
            <li>
              <router-link
                to="/student/help"
                class="flex items-center px-4 py-3.5 sm:py-2.5 rounded-lg transition-colors"
                :class="isActive('/student/help') ? 'bg-orange-100 text-orange-500' : 'text-gray-700 hover:bg-gray-100'"
                aria-label="Help"
                @click="(event) => handleNavClick(event)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 sm:h-5 sm:w-5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span class="text-base sm:text-sm font-medium">Help</span>
              </router-link>
            </li>
          </ul>
        </div>
      </div>

      <!-- User profile with improved mobile styling -->
      <div class="p-4 border-t flex items-center">
        <img :src="userAvatar" alt="User Profile" class="h-10 w-10 sm:h-8 sm:w-8 rounded-full object-cover border border-gray-200 shadow-sm" @error="handleAvatarError" />
        <div class="ml-3 sm:ml-2 flex-grow overflow-hidden">
          <span class="text-gray-800 font-medium block truncate text-base sm:text-sm">{{ userName }}</span>
          <span class="text-gray-500 text-sm sm:text-xs block">Student</span>
        </div>
        <router-link
          to="/student/settings"
          class="p-2.5 sm:p-1.5 rounded-full hover:bg-gray-100 active:bg-gray-200 transition-colors"
          @click="(event) => handleNavClick(event)"
          aria-label="User settings"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-4 sm:w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </router-link>
      </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router';
import { ref, onMounted, onUnmounted } from 'vue';

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['toggle']);

// Route
const route = useRoute();

// User data
const userName = ref('Student User');
const userAvatar = ref('/prof.png');

// Load user profile data
const loadUserProfile = () => {
  try {
    const userData = localStorage.getItem('user_profile');
    if (userData) {
      const profile = JSON.parse(userData);
      if (profile.fullName) {
        userName.value = profile.fullName;
      }
      if (profile.avatar) {
        userAvatar.value = profile.avatar;
      }
    }
  } catch (error) {
    console.error('Error loading user profile:', error);
  }
};

// Handle profile updates from other components
const handleProfileUpdate = (event) => {
  const profileData = event.detail;
  if (profileData.fullName) {
    userName.value = profileData.fullName;
  }
  if (profileData.avatar) {
    userAvatar.value = profileData.avatar;
  }
};

// Handle avatar image errors
const handleAvatarError = (event) => {
  // If the avatar fails to load, use the default image
  event.target.src = '/prof.png';
  userAvatar.value = '/prof.png';
};

// Load user data from localStorage if available
onMounted(() => {
  loadUserProfile();

  // Listen for profile updates from other components
  window.addEventListener('userProfileUpdated', handleProfileUpdate);
});

// Cleanup event listeners
onUnmounted(() => {
  window.removeEventListener('userProfileUpdated', handleProfileUpdate);
});

// Methods
const toggleSidebar = () => {
  emit('toggle');
};

// Close sidebar after navigation on all devices
const handleNavClick = (event) => {
  // Get the target route path from the clicked link
  const targetPath = event?.target?.closest('a')?.getAttribute('href');
  const currentPath = route.path;

  // Check if navigating to the same route
  if (targetPath && targetPath.replace('#', '') === currentPath) {
    // If it's the same route, we need to prevent the default router-link behavior
    // and manually close the sidebar since Vue Router won't trigger a full navigation
    event.preventDefault();
    event.stopPropagation();

    // Close the sidebar
    emit('toggle');

    // Log for debugging
    console.log('Navigating to same route, sidebar closed manually');
  } else {
    // For different routes, just close the sidebar as normal
    emit('toggle');
  }
};

// Enhanced active state detection
const isActive = (path) => {
  // Exact match
  if (route.path === path) return true;

  // Special case for testimonials - match both routes
  if (path === '/student/testimonials' && route.path === '/testimonials') return true;

  // Check if current route starts with the given path (for nested routes)
  // Only apply this for paths that end with a specific route (not just '/')
  if (path.length > 1 && route.path.startsWith(path)) {
    // For paths like /student/class/:id, we want to match /student/class
    const nextCharInRoute = route.path.charAt(path.length);
    // If next char is '/' or end of string, it's a match
    return nextCharInRoute === '/' || nextCharInRoute === '';
  }

  // Special case for academy-related routes
  if (path === '/student/academy' && route.path.includes('/student/class/')) {
    return true;
  }

  return false;
};


</script>
