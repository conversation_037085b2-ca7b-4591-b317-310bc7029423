<template>
  <div v-if="show" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
    <div class="bg-white rounded-xl shadow-xl w-full max-w-lg relative p-0 max-h-[90vh] flex flex-col">
      <div class="p-8 overflow-y-auto max-h-[80vh]">
        <button @click="$emit('close')" class="absolute top-4 right-4 text-gray-400 hover:text-gray-700">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        <div class="flex items-center gap-4 mb-6">
          <img :src="student.image" class="w-16 h-16 rounded-full object-cover border-2" :class="statusBorder" />
          <div>
            <div class="font-semibold text-lg">{{ student.name }}</div>
            <div class="flex items-center gap-2 mt-1">
              <span v-if="student.status === 'Passed'" class="text-green-600 font-bold">100%</span>
              <span v-if="student.status === 'On Going'" class="text-blue-600 font-bold">{{ student.progress }}</span>
              <span v-if="student.status === 'Not passed'" class="text-red-600 font-bold">{{ student.progress }}</span>
              <span :class="badgeClass">{{ badgeText }}</span>
            </div>
          </div>
        </div>
        <div class="mb-4">
          <div class="text-gray-700 font-semibold mb-1">Class</div>
          <div class="border rounded-md px-4 py-2 bg-gray-50">{{ student.class }}</div>
        </div>
        <div class="overflow-x-auto mt-4">
          <table class="min-w-full text-sm border border-gray-200 rounded-md overflow-hidden">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-4 py-2 text-left font-medium text-gray-600">Task</th>
                <th class="px-4 py-2 text-left font-medium text-gray-600">Grades</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(task, idx) in tasks" :key="idx" class="border-t">
                <td class="px-4 py-3">
                  <div>{{ task.title }}</div>
                  <div class="text-xs text-gray-400" v-if="task.date">Returned {{ task.date }}</div>
                </td>
                <td class="px-4 py-3 font-semibold text-right">{{ task.grade ?? '-' }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="mt-6">
          <div class="bg-gray-100 rounded-md px-4 py-3 flex justify-between items-center font-semibold">
            <span>Total Grades</span>
            <span>{{ totalGrade }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
const props = defineProps({
  student: { type: Object, required: true },
  show: { type: Boolean, default: false }
})
const emit = defineEmits(['close'])

// Dummy tasks/grades
const tasks = computed(() => {
  if (props.student.status === 'Passed') {
    return [
      { title: 'Pengenalan Golang & Setup', grade: 100, date: '2 February 2025, 10.00 AM' },
      { title: 'Struktur Data dalam Golang', grade: 95, date: '7 February 2025, 10.00 AM' },
      { title: 'Penerapan OOP dalam Golang', grade: 95, date: '13 February 2025, 10.00 AM' },
      { title: 'Fungsi dan Error Handling', grade: 90, date: '18 February 2025, 10.00 AM' },
      { title: 'Struct, Method, dan Interface', grade: 90, date: '23 February 2025, 10.00 AM' },
      { title: 'Pointer dan Manajemen Memori', grade: 90, date: '28 February 2025, 10.00 AM' },
      { title: 'Menggunakan Goroutines dan Channels.', grade: 90, date: '5 March 2025, 10.00 AM' },
      { title: 'Pemrosesan File dan JSON', grade: 90, date: '10 March 2025, 10.00 AM' },
      { title: 'Membangun Web API', grade: 90, date: '15 March 2025, 10.00 AM' },
      { title: 'Koneksi ke Database', grade: 90, date: '20 March 2025, 10.00 AM' },
      { title: 'Testing dan Deployment', grade: 90, date: '25 March 2025, 10.00 AM' },
    ]
  } else if (props.student.status === 'On Going') {
    return [
      { title: 'Pengenalan Golang & Setup', grade: 100, date: '2 February 2025, 10.00 AM' },
      { title: 'Struktur Data dalam Golang', grade: 95, date: '7 February 2025, 10.00 AM' },
      { title: 'Penerapan OOP dalam Golang', grade: 95, date: '13 February 2025, 10.00 AM' },
      { title: 'Fungsi dan Error Handling', grade: 90, date: '18 February 2025, 10.00 AM' },
      { title: 'Struct, Method, dan Interface', grade: 90, date: '23 February 2025, 10.00 AM' },
      { title: 'Pointer dan Manajemen Memori', grade: 90, date: '28 February 2025, 10.00 AM' },
    ]
  } else {
    // Not passed
    return [
      { title: 'Pengenalan Golang & Setup', grade: 60, date: '2 February 2025, 10.00 AM' },
      { title: 'Struktur Data dalam Golang', grade: 55, date: '7 February 2025, 10.00 AM' },
      { title: 'Penerapan OOP dalam Golang', grade: 50, date: '13 February 2025, 10.00 AM' },
      { title: 'Fungsi dan Error Handling', grade: 40, date: '18 February 2025, 10.00 AM' },
      { title: 'Struct, Method, dan Interface', grade: 30, date: '23 February 2025, 10.00 AM' },
      { title: 'Pointer dan Manajemen Memori', grade: 20, date: '28 February 2025, 10.00 AM' },
    ]
  }
})

const totalGrade = computed(() => {
  if (tasks.value.length === 0) return '-'
  const sum = tasks.value.reduce((acc, t) => acc + (t.grade || 0), 0)
  return Math.round(sum / tasks.value.length)
})

const badgeText = computed(() => {
  if (props.student.status === 'Passed') return 'Passed'
  if (props.student.status === 'On Going') return 'On Going'
  return 'Not Passed'
})
const badgeClass = computed(() => {
  if (props.student.status === 'Passed') return 'ml-2 px-3 py-1 rounded-full text-xs bg-green-100 text-green-600 font-semibold'
  if (props.student.status === 'On Going') return 'ml-2 px-3 py-1 rounded-full text-xs bg-blue-100 text-blue-600 font-semibold'
  return 'ml-2 px-3 py-1 rounded-full text-xs bg-red-100 text-red-600 font-semibold'
})
const statusBorder = computed(() => {
  if (props.student.status === 'Passed') return 'border-green-400'
  if (props.student.status === 'On Going') return 'border-blue-400'
  return 'border-red-400'
})
</script> 