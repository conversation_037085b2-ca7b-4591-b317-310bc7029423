<template>
    <transition name="fade">
      <div
        v-if="visible"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40"
      >
        <div class="bg-white rounded-lg px-6 py-5 text-center shadow-lg w-[300px]">
          <img src="/saved.png" class="mx-auto w-12 h-12 mb-4" />
          <h2 class="text-lg font-semibold text-gray-800">Edit saved!</h2>
          <p class="text-sm text-gray-600 mt-1">Changes Saved Successfully</p>
        </div>
      </div>
    </transition>
  </template>
  
  <script setup>
  import { ref, watch, onMounted } from 'vue'
  
  const props = defineProps({
    show: Boolean
  })
  const emit = defineEmits(['close'])
  
  const visible = ref(props.show)
  
  watch(() => props.show, (newVal) => {
    visible.value = newVal
    if (newVal) {
      setTimeout(() => {
        visible.value = false
        emit('close')
      }, 2000)
    }
  })
  </script>
  
  <style>
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }
  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
  </style>
  