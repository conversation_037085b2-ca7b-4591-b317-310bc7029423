<template>
    <div class="max-w-5xl mx-auto px-6 py-8 space-y-6">
      <!-- Breadcrumb -->
      <div class="text-sm text-gray-500 flex items-center gap-2">
        <router-link to="/AllClasses" class="text-[#F2720C] hover:underline font-medium">Class Camp</router-link>
        <span>›</span>
        <router-link :to="{ name: 'DetailClassCompleted', params: { title: classTitle } }"
                     class="text-[#F2720C] hover:underline font-medium">
          {{ classTitle }}
        </router-link>
        <span>›</span>
        <span class="text-gray-700 font-medium">Review & Rating</span>
      </div>
  
      <!-- DROPDOWN FILTER -->
      <div class="flex justify-end">
        <select v-model="selectedFilter" class="border px-4 py-2 rounded-md text-sm">
          <option value="all">All Reviews</option>
          <option value="positive">Positive</option>
          <option value="negative">Negative</option>
        </select>
      </div>
  
      <!-- REVIEW LIST -->
      <div class="space-y-4 max-h-[600px] overflow-y-auto pr-2 custom-scroll">
        <div
          v-for="(review, index) in filteredReviews"
          :key="index"
          class="bg-white shadow rounded-lg p-4 border border-gray-200"
        >
          <div class="flex items-center gap-2 mb-1">
            <div class="font-semibold text-sm text-gray-800">{{ review.name }}</div>
            <div class="text-yellow-500 text-sm">★ ★ ★ ★ ★</div>
            <div class="text-xs text-gray-400">{{ review.timeAgo }}</div>
          </div>
          <p class="text-sm text-gray-700">{{ review.comment }}</p>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { useRoute } from 'vue-router'
  import { ref, computed } from 'vue'
  
  const route = useRoute()
  const classTitle = route.params.title || 'Unknown Class'
  
  const selectedFilter = ref('all')
  
  // Dummy data
  const allReviews = [
    {
      name: 'jasmine',
      timeAgo: '2 days ago',
      comment: 'mentornya keren ngajarnya bagus aku jadi bisa gampang paham',
      rating: 5
    },
    {
      name: 'Andri',
      timeAgo: '2 days ago',
      comment:
        'Mentor ini luar biasa! Cara mengajarnya jelas, terstruktur, dan selalu memberikan contoh yang mudah dipahami. Setiap materi dijelaskan dengan rinci, dan kalau ada pertanyaan, pasti dijawab dengan sabar. Aku juga suka cara mentor memberikan feedback untuk tugas-tugas kami, jadi bisa tahu di mana kesalahan dan bagaimana cara memperbaikinya.',
      rating: 5
    },
    {
      name: 'Andri',
      timeAgo: '2 days ago',
      comment:
        'Mentor ini luar biasa! Cara mengajarnya jelas, terstruktur, dan selalu memberikan contoh yang mudah dipahami. Setiap materi dijelaskan dengan rinci, dan kalau ada pertanyaan, pasti dijawab dengan sabar. Aku juga suka cara mentor memberikan feedback untuk tugas-tugas kami, jadi bisa tahu di mana kesalahan dan bagaimana cara memperbaikinya.',
      rating: 5
    },
    {
      name: 'jasmine',
      timeAgo: '2 days ago',
      comment: 'mentornya keren ngajarnya bagus aku jadi bisa gampang paham',
      rating: 5
    }
  ]
  
  const filteredReviews = computed(() => {
    if (selectedFilter.value === 'positive') return allReviews.filter(r => r.rating >= 4)
    if (selectedFilter.value === 'negative') return allReviews.filter(r => r.rating <= 2)
    return allReviews
  })
  </script>
  