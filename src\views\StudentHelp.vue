<template>
  <StudentLayoutWrapper contentClass="bg-gray-50">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-800">Help Center</h1>
      <p class="text-gray-600 mt-1">Find answers to common questions and get support</p>
    </div>

    <!-- Search Bar -->
    <div class="bg-white p-6 rounded-lg shadow-sm mb-8">
      <div class="max-w-3xl mx-auto">
        <div class="relative">
          <input
            type="text"
            v-model="searchQuery"
            placeholder="Search for help topics..."
            class="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500 text-gray-900"
          />
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- Help Categories -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <div
        v-for="(category, index) in categories"
        :key="index"
        class="bg-white rounded-lg shadow-sm p-6 transition-all duration-300 hover:shadow-md border border-gray-100 hover:border-orange-100"
      >
        <div class="flex items-center mb-4">
          <div class="w-10 h-10 rounded-full flex items-center justify-center" :class="category.bgColor">
            <component :is="category.icon" class="h-5 w-5" :class="category.iconColor" />
          </div>
          <h3 class="ml-3 text-lg font-medium text-gray-900">{{ category.title }}</h3>
        </div>
        <p class="text-gray-600 mb-4">{{ category.description }}</p>
        <button
          @click="selectCategory(category.id)"
          class="text-orange-600 hover:text-orange-700 font-medium flex items-center"
        >
          View articles
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Frequently Asked Questions -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
      <h2 class="text-xl font-semibold text-gray-800 mb-6">Frequently Asked Questions</h2>
      
      <div class="space-y-4">
        <div v-for="(faq, index) in filteredFaqs" :key="index" class="border-b border-gray-200 pb-4 last:border-b-0 last:pb-0">
          <button
            @click="toggleFaq(index)"
            class="flex justify-between items-center w-full text-left font-medium text-gray-900 focus:outline-none"
          >
            <span>{{ faq.question }}</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 text-gray-500 transition-transform duration-200"
              :class="{ 'transform rotate-180': expandedFaqs[index] }"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          <div
            v-show="expandedFaqs[index]"
            class="mt-2 text-gray-600 transition-all duration-200 overflow-hidden"
          >
            <p>{{ faq.answer }}</p>
          </div>
        </div>
      </div>

      <div v-if="filteredFaqs.length === 0" class="text-center py-8">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h3 class="text-lg font-medium text-gray-800 mb-1">No results found</h3>
        <p class="text-gray-600">Try adjusting your search query or browse by category</p>
      </div>
    </div>

    <!-- Contact Support -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
      <h2 class="text-xl font-semibold text-gray-800 mb-4">Still Need Help?</h2>
      <p class="text-gray-600 mb-6">Our support team is here to help you with any questions or issues you may have.</p>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="border border-gray-200 rounded-lg p-4 flex items-start">
          <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-medium text-gray-900">Email Support</h3>
            <p class="text-gray-600 mb-2">Send us an email and we'll get back to you within 24 hours.</p>
            <a href="mailto:<EMAIL>" class="text-orange-600 hover:text-orange-700 font-medium"><EMAIL></a>
          </div>
        </div>
        
        <div class="border border-gray-200 rounded-lg p-4 flex items-start">
          <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-medium text-gray-900">Live Chat</h3>
            <p class="text-gray-600 mb-2">Chat with our support team in real-time during business hours.</p>
            <button
              @click="startLiveChat"
              class="text-orange-600 hover:text-orange-700 font-medium flex items-center"
            >
              Start chat
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Contact Form -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
      <h2 class="text-xl font-semibold text-gray-800 mb-4">Contact Us</h2>
      <p class="text-gray-600 mb-6">Fill out the form below and we'll get back to you as soon as possible.</p>
      
      <form @submit.prevent="submitContactForm" class="space-y-6">
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
            <input
              type="text"
              id="name"
              v-model="contactForm.name"
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
              required
            />
          </div>
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
            <input
              type="email"
              id="email"
              v-model="contactForm.email"
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
              required
            />
          </div>
        </div>
        
        <div>
          <label for="subject" class="block text-sm font-medium text-gray-700">Subject</label>
          <input
            type="text"
            id="subject"
            v-model="contactForm.subject"
            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
            required
          />
        </div>
        
        <div>
          <label for="message" class="block text-sm font-medium text-gray-700">Message</label>
          <textarea
            id="message"
            v-model="contactForm.message"
            rows="4"
            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
            required
          ></textarea>
        </div>
        
        <div class="flex justify-end">
          <button
            type="submit"
            class="px-4 py-2 bg-orange-600 text-white rounded-md text-sm font-medium hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
            :disabled="isSubmitting"
          >
            <span v-if="isSubmitting" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Sending...
            </span>
            <span v-else>Send Message</span>
          </button>
        </div>
      </form>
    </div>

    <!-- Success Toast -->
    <div
      v-if="showSuccessToast"
      class="fixed bottom-4 right-4 bg-green-50 border-l-4 border-green-400 p-4 rounded shadow-md transition-all duration-500 transform"
      :class="{ 'translate-y-0 opacity-100': showSuccessToast, 'translate-y-10 opacity-0': !showSuccessToast }"
    >
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-green-800">{{ successMessage }}</p>
        </div>
        <div class="ml-auto pl-3">
          <div class="-mx-1.5 -my-1.5">
            <button
              @click="showSuccessToast = false"
              class="inline-flex bg-green-50 rounded-md p-1.5 text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </StudentLayoutWrapper>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import StudentLayoutWrapper from '@/components/@student/StudentLayoutWrapper.vue';

// State
const searchQuery = ref('');
const selectedCategory = ref(null);
const expandedFaqs = ref([]);
const isSubmitting = ref(false);
const showSuccessToast = ref(false);
const successMessage = ref('');

// Contact form
const contactForm = ref({
  name: '',
  email: '',
  subject: '',
  message: ''
});

// Help categories with SVG components
const categories = [
  {
    id: 'getting-started',
    title: 'Getting Started',
    description: 'Learn the basics of using Flow Camp and get started with your learning journey.',
    icon: {
      template: `
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      `
    },
    bgColor: 'bg-blue-100',
    iconColor: 'text-blue-600'
  },
  {
    id: 'account',
    title: 'Account & Profile',
    description: 'Manage your account settings, profile information, and security options.',
    icon: {
      template: `
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      `
    },
    bgColor: 'bg-purple-100',
    iconColor: 'text-purple-600'
  },
  {
    id: 'classes',
    title: 'Classes & Learning',
    description: 'Find information about enrolling in classes, accessing materials, and tracking progress.',
    icon: {
      template: `
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      `
    },
    bgColor: 'bg-green-100',
    iconColor: 'text-green-600'
  },
  {
    id: 'exams',
    title: 'Exams & Assignments',
    description: 'Learn about taking exams, submitting assignments, and viewing your results.',
    icon: {
      template: `
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
      `
    },
    bgColor: 'bg-orange-100',
    iconColor: 'text-orange-600'
  },
  {
    id: 'certificates',
    title: 'Certificates',
    description: 'Information about earning, viewing, and downloading your certificates.',
    icon: {
      template: `
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
        </svg>
      `
    },
    bgColor: 'bg-yellow-100',
    iconColor: 'text-yellow-600'
  },
  {
    id: 'technical',
    title: 'Technical Issues',
    description: 'Troubleshoot common technical problems and find solutions.',
    icon: {
      template: `
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      `
    },
    bgColor: 'bg-red-100',
    iconColor: 'text-red-600'
  }
];

// FAQs
const faqs = [
  {
    question: 'How do I enroll in a class?',
    answer: 'To enroll in a class, navigate to the "Classes" section from the sidebar, browse available classes, and click the "Enroll" button on the class you want to join. Some classes may require payment or approval before enrollment is complete.',
    category: 'classes'
  },
  {
    question: 'How do I reset my password?',
    answer: 'To reset your password, go to the login page and click on "Forgot Password". Enter your email address and follow the instructions sent to your email to create a new password.',
    category: 'account'
  },
  {
    question: 'How do I track my progress in a class?',
    answer: 'Your progress is automatically tracked as you complete lessons and assignments. You can view your overall progress on your dashboard and detailed progress for each class on the class page.',
    category: 'classes'
  },
  {
    question: 'How do I download my certificate?',
    answer: 'After completing a class, your certificate will be available in the "Certificates" section. Click on the certificate you want to download and use the "Download" button to save it to your device.',
    category: 'certificates'
  },
  {
    question: 'What should I do if a video won\'t play?',
    answer: 'If a video won\'t play, try refreshing the page, checking your internet connection, or using a different browser. If the issue persists, clear your browser cache or contact support for assistance.',
    category: 'technical'
  },
  {
    question: 'How do I submit an assignment?',
    answer: 'To submit an assignment, navigate to the assignment page within your class, complete the required tasks, and click the "Submit" button. Make sure to review your work before submitting as some assignments may not allow resubmission.',
    category: 'exams'
  },
  {
    question: 'How do I contact my instructor?',
    answer: 'You can contact your instructor through the messaging system within each class. Navigate to the class page, find the "Contact Instructor" button, and send your message. Instructors typically respond within 24-48 hours.',
    category: 'classes'
  },
  {
    question: 'Can I access the platform on mobile devices?',
    answer: 'Yes, Flow Camp is fully responsive and can be accessed on smartphones and tablets through your device\'s web browser. We recommend using the latest version of Chrome, Safari, or Firefox for the best experience.',
    category: 'technical'
  }
];

// Filter FAQs based on search query and selected category
const filteredFaqs = computed(() => {
  let filtered = faqs;
  
  // Filter by category if selected
  if (selectedCategory.value) {
    filtered = filtered.filter(faq => faq.category === selectedCategory.value);
  }
  
  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(faq => 
      faq.question.toLowerCase().includes(query) || 
      faq.answer.toLowerCase().includes(query)
    );
  }
  
  return filtered;
});

// Select category
const selectCategory = (categoryId) => {
  selectedCategory.value = categoryId;
  // Reset expanded FAQs when changing category
  expandedFaqs.value = Array(filteredFaqs.value.length).fill(false);
};

// Toggle FAQ expansion
const toggleFaq = (index) => {
  expandedFaqs.value[index] = !expandedFaqs.value[index];
};

// Start live chat
const startLiveChat = () => {
  // In a real app, this would open a chat widget
  alert('Live chat feature would open here');
};

// Submit contact form
const submitContactForm = () => {
  isSubmitting.value = true;
  
  // Simulate API call
  setTimeout(() => {
    // Reset form
    contactForm.value = {
      name: '',
      email: '',
      subject: '',
      message: ''
    };
    
    // Show success message
    successMessage.value = 'Your message has been sent! We\'ll get back to you soon.';
    showSuccessToast.value = true;
    isSubmitting.value = false;
    
    // Hide toast after 5 seconds
    setTimeout(() => {
      showSuccessToast.value = false;
    }, 5000);
  }, 1500);
};

// Initialize expanded FAQs array
onMounted(() => {
  expandedFaqs.value = Array(faqs.length).fill(false);
  
  // Pre-fill contact form with user data if available
  try {
    const storedProfile = localStorage.getItem('user_profile');
    if (storedProfile) {
      const profile = JSON.parse(storedProfile);
      contactForm.value.name = profile.fullName || '';
      contactForm.value.email = profile.email || '';
    }
  } catch (error) {
    console.error('Error loading profile:', error);
  }
});
</script>
