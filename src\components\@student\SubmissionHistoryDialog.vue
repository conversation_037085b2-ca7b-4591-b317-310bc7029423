<template>
  <div v-if="show" class="fixed inset-0 z-[999] overflow-hidden">
    <!-- Full screen backdrop overlay -->
    <div class="absolute inset-0 bg-black/50" @click="$emit('close')"></div>

    <!-- Dialog content -->
    <div class="absolute inset-0 flex items-center justify-center p-4">
      <div class="bg-white rounded-lg p-4 sm:p-6 max-w-2xl w-full max-h-[90vh] flex flex-col relative z-[1000]">
        <!-- Header -->
        <div class="flex items-center justify-between mb-3 sm:mb-4 flex-shrink-0">
          <div class="flex items-center">
            <div class="flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8 border-2 border-orange rounded-full mr-2 sm:mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-5 sm:w-5 text-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h2 class="text-base sm:text-xl font-semibold text-gray-700">Submission History</h2>
          </div>
          <button @click="$emit('close')" class="text-gray-500 hover:text-gray-700 border-2 rounded-full border-gray-500 hover:border-gray-700">
            <svg class="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Submission list -->
        <div class="flex-1 overflow-y-auto">
          <div class="border-b ml-1 mr-1 border-black"></div>

          <!-- Header for completed class view -->
          <div v-if="isClassCompleted" class="grid grid-cols-[1fr,auto] gap-2 sm:gap-4 p-2 mb-2 sm:mb-4">
            <h3 class="text-sm sm:text-base font-medium text-gray-700">Submission</h3>
            <p class="text-sm sm:text-base font-medium text-gray-700 text-center min-w-[90px] sm:min-w-[115px]">Mark</p>
          </div>

          <!-- Regular header for non-completed class view -->
          <div v-else-if="submissionHistory.length > 0" class="font-semibold p-2 mb-2 sm:mb-4 text-base sm:text-lg">Submission</div>

          <!-- Empty state -->
          <div v-if="submissionHistory.length === 0" class="text-center text-gray-500 py-4 text-sm sm:text-base">
            No submissions available yet. Complete and submit your tasks to see them here.
          </div>

          <!-- Submission items list -->
          <template v-if="submissionHistory.length > 0">
            <!-- For completed class view -->
            <template v-if="isClassCompleted">
              <div v-for="submission in submissionHistory"
                   :key="submission.id"
                   class="p-2 mb-2 hover:bg-gray-100 hover:rounded-lg transition-colors cursor-pointer"
                   @click="navigateToTask(submission.id)">
                <div class="grid grid-cols-[1fr,auto] gap-2 sm:gap-4 items-center">
                  <div>
                    <h3 class="text-gray-800 font-medium text-sm sm:text-base">{{ submission.title }}</h3>
                    <p class="text-gray-500 text-xs sm:text-sm">
                      Returned {{ formatDate(submission.submittedDate) }} {{ formatTime(submission.submittedTime) }}
                    </p>
                  </div>
                  <span class="text-sm font-medium text-center min-w-[80px] sm:min-w-[100px]">
                    {{ submission.taskScore || 100 }}
                  </span>
                </div>
              </div>
            </template>

            <!-- For non-completed class view -->
            <template v-else>
              <div v-for="submission in submissionHistory"
                   :key="submission.id"
                   class="p-2 mb-0.5 hover:bg-gray-100 hover:rounded-lg transition-colors cursor-pointer"
                   @click="navigateToTask(submission.id)">
                <div class="flex flex-wrap sm:flex-nowrap justify-between items-center gap-2">
                  <h3 class="text-base sm:text-lg font-light text-gray-800 w-full sm:max-w-[70%]">{{ submission.title }}</h3>
                  <span class="text-xs font-medium px-2 sm:px-3 py-1 rounded-full shadow-sm whitespace-nowrap"
                        :class="getStatusClass(submission.taskStatus)">
                    {{ formatTaskStatus(submission.taskStatus) }}
                  </span>
                </div>

                <!-- Date information -->
                <div class="flex items-center mt-1 sm:mt-2">
                  <span v-if="submission.taskStatus === 'past_due'" class="text-xs sm:text-sm text-red-500">
                    <span class="font-medium">Due date passed:</span> {{ formatDate(submission.dueDate) }} {{ submission.dueTime }}
                  </span>
                  <span v-else class="text-xs sm:text-sm text-gray-500">
                    Submitted: {{ formatDate(submission.submittedDate) }} {{ submission.submittedTime }}
                  </span>
                </div>
              </div>
            </template>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { useClassStore } from '@/data/availableClasses';
import { formatDate, formatTime, getStatusClass, formatTaskStatus } from '@/utils/studentUtils';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  classId: {
    type: [String, Number],
    required: true
  }
});

const emit = defineEmits(['close']);
const router = useRouter();
const classStore = useClassStore();

// Check if the class is completed
const isClassCompleted = computed(() => {
  const classItem = classStore.getClassById(parseInt(props.classId));
  return classItem?.status === 'completed';
});

const submissionHistory = computed(() => {
  const tasks = classStore.getAllTaskHistory(parseInt(props.classId))
    // Filter to include tasks that have been submitted, are under review, or have been reviewed
    .filter(task =>
      ['submitted', 'under_review', 'reviewed', 'past_due', 'turned_in'].includes(task.taskStatus)
    )
    .map(task => ({
      id: task.id,
      title: task.title,
      taskStatus: task.taskStatus,
      submittedDate: task.submissionDate,
      submittedTime: task.submissionTime,
      reviewStartDate: task.reviewStartDate,
      reviewStartTime: task.reviewStartTime,
      reviewedDate: task.reviewedDate,
      reviewedTime: task.reviewedTime,
      dueDate: task.dueDate,
      dueTime: task.dueTime,
      // Ensure consistent scores across the application
      taskScore: task.taskScore || (task.taskStatus === 'reviewed' ? (task.id % 10 === 0 ? 100 : task.id % 10 === 1 ? 95 : task.id % 10 === 2 ? 90 : 95) : null)
    }));

  // For completed classes, only show materials with 'reviewed' status
  if (isClassCompleted.value) {
    const reviewedTasks = tasks.filter(task => task.taskStatus === 'reviewed');

    // Sort by score (highest first)
    return reviewedTasks.sort((a, b) => {
      // If scores are the same, sort by submission date (most recent first)
      if (b.taskScore === a.taskScore) {
        const dateA = new Date(`${a.submittedDate}T${a.submittedTime || '00:00'}`);
        const dateB = new Date(`${b.submittedDate}T${b.submittedTime || '00:00'}`);
        return dateB - dateA;
      }
      return b.taskScore - a.taskScore;
    });
  }

  // For non-completed classes, separate tasks by status
  const pastDueTasks = tasks.filter(task => task.taskStatus === 'past_due');
  const submittedTasks = tasks.filter(task => task.taskStatus === 'submitted');
  const underReviewTasks = tasks.filter(task => task.taskStatus === 'under_review');
  const reviewedTasks = tasks.filter(task => task.taskStatus === 'reviewed');
  const turnedInTasks = tasks.filter(task => task.taskStatus === 'turned_in');

  // Sort past_due tasks by due date (most overdue first)
  const sortedPastDueTasks = pastDueTasks.sort((a, b) => {
    const dateA = new Date(`${a.dueDate}T${a.dueTime}`);
    const dateB = new Date(`${b.dueDate}T${b.dueTime}`);
    return dateA - dateB; // Ascending order - oldest due date first (most overdue)
  });

  // Sort submitted tasks by submission date (most recent first)
  const sortedSubmittedTasks = submittedTasks.sort((a, b) => {
    const dateA = new Date(`${a.submittedDate || a.postedDate}T${a.submittedTime || '00:00'}`);
    const dateB = new Date(`${b.submittedDate || b.postedDate}T${b.submittedTime || '00:00'}`);
    return dateB - dateA; // Descending order - most recent first
  });

  // Sort under_review tasks by review start date (most recent first)
  const sortedUnderReviewTasks = underReviewTasks.sort((a, b) => {
    const dateA = new Date(`${a.reviewStartDate || a.submittedDate || a.postedDate}T${a.reviewStartTime || a.submittedTime || '00:00'}`);
    const dateB = new Date(`${b.reviewStartDate || b.submittedDate || b.postedDate}T${b.reviewStartTime || b.submittedTime || '00:00'}`);
    return dateB - dateA; // Descending order - most recent first
  });

  // Sort reviewed tasks by reviewed date (most recent first)
  const sortedReviewedTasks = reviewedTasks.sort((a, b) => {
    const dateA = new Date(`${a.reviewedDate || a.submittedDate || a.postedDate}T${a.reviewedTime || a.submittedTime || '00:00'}`);
    const dateB = new Date(`${b.reviewedDate || b.submittedDate || b.postedDate}T${b.reviewedTime || b.submittedTime || '00:00'}`);
    return dateB - dateA; // Descending order - most recent first
  });

  // Sort turned_in tasks by submission date (most recent first)
  const sortedTurnedInTasks = turnedInTasks.sort((a, b) => {
    const dateA = new Date(`${a.submittedDate || a.postedDate}T${a.submittedTime || '00:00'}`);
    const dateB = new Date(`${b.submittedDate || b.postedDate}T${b.submittedTime || '00:00'}`);
    return dateB - dateA; // Descending order - most recent first
  });

  // Combine the arrays with priority order: past_due, under_review, submitted, reviewed, turned_in
  return [
    ...sortedPastDueTasks,
    ...sortedUnderReviewTasks,
    ...sortedSubmittedTasks,
    ...sortedReviewedTasks,
    ...sortedTurnedInTasks
  ];
});

const navigateToTask = (materialId) => {
  classStore.setCurrentMaterial(materialId);
  emit('close');
  router.push({
    name: 'DetailTaskHistory',
    params: {
      classId: props.classId,
      materialId
    },
    query: {
      source: 'dialog_submission'
    }
  });
};

// formatTaskStatus, getStatusClass, formatDate, and formatTime are now imported from studentUtils.js
</script>
