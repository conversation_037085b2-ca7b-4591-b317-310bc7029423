<template>
  <div class="bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg p-6 flex flex-col items-center">
    <!-- Image Section -->
    <div class="mb-4 w-full overflow-hidden rounded-lg">
      <img :src="testimonial.image" :alt="testimonial.name" class="w-full h-56 object-cover" />
    </div>
    
    <!-- Name and Role Section -->
    <div class="text-center mb-5">
      <h3 class="text-xl font-bold text-gray-800">{{ testimonial.name }} <span class="font-normal">— {{ testimonial.role }}</span></h3>
    </div>
    
    <!-- Testimonial Text -->
    <p class="text-gray-700 mb-2 w-full">
      {{ testimonial.feedback }}
    </p>
    
    <!-- Rating Stars -->
    <div class="flex w-full">
      <div class="flex">
        <template v-for="star in 5" :key="star">
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            viewBox="0 0 24 24" 
            fill="currentColor" 
            class="w-5 h-5 text-yellow-500"
          >
            <path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
          </svg>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StudentCard',
  props: {
    testimonial: {
      type: Object,
      required: true
    }
  }
}
</script> 