<template>
    <div class="flex flex-col items-center justify-start min-h-screen bg-gray-50 overflow-x-hidden">
      <!-- Header with logo - fixed width container -->
      <div class="w-full border-b border-gray-300 py-4">
        <div class="container mx-auto px-4">
          <router-link to="/" class="flex items-center">
            <img src="/logo.png" alt="FlowCamp Logo" class="h-10" />
            <span class="ml-2 text-[#ff8c00] font-bold text-xl">Flow Camp</span>
          </router-link>
        </div>
      </div>

      <!-- Main content container - centered with max width -->
      <div class="container mx-auto flex justify-center px-4 py-8">
        <div class="w-full max-w-md">
          <!-- Profile positioned more accurately -->
          <div class="flex justify-center mb-6">
            <Profile />
          </div>

          <!-- Form content directly on the page without the white background container -->
          <div class="w-full">
            <h2 class="text-3xl font-bold text-center mb-2">Let's create your account</h2>
            <p class="text-center text-gray-600 mb-6">Enter your details to sign up</p>

            <!-- GoogleButton with full width to match input fields -->
            <div class="w-full flex justify-center">
              <GoogleButton class="w-full" />
            </div>

            <!-- OR Divider -->
            <div class="flex items-center justify-center my-4">
              <div class="w-1/3 border-t border-gray-300"></div>
              <span class="mx-4 text-sm text-gray-500">OR</span>
              <div class="w-1/3 border-t border-gray-300"></div>
            </div>


            <div v-if="formError" class="mb-4 p-3 bg-red-100 text-red-700 rounded-md text-sm">
              {{ formError }}
            </div>


            <form @submit.prevent="submitForm">
              <div class="mb-4">
                <input
                  type="text"
                  placeholder="Full Name"
                  v-model="fullName"
                  :class="{'border-red-500': errors.fullName}"
                  class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
                <p v-if="errors.fullName" class="text-red-500 text-xs mt-1">{{ errors.fullName }}</p>
              </div>
              <div class="mb-4">
                <input
                  type="email"
                  placeholder="Email"
                  v-model="email"
                  :class="{'border-red-500': errors.email}"
                  class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
                <p v-if="errors.email" class="text-red-500 text-xs mt-1">{{ errors.email }}</p>
              </div>
              <div class="mb-4">
                <input
                  type="tel"
                  placeholder="Phone"
                  v-model="phoneNumber"
                  :class="{'border-red-500': errors.phoneNumber}"
                  @keypress="phoneNumberOnly"
                  @paste="handlePaste"
                  class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
                <p v-if="errors.phoneNumber" class="text-red-500 text-xs mt-1">{{ errors.phoneNumber }}</p>
              </div>


              <div class="mb-4 relative">
                <input
                  :type="passwordVisible ? 'text' : 'password'"
                  placeholder="Password"
                  :class="{'border-red-500': errors.password}"
                  class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  v-model="password"
                />
                <button
                  type="button"
                  @click="togglePasswordVisibility"
                  class="absolute right-3 top-3 text-gray-500"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      v-if="passwordVisible"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                    <path
                      v-if="passwordVisible"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                    />
                    <path
                      v-if="!passwordVisible"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l18 18"
                    />
                  </svg>
                </button>
                <p v-if="errors.password" class="text-red-500 text-xs mt-1">{{ errors.password }}</p>
              </div>

              <div class="flex items-start mb-4">
                <input
                  type="checkbox"
                  v-model="agreeToTerms"
                  :class="{'border-red-500': errors.agreeToTerms}"
                  class="mt-1 mr-2"
                />
                <span class="text-sm text-gray-600">
                  I agree to the World's
                  <a href="#" class="text-blue-500 hover:text-blue-600">Terms of Use</a>,
                  <a href="#" class="text-blue-500 hover:text-blue-600">Privacy Policy</a>, and
                  <a href="#" class="text-blue-500 hover:text-blue-600">Data Processing Agreement</a>.
                </span>
              </div>
              <p v-if="errors.agreeToTerms" class="text-red-500 text-xs mb-4">{{ errors.agreeToTerms }}</p>


              <div class="w-full flex justify-center">
                <SignButton class="w-full" label="Sign Up" />
              </div>
            </form>

            <div class="text-center mt-4 mb-8">
              <div class="mb-2">
                <span class="text-sm text-gray-600">
                  Already have an account?
                  <router-link to="/login" style="color: #ff8c00;" class="font-medium hover:underline">Sign In</router-link>
                </span>
              </div>
              <div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer with copyright only -->
      <div class="w-full mt-auto border-t border-gray-200">
        <div class="container mx-auto px-4 py-4 flex justify-center items-center">
          <Copyright />
        </div>
      </div>
    </div>
  </template>

  <script>
  import GoogleButton from "@/components/GoogleButton.vue";
  import SignButton from "@/components/SignButton.vue";
  import Copyright from "@/components/Copyright.vue";
  import Profile from "@/components/Profile.vue";

  export default {
    name: "Register",
    components: {
      GoogleButton,
      SignButton,
      Copyright,
      Profile,
    },
    data() {
      return {
        fullName: "",
        email: "",
        phoneNumber: "",
        password: "",
        passwordVisible: false,
        agreeToTerms: false,
        formError: "",
        errors: {
          fullName: "",
          email: "",
          phoneNumber: "",
          password: "",
          agreeToTerms: ""
        }
      };
    },
    methods: {
      togglePasswordVisibility() {
        this.passwordVisible = !this.passwordVisible;
      },


      phoneNumberOnly(evt) {
        const charCode = (evt.which) ? evt.which : evt.keyCode;
        if (charCode == 8 || charCode == 9 || charCode == 13 ||
            charCode == 27 || charCode == 46 ||
            (charCode == 65 && evt.ctrlKey) ||
            (charCode == 67 && evt.ctrlKey) ||
            (charCode == 86 && evt.ctrlKey) ||
            (charCode == 88 && evt.ctrlKey) ||
            (charCode >= 35 && charCode <= 40)) {
          return;
        }


        if (charCode < 48 || charCode > 57) {
          evt.preventDefault();
        }
      },

      handlePaste(evt) {

        const clipboardData = evt.clipboardData || window.clipboardData;
        const pastedData = clipboardData.getData('Text');

        if (/[^\d]/g.test(pastedData)) {
          const numericValue = pastedData.replace(/[^\d]/g, '');


          evt.preventDefault();

          this.phoneNumber = numericValue;
        }
      },


      validateForm() {
        let isValid = true;


        this.formError = "";
        this.errors = {
          fullName: "",
          email: "",
          phoneNumber: "",
          password: "",
          agreeToTerms: ""
        };


        if (!this.fullName.trim()) {
          this.errors.fullName = "Full Name is required";
          isValid = false;
        }


        if (!this.email.trim()) {
          this.errors.email = "Email is required";
          isValid = false;
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.email)) {
          this.errors.email = "Please enter a valid email address";
          isValid = false;
        }


        if (!this.phoneNumber.trim()) {
          this.errors.phoneNumber = "Phone number is required";
          isValid = false;
        }

        if (!this.password) {
          this.errors.password = "Password is required";
          isValid = false;
        } else if (this.password.length < 6) {
          this.errors.password = "Password must be at least 6 characters";
          isValid = false;
        }


        if (!this.agreeToTerms) {
          this.errors.agreeToTerms = "You must agree to the Terms of Use";
          isValid = false;
        }

        if (!isValid) {
          this.formError = "Please fill in all required fields";
        }

        return isValid;
      },

      // Handle form submission
      submitForm() {
        if (this.validateForm()) {
          // Form is valid, proceed with submission
          console.log("Form submitted successfully");
          // Here you would typically make an API call to register the user

          // For demo purposes, redirect to the email verification page
          this.$router.push({
            name: 'email-verification',
            query: {
              email: this.email,
              source: 'register'
            }
          });
        } else {
          console.log("Form validation failed");
        }
      }
    },
  };
  </script>

  <style scoped>
  /* No need for absolute positioning anymore as we're using flex layout */
  </style>