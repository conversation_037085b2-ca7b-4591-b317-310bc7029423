<template>
  <nav class="flex items-center px-6 py-4 bg-white shadow-sm relative">
    <!-- Logo -->
    <router-link to="/" class="flex items-center gap-2 text-[#F2720C] font-bold text-lg ml-4 mr-10">
      <img src="/logo.png" alt="Flow Camp" class="h-6 w-auto" />
      Flow Camp
    </router-link>

    <!-- Nav Links -->
    <div class="flex gap-10 text-sm font-medium mx-auto">
      <router-link
        v-for="item in navItems"
        :key="item.name"
        :to="item.to"
        class="transition-colors duration-200"
        :class="isActiveRoute(item.to) ? 'text-[#F2720C]' : 'text-gray-500 hover:text-[#F2720C]'"
      >
        {{ item.name }}
      </router-link>
    </div>

    <!-- Right Section -->
    <div class="flex items-center gap-6">

   <!-- Notification Button -->
<div class="relative">
  <button @click="toggleNotification" class="focus:outline-none relative">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-black" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
        d="M15 17h5l-1.405-1.405M19 13V9a7 7 0 10-14 0v4l-1.405 1.405M5 17h5m4 0v1a3 3 0 11-6 0v-1h6z" />
    </svg>
    <span v-if="hasNewNotifications" class="absolute top-0 right-0 w-2.5 h-2.5 bg-red-500 rounded-full"></span>
  </button>

 <!-- Notification Dropdown -->
<div
  v-if="showNotification"
  class="absolute right-0 top-full mt-2 w-[350px] bg-white border border-gray-300 rounded-md shadow-md z-50"
>
  <div class="px-4 py-3 border-b">
    <h3 class="text-lg font-semibold">Notifications</h3>
  </div>

  <div class="divide-y max-h-80 overflow-y-auto">
    <div class="px-4 py-3 text-sm space-y-1">
      <div class="flex justify-between items-start">
        <p>
          📩 <span class="font-semibold">Assignment:</span> Jessica has submitted the task : Pengenalan golang & setup<br />
          
        </p>
        <span class="text-xs text-gray-500 whitespace-nowrap ml-2">Just now</span>
      </div>
    </div>

    <div class="px-4 py-3 text-sm space-y-1">
      <div class="flex justify-between items-start">
        <p>
          📩 <span class="font-semibold">Assignment:</span> Jasmine has submitted the task : Pengenalan golang & setup<br />
          
        </p>
        <span class="text-xs text-gray-500 whitespace-nowrap ml-2">Yesterday</span>
      </div>
    </div>
  </div>
</div>
</div>


      <!-- Profile -->
      <div class="relative">
        <img @click="toggleDropdown" src="/profilepic.png" alt="Profile"
          class="w-8 h-8 rounded-full object-cover cursor-pointer" />
        <div
          v-if="showDropdown"
          class="absolute right-0 top-full mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-md z-50"
        >
          <ul class="text-sm text-gray-700 py-2">
            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">My Profile</li>
            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">Settings</li>
            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">Logout</li>
          </ul>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const navItems = [
  { name: 'Home', to: '/academyhome' },
  { name: 'Academy', to: '/AcademyMentor' },
  { name: 'Classes', to: '/allclasses' }
]

const isActiveRoute = (path) => {
  const current = route.path.toLowerCase().replace(/\/$/, '')
  const target = path.toLowerCase().replace(/\/$/, '')
  return current === target || current.startsWith(target)
}

const showDropdown = ref(false)
const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}

const showNotification = ref(false)
const hasNewNotifications = ref(true)
const toggleNotification = () => {
  showNotification.value = !showNotification.value
  hasNewNotifications.value = false
}


function handleClickOutside(event) {
  const dropdown = document.querySelector('.relative')
  if (!event.target.closest('.relative')) {
    showDropdown.value = false
    showNotification.value = false
  }
}

onMounted(() => window.addEventListener('click', handleClickOutside))
onBeforeUnmount(() => window.removeEventListener('click', handleClickOutside))
</script>
