<template>
  <StudentLayoutWrapper contentClass="bg-gray-50">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-800">Exams</h1>
      <p class="text-gray-600 mt-1">View and manage your upcoming and completed exams</p>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white p-4 rounded-lg shadow-sm mb-6 flex flex-col sm:flex-row justify-between items-center gap-4">
      <!-- Search -->
      <div class="relative w-full sm:w-64">
        <input
          type="text"
          v-model="searchQuery"
          placeholder="Search exams..."
          class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
        />
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>

      <!-- Status Filter -->
      <div class="relative w-full sm:w-auto">
        <div class="flex items-center gap-2">
          <button
            @click="toggleStatusFilter"
            class="flex items-center justify-between w-full sm:w-40 px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            <span>{{ statusFilter || 'All Status' }}</span>
            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            @click="resetFilters"
            class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            Reset
          </button>
        </div>

        <!-- Status Dropdown -->
        <div
          v-if="statusFilterOpen"
          class="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10"
        >
          <div class="py-1">
            <button
              @click="setStatusFilter('')"
              class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              All Status
            </button>
            <button
              @click="setStatusFilter('upcoming')"
              class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              Upcoming
            </button>
            <button
              @click="setStatusFilter('in progress')"
              class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              In Progress
            </button>
            <button
              @click="setStatusFilter('completed')"
              class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              Completed
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Exams Grid -->
    <div v-if="isLoading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <div v-for="i in 6" :key="i" class="bg-white rounded-lg shadow-sm border border-gray-100 animate-pulse p-6">
        <div class="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
        <div class="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
        <div class="h-4 bg-gray-200 rounded w-2/3 mb-4"></div>
        <div class="flex gap-2 mb-4">
          <div class="h-6 bg-gray-200 rounded w-16"></div>
          <div class="h-6 bg-gray-200 rounded w-20"></div>
        </div>
        <div class="flex justify-between items-center">
          <div class="h-8 bg-gray-200 rounded w-24"></div>
          <div class="h-4 bg-gray-200 rounded-full w-16"></div>
        </div>
      </div>
    </div>

    <div v-else-if="filteredExams.length === 0" class="bg-white p-8 rounded-lg shadow-sm text-center">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No exams found</h3>
      <p class="text-gray-600 mb-4">
        {{ searchQuery || statusFilter ? 'Try adjusting your filters or search query' : 'You don\'t have any exams yet' }}
      </p>
      <button
        @click="resetFilters"
        v-if="searchQuery || statusFilter"
        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
      >
        Clear Filters
      </button>
    </div>

    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <div
        v-for="(exam, index) in filteredExams"
        :key="index"
        class="bg-white rounded-lg shadow-sm border border-gray-200 transition-all duration-300 hover:shadow-md hover:border-orange-200 hover:-translate-y-1 p-6"
      >
        <!-- Header with title and status -->
        <div class="flex justify-between items-start mb-4">
          <div class="flex-1 mr-3">
            <h3 class="text-lg font-bold text-gray-900 leading-tight mb-1">{{ exam.title }}</h3>
            <p class="text-sm text-gray-600 font-medium">{{ exam.category }}</p>
          </div>
          <span
            :class="{
              'px-3 py-1.5 rounded-full text-xs font-semibold flex-shrink-0 uppercase tracking-wide': true,
              'bg-green-100 text-green-700 border border-green-200': exam.status === 'completed',
              'bg-blue-100 text-blue-700 border border-blue-200': exam.status === 'in progress',
              'bg-yellow-100 text-yellow-700 border border-yellow-200': exam.status === 'upcoming'
            }"
          >
            {{ exam.status }}
          </span>
        </div>

        <!-- Description -->
        <p class="text-gray-600 text-sm mb-4 line-clamp-2 leading-relaxed">
          {{ exam.description || 'No description available' }}
        </p>

        <!-- Exam details -->
        <div class="flex flex-wrap gap-2 mb-6">
          <span class="inline-flex items-center text-xs bg-gray-50 text-gray-700 px-3 py-1.5 rounded-md border border-gray-200 font-medium">
            <svg class="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
            </svg>
            {{ exam.category }}
          </span>
          <span v-if="exam.questions" class="inline-flex items-center text-xs bg-orange-50 text-orange-700 px-3 py-1.5 rounded-md border border-orange-200 font-medium">
            <svg class="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
            </svg>
            {{ exam.questions }}
          </span>
          <span v-if="exam.points" class="inline-flex items-center text-xs bg-blue-50 text-blue-700 px-3 py-1.5 rounded-md border border-blue-200 font-medium">
            <svg class="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            {{ exam.points }}
          </span>
        </div>

        <!-- Action button and date info -->
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
          <button
            class="px-4 py-2.5 bg-orange-600 text-white rounded-lg text-sm font-semibold hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200 transform"
            :disabled="exam.status === 'completed'"
            :class="{ 'opacity-50 cursor-not-allowed hover:scale-100': exam.status === 'completed' }"
          >
            {{ exam.status === 'completed' ? 'Completed' : exam.status === 'in progress' ? 'Continue' : 'Start Exam' }}
          </button>
          <div v-if="exam.status === 'completed' || exam.status === 'upcoming'" class="text-xs text-gray-500 font-medium">
            {{ exam.status === 'completed' ? 'Completed on ' + exam.completedDate : 'Due ' + exam.dueDate }}
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div v-if="totalPages > 1" class="flex justify-center mt-6 mb-8">
      <nav class="flex items-center space-x-2" aria-label="Pagination">
        <button
          @click="currentPage > 1 && (currentPage--)"
          :disabled="currentPage === 1"
          class="px-3 py-1 rounded-md border border-gray-300 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Previous
        </button>
        <div v-for="page in totalPages" :key="page" class="hidden md:block">
          <button
            @click="currentPage = page"
            :class="{
              'px-3 py-1 rounded-md text-sm font-medium': true,
              'bg-orange-600 text-white': currentPage === page,
              'border border-gray-300 text-gray-700 hover:bg-gray-50': currentPage !== page
            }"
          >
            {{ page }}
          </button>
        </div>
        <span class="md:hidden text-sm text-gray-700">
          Page {{ currentPage }} of {{ totalPages }}
        </span>
        <button
          @click="currentPage < totalPages && (currentPage++)"
          :disabled="currentPage === totalPages"
          class="px-3 py-1 rounded-md border border-gray-300 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Next
        </button>
      </nav>
    </div>
  </StudentLayoutWrapper>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import StudentLayoutWrapper from '@/components/@student/StudentLayoutWrapper.vue';
import { useClassStore } from '@/data/availableClasses';

const classStore = useClassStore();

// Loading state
const isLoading = ref(true);

// Pagination
const currentPage = ref(1);
const itemsPerPage = 6;

// Filters
const searchQuery = ref('');
const statusFilter = ref('');
const statusFilterOpen = ref(false);

// Toggle status filter dropdown
const toggleStatusFilter = () => {
  statusFilterOpen.value = !statusFilterOpen.value;
};

// Set status filter
const setStatusFilter = (status) => {
  statusFilter.value = status;
  statusFilterOpen.value = false;
};

// Reset filters
const resetFilters = () => {
  searchQuery.value = '';
  statusFilter.value = '';
  currentPage.value = 1;
};

// Close dropdown when clicking outside
const closeStatusFilterOnClickOutside = (event) => {
  if (statusFilterOpen.value && !event.target.closest('.relative')) {
    statusFilterOpen.value = false;
  }
};

// Mock exam data - in a real app, this would come from an API
const exams = computed(() => {
  const examsList = [];

  // Generate exams from class materials with tasks
  const classes = classStore.classes?.value || [];

  classes.forEach(cls => {
    if (cls.materials) {
      cls.materials.forEach(material => {
        if (material.hasTask) {
          // Determine status based on task status
          let status = 'upcoming';
          if (material.taskStatus === 'completed') {
            status = 'completed';
          } else if (material.taskStatus === 'in_progress') {
            status = 'in progress';
          }

          examsList.push({
            title: material.title || `${cls.title} - Task`,
            status: status,
            category: cls.category || 'General',
            questions: material.taskQuestions ? `${material.taskQuestions} Questions` : 'Practical Task',
            points: material.taskPoints ? `Total Points: ${material.taskPoints}` : null,
            createdDate: material.postedDate,
            dueDate: material.dueDate,
            completedDate: material.submissionDate,
            description: material.description
          });
        }
      });
    }
  });

  return examsList;
});

// Process exams - no longer need gradients since we removed images
const processedExams = computed(() => {
  return exams.value;
});

// Filter exams based on search query and status
const filteredExams = computed(() => {
  const query = searchQuery.value.toLowerCase();
  const status = statusFilter.value.toLowerCase();

  // Filter by search query and status
  const filtered = processedExams.value.filter(exam => {
    const matchesQuery = exam.title.toLowerCase().includes(query) ||
                         (exam.description && exam.description.toLowerCase().includes(query)) ||
                         exam.category.toLowerCase().includes(query);
    const matchesStatus = !status || exam.status.toLowerCase() === status;

    return matchesQuery && matchesStatus;
  });

  // Calculate total pages
  const totalPages = Math.ceil(filtered.length / itemsPerPage);
  if (currentPage.value > totalPages && totalPages > 0) {
    currentPage.value = Math.max(1, totalPages);
  }

  // Get paginated results
  const startIndex = (currentPage.value - 1) * itemsPerPage;
  return filtered.slice(startIndex, startIndex + itemsPerPage);
});

// Calculate total pages
const totalPages = computed(() => {
  const query = searchQuery.value.toLowerCase();
  const status = statusFilter.value.toLowerCase();

  const filtered = processedExams.value.filter(exam => {
    const matchesQuery = exam.title.toLowerCase().includes(query) ||
                         (exam.description && exam.description.toLowerCase().includes(query)) ||
                         exam.category.toLowerCase().includes(query);
    const matchesStatus = !status || exam.status.toLowerCase() === status;

    return matchesQuery && matchesStatus;
  });

  return Math.ceil(filtered.length / itemsPerPage) || 1;
});

// Lifecycle hooks
onMounted(() => {
  // Add click event listener for closing status filter dropdown
  document.addEventListener('click', closeStatusFilterOnClickOutside);

  // Simulate loading data
  setTimeout(() => {
    isLoading.value = false;
  }, 800);
});

onUnmounted(() => {
  // Remove event listener
  document.removeEventListener('click', closeStatusFilterOnClickOutside);
});
</script>
