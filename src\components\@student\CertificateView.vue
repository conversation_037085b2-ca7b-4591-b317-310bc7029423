<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white rounded-lg p-4 sm:p-6 max-w-3xl w-full" @click.stop>
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg sm:text-xl font-semibold">E-Certificate</h3>
        <button @click="$emit('close')" class="text-gray-500 hover:text-gray-700 transition-colors duration-200 p-1 rounded-full hover:bg-gray-100">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Certificate Content -->
      <div class="border-4 border-orange-100 p-6 sm:p-8 bg-gradient-to-r from-orange-50 to-white rounded-lg shadow-md mb-4">
        <div class="text-center">
          <div class="mb-4">
            <img src="/logo.png" alt="FlowCamp Logo" class="h-12 mx-auto" />
          </div>

          <h2 class="text-xl sm:text-2xl md:text-3xl font-bold text-gray-800 mb-2">CERTIFICATE OF COMPLETION</h2>
          <p class="text-sm sm:text-base text-gray-600 mb-6">This certifies that</p>

          <h3 class="text-lg sm:text-xl md:text-2xl font-bold text-orange mb-2">{{ studentName }}</h3>

          <p class="text-sm sm:text-base text-gray-600 mb-6">has successfully completed the course</p>

          <h4 class="text-base sm:text-lg md:text-xl font-bold text-gray-800 mb-6">{{ classTitle }}</h4>

          <div class="flex justify-center items-center mb-6">
            <div class="w-16 h-px bg-gray-300"></div>
            <div class="mx-4 text-orange">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <div class="w-16 h-px bg-gray-300"></div>
          </div>

          <div class="flex justify-between items-center px-4 sm:px-8">
            <div class="text-center">
              <p class="text-xs sm:text-sm text-gray-500">Date</p>
              <p class="text-sm sm:text-base font-medium">{{ formattedDate }}</p>
            </div>

            <div class="text-center">
              <div class="mb-2">
                <img src="/choose.png" alt="Signature" class="h-10 mx-auto" onerror="this.src='/logo.png'; this.onerror=null;" />
              </div>
              <p class="text-xs sm:text-sm text-gray-500">Director</p>
              <p class="text-sm sm:text-base font-medium">FlowCamp</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Download Button -->
      <div class="flex justify-end">
        <button
          @click="downloadCertificate"
          class="bg-orange text-white px-4 py-2 rounded-md hover:bg-orange-dark transition-colors flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          Download Certificate
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CertificateView',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    classId: {
      type: [String, Number],
      required: true
    },
    classTitle: {
      type: String,
      required: true
    },
    studentName: {
      type: String,
      default: 'Student Name'
    },
    completionDate: {
      type: String,
      default: null
    }
  },
  emits: ['close', 'download'],
  computed: {
    formattedDate() {
      if (this.completionDate) {
        return this.completionDate;
      }

      // Default to current date if no completion date provided
      const now = new Date();
      const day = String(now.getDate()).padStart(2, '0');
      const month = now.toLocaleString('default', { month: 'long' });
      const year = now.getFullYear();

      return `${day} ${month} ${year}`;
    }
  },
  methods: {
    downloadCertificate() {
      // In a real application, this would generate and download a PDF certificate
      // For now, we'll just emit an event to show the success dialog

      // Validate class ID
      if (!this.classId) {
        console.error('Cannot download certificate: Missing class ID');
        return;
      }

      console.log(`Downloading certificate for class ${this.classId}: ${this.classTitle}`);

      // Emit download event with class ID
      this.$emit('download', this.classId);
    }
  }
}
</script>
