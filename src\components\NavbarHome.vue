<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();
const showDropdown = ref(false);
const dropdownRef = ref(null);

// Define navigation items
const navItems = [
  { name: 'Program', path: '/', isDropdown: true },
  { name: 'Bootcamp', path: '/bootcamp', isDropdown: false },
  { name: 'Free Class', path: '/freeclass', isDropdown: false }
];

// Track the selected dropdown option (default to 'Program')
const selectedOption = ref('Program');

// Determine if a route is active
const isActive = (path) => {
  return route.path === path;
};

// Computed property to check if we're on the home page
const isHomePage = computed(() => {
  return route.path === '/';
});

// Computed property to check if we're on the bootcamp page
const isBootcampPage = computed(() => {
  return route.path === '/bootcamp';
});

// Computed property to check if we're on the free class page
const isFreeClassPage = computed(() => {
  return route.path === '/freeclass';
});

const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value;
};

const handleMenuClick = (route, optionName) => {
  // Update the selected option when a dropdown item is clicked
  if (optionName) {
    selectedOption.value = optionName;
  }

  router.push(route);
  showDropdown.value = false;
};

const handleClickOutside = (event) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target)) {
    showDropdown.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);

  // Set the initial selected option based on the current route
  if (isBootcampPage.value) {
    selectedOption.value = 'Bootcamp';
  } else if (isFreeClassPage.value) {
    selectedOption.value = 'Free Class';
  } else {
    selectedOption.value = 'Program';
  }
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});

// Watch for route changes to update the selected option
watch(() => route.path, (newPath) => {
  // Update the selected option based on the current route
  if (newPath === '/bootcamp') {
    selectedOption.value = 'Bootcamp';
  } else if (newPath === '/freeclass') {
    selectedOption.value = 'Free Class';
  } else if (newPath === '/') {
    selectedOption.value = 'Program';
  }
}, { immediate: true });
</script>

<template>
  <nav class="bg-white shadow-sm py-4">
    <div class="container mx-auto px-4">
      <div class="flex items-center justify-between">
        <!-- Left Section: Logo and Dropdown -->
        <div class="flex items-center space-x-8">
          <!-- Logo -->
          <router-link to="/" class="flex items-center">
            <img src="/logo.png" alt="Flow Camp" class="h-10" />
            <span class="text-orange-500 font-bold text-xl ml-2">Flowcamp</span>
          </router-link>

          <!-- Program Dropdown -->
          <div class="relative ml-4" ref="dropdownRef">
            <button
              class="flex items-center px-4 py-2 rounded-md transition-colors duration-200 relative"
              :class="[
                isHomePage || isBootcampPage || isFreeClassPage
                  ? 'text-orange-500 font-medium'
                  : 'text-gray-700 hover:text-orange-500 font-medium'
              ]"
              @click="toggleDropdown"
              :aria-expanded="showDropdown"
            >
              <span>{{ selectedOption }}</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 ml-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>

            </button>

            <!-- Dropdown Menu -->
            <div
              class="absolute left-0 mt-1 w-72 bg-white rounded-2xl shadow-lg py-4 z-10"
              :class="{ 'hidden': !showDropdown }"
            >
              <ul class="py-1 px-2 space-y-2">
                <li>
                  <a
                    class="flex items-center justify-between px-4 py-3 text-black rounded-full cursor-pointer transition-all duration-200"
                    :class="[
                      isBootcampPage
                        ? 'bg-orange-100 border border-orange-200'
                        : 'bg-white border border-gray-200 hover:bg-gray-50'
                    ]"
                    href="#"
                    @click.prevent="handleMenuClick('/bootcamp', 'Bootcamp')"
                  >
                    <span>Bootcamp</span>
                    <span class="text-xl">🎓</span>
                  </a>
                </li>
                <li>
                  <a
                    class="flex items-center justify-between px-4 py-3 text-black rounded-full cursor-pointer transition-all duration-200"
                    :class="[
                      isFreeClassPage
                        ? 'bg-orange-100 border border-orange-200'
                        : 'bg-white border border-gray-200 hover:bg-gray-50'
                    ]"
                    href="#"
                    @click.prevent="handleMenuClick('/freeclass', 'Free Class')"
                  >
                    <span>Free Class</span>
                    <span class="text-xl">🎥</span>
                  </a>
                </li>
                <li>
                  <a
                    class="flex items-center justify-between px-4 py-3 text-black rounded-full cursor-pointer transition-all duration-200"
                    :class="[
                      isHomePage && selectedOption === 'Program'
                        ? 'bg-orange-100 border border-orange-200'
                        : 'bg-white border border-gray-200 hover:bg-gray-50'
                    ]"
                    href="#"
                    @click.prevent="handleMenuClick('/', 'Program')"
                  >
                    <span>Program</span>
                    <span class="text-xl">📚</span>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>

      </div>
    </div>
  </nav>
</template>
