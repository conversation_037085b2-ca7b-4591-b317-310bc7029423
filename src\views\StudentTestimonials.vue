<template>
  <StudentLayoutWrapper contentClass="bg-gray-50">
    <!-- Header Section with subtle animation -->
    <div class="mb-8 animate-fade-in">
      <h1 class="text-3xl font-bold text-gray-800 mb-2">Write Your Testimonial</h1>
      <p class="text-gray-600 text-lg">Share your experience and help others discover Flow Camp</p>
    </div>

    <!-- Testimonial Form Card with enhanced visual hierarchy -->
    <div class="bg-white rounded-xl shadow-md transition-all duration-300 hover:shadow-lg mb-10">
      <div class="p-8">
        <!-- Success State -->
        <div v-if="isSubmitted" class="text-center py-12 animate-scale-in">
          <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-800 mb-2">Thank You for Your Feedback!</h3>
          <p class="text-gray-600 mb-6">Your testimonial has been submitted successfully.</p>
          <button
            @click="resetForm"
            class="px-4 py-2 bg-orange-600 text-white rounded-md text-sm font-medium hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
          >
            Write Another Testimonial
          </button>
        </div>

        <!-- Form State -->
        <form v-else @submit.prevent="submitTestimonial" class="space-y-8">
          <!-- Class Selection with custom dropdown -->
          <div class="form-group relative">
            <label for="class" class="block text-lg font-medium text-gray-700 mb-2">
              Which class would you like to review?
            </label>
            <div class="relative">
              <button
                type="button"
                @click="isDropdownOpen = !isDropdownOpen"
                class="relative w-full bg-white border border-gray-300 rounded-lg py-3 px-4 text-left cursor-pointer focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                <span class="block truncate">
                  {{ testimonial.classId ? getClassName(testimonial.classId) : 'Select your completed class' }}
                </span>
                <span class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                  <svg
                    class="h-5 w-5 text-gray-400 transition-transform duration-200"
                    :class="{ 'transform rotate-180': isDropdownOpen }"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </span>
              </button>

              <transition
                enter-active-class="transition duration-100 ease-out"
                enter-from-class="transform scale-95 opacity-0"
                enter-to-class="transform scale-100 opacity-100"
                leave-active-class="transition duration-75 ease-in"
                leave-from-class="transform scale-100 opacity-100"
                leave-to-class="transform scale-95 opacity-0"
              >
                <div
                  v-if="isDropdownOpen"
                  class="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-lg py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm"
                >
                  <div
                    v-for="cls in completedClasses"
                    :key="cls.id"
                    @click="selectClass(cls)"
                    class="cursor-pointer select-none relative py-3 px-4 hover:bg-orange-50 transition-colors duration-200"
                    :class="{
                      'bg-orange-50': testimonial.classId === cls.id,
                      'text-gray-900': testimonial.classId === cls.id,
                      'text-gray-700': testimonial.classId !== cls.id
                    }"
                  >
                    <div class="flex items-center">
                      <span class="font-medium block truncate">{{ cls.title }}</span>
                      <span
                        v-if="testimonial.classId === cls.id"
                        class="text-orange-600 absolute inset-y-0 right-0 flex items-center pr-4"
                      >
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                      </span>
                    </div>
                  </div>
                </div>
              </transition>
            </div>
            <p v-if="errors.classId" class="mt-2 text-sm text-red-600 animate-shake">{{ errors.classId }}</p>
          </div>

          <!-- Rating with improved star interaction -->
          <div class="form-group">
            <label class="block text-lg font-medium text-gray-700 mb-3">How would you rate this class?</label>
            <div class="flex items-center space-x-2">
              <div class="flex space-x-1">
                <button
                  v-for="star in 5"
                  :key="star"
                  type="button"
                  @click="testimonial.rating = star"
                  @mouseenter="hoverRating = star"
                  @mouseleave="hoverRating = 0"
                  class="focus:outline-none transform transition-transform hover:scale-110"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-10 w-10 transition-colors duration-200"
                    :class="
                      star <= (hoverRating || testimonial.rating) 
                        ? 'text-yellow-400' 
                        : 'text-gray-300'
                    "
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                </button>
              </div>
              <span class="ml-3 text-lg text-gray-600 min-w-[100px]">
                {{ ratingText[testimonial.rating - 1] || 'Select rating' }}
              </span>
            </div>
            <p v-if="errors.rating" class="mt-2 text-sm text-red-600 animate-shake">{{ errors.rating }}</p>
          </div>

          <!-- Testimonial Text with character counter -->
          <div class="form-group">
            <label for="testimonial" class="block text-lg font-medium text-gray-700 mb-2">Your Experience</label>
            <textarea
              id="testimonial"
              v-model="testimonial.text"
              rows="6"
              class="block w-full px-4 py-3 text-base border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200"
              placeholder="Tell us about your learning experience, what you enjoyed most, and how it helped you grow..."
              required
              maxlength="500"
            ></textarea>
            <div class="mt-2 flex justify-between items-center">
              <p v-if="errors.text" class="text-sm text-red-600 animate-shake">{{ errors.text }}</p>
              <span 
                class="text-sm"
                :class="testimonial.text.length > 450 ? 'text-orange-600' : 'text-gray-500'"
              >
                {{ testimonial.text.length }}/500
              </span>
            </div>
          </div>

          <!-- Permission Toggle with improved styling -->
          <div class="form-group">
            <label class="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                v-model="testimonial.permission"
                class="sr-only peer"
              >
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-500"></div>
              <span class="ml-3 text-sm font-medium text-gray-700">
                Allow Flow Camp to share my testimonial publicly
              </span>
            </label>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end">
            <button
              type="submit"
              class="px-6 py-3 bg-orange-600 text-white rounded-lg text-base font-medium hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              :disabled="isSubmitting"
            >
              <span v-if="isSubmitting" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Submitting...
              </span>
              <span v-else>Submit Testimonial</span>
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Previous Testimonials Section -->
    <div class="mb-10">
      <h2 class="text-xl font-semibold text-gray-800 mb-4">Your Previous Testimonials</h2>
      
      <div v-if="isLoading" class="space-y-4">
        <div v-for="i in 2" :key="i" class="bg-white p-4 rounded-lg shadow-sm border border-gray-100 animate-pulse">
          <div class="flex justify-between items-start mb-4">
            <div>
              <div class="h-5 bg-gray-200 rounded w-40 mb-2"></div>
              <div class="h-4 bg-gray-200 rounded w-24"></div>
            </div>
            <div class="flex">
              <div class="w-6 h-6 bg-gray-200 rounded-full mr-1"></div>
              <div class="w-6 h-6 bg-gray-200 rounded-full mr-1"></div>
              <div class="w-6 h-6 bg-gray-200 rounded-full mr-1"></div>
              <div class="w-6 h-6 bg-gray-200 rounded-full mr-1"></div>
              <div class="w-6 h-6 bg-gray-200 rounded-full"></div>
            </div>
          </div>
          <div class="h-16 bg-gray-200 rounded w-full mb-3"></div>
          <div class="h-4 bg-gray-200 rounded w-32"></div>
        </div>
      </div>

      <div v-else-if="userTestimonials.length === 0" class="bg-white p-6 rounded-lg shadow-sm text-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
        </svg>
        <h3 class="text-lg font-medium text-gray-800 mb-1">No testimonials yet</h3>
        <p class="text-gray-600">You haven't submitted any testimonials yet.</p>
      </div>

      <div v-else class="space-y-4">
        <div
          v-for="(item, index) in userTestimonials"
          :key="index"
          class="bg-white p-4 rounded-lg shadow-sm border border-gray-100 transition-all duration-300 hover:shadow-md"
        >
          <div class="flex justify-between items-start mb-4">
            <div>
              <h3 class="font-medium text-gray-800">{{ getClassName(item.classId) }}</h3>
              <p class="text-sm text-gray-500">Submitted on {{ item.date }}</p>
            </div>
            <div class="flex">
              <svg
                v-for="star in 5"
                :key="star"
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                :class="star <= item.rating ? 'text-yellow-400' : 'text-gray-300'"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            </div>
          </div>
          <p class="text-gray-700 mb-3">{{ item.text }}</p>
          <div class="flex items-center text-xs text-gray-500">
            <span v-if="item.permission" class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              Public
            </span>
            <span v-else class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              Private
            </span>
          </div>
        </div>
      </div>
    </div>
  </StudentLayoutWrapper>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import StudentLayoutWrapper from '@/components/@student/StudentLayoutWrapper.vue';
import { useClassStore } from '@/data/availableClasses';

const classStore = useClassStore();

// Loading state
const isLoading = ref(true);
const isSubmitting = ref(false);
const isSubmitted = ref(false);

// Form data
const testimonial = ref({
  classId: '',
  rating: 0,
  text: '',
  permission: false
});

// Form validation errors
const errors = ref({
  classId: '',
  rating: '',
  text: ''
});

// Rating text descriptions
const ratingText = [
  'Poor',
  'Fair',
  'Good',
  'Very Good',
  'Excellent'
];

// Get completed classes for the dropdown
const completedClasses = computed(() => {
  const classes = classStore.classes?.value || [];
  return classes.filter(cls => cls.progress === 100);
});

// Get class name by ID
const getClassName = (classId) => {
  const cls = completedClasses.value.find(c => c.id === classId);
  return cls ? cls.title : 'Unknown Class';
};

// User's previous testimonials
const userTestimonials = ref([]);

// Load user testimonials from localStorage
const loadUserTestimonials = () => {
  try {
    const storedTestimonials = localStorage.getItem('user_testimonials');
    if (storedTestimonials) {
      userTestimonials.value = JSON.parse(storedTestimonials);
    }
  } catch (error) {
    console.error('Error loading testimonials:', error);
    userTestimonials.value = [];
  }
};

// Save testimonials to localStorage
const saveUserTestimonials = () => {
  try {
    localStorage.setItem('user_testimonials', JSON.stringify(userTestimonials.value));
  } catch (error) {
    console.error('Error saving testimonials:', error);
  }
};

// Validate form
const validateForm = () => {
  let isValid = true;
  errors.value = {
    classId: '',
    rating: '',
    text: ''
  };

  if (!testimonial.value.classId) {
    errors.value.classId = 'Please select a class';
    isValid = false;
  }

  if (testimonial.value.rating < 1) {
    errors.value.rating = 'Please select a rating';
    isValid = false;
  }

  if (!testimonial.value.text.trim()) {
    errors.value.text = 'Please enter your testimonial';
    isValid = false;
  } else if (testimonial.value.text.length > 500) {
    errors.value.text = 'Testimonial must be 500 characters or less';
    isValid = false;
  }

  return isValid;
};

// Submit testimonial
const submitTestimonial = () => {
  if (!validateForm()) {
    return;
  }

  isSubmitting.value = true;

  // Simulate API call
  setTimeout(() => {
    // Create new testimonial object
    const newTestimonial = {
      id: Date.now(),
      classId: testimonial.value.classId,
      rating: testimonial.value.rating,
      text: testimonial.value.text,
      permission: testimonial.value.permission,
      date: new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    };

    // Add to user testimonials
    userTestimonials.value.unshift(newTestimonial);
    
    // Save to localStorage
    saveUserTestimonials();

    // Reset form and show success message
    isSubmitting.value = false;
    isSubmitted.value = true;
  }, 1000);
};

// Reset form after submission
const resetForm = () => {
  testimonial.value = {
    classId: '',
    rating: 0,
    text: '',
    permission: false
  };
  isSubmitted.value = false;
};

// Dropdown state
const isDropdownOpen = ref(false);

// Select class from dropdown
const selectClass = (cls) => {
  testimonial.value.classId = cls.id;
  isDropdownOpen.value = false;
};

// Lifecycle hooks
onMounted(() => {
  // Load user testimonials
  loadUserTestimonials();
  
  // Simulate loading data
  setTimeout(() => {
    isLoading.value = false;
  }, 800);

  // Click outside handler to close dropdown
  document.addEventListener('click', (e) => {
    const dropdown = document.querySelector('.form-group.relative');
    if (dropdown && !dropdown.contains(e.target)) {
      isDropdownOpen.value = false;
    }
  });
});
</script>

<style>
.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

.animate-shake {
  animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes shake {
  10%, 90% { transform: translateX(-1px); }
  20%, 80% { transform: translateX(2px); }
  30%, 50%, 70% { transform: translateX(-4px); }
  40%, 60% { transform: translateX(4px); }
}
</style>
