<script setup>
defineProps({
  student: {
    type: Object,
    required: true,
  },
  rank: {
    type: Number,
    required: true,
  },
  isSearchActive: {
    type: Boolean,
    default: false
  }
});

// Function to get initial letter for avatar fallback
function getInitial(name) {
  return name.charAt(0).toUpperCase();
}

// Handle image loading error by using a fallback image from randomuser.me
function handleImageError(event) {
  // Generate a random but consistent image based on the student's name
  const seed = event.target.alt.length % 99; // Use name length as a simple hash
  const gender = seed % 2 === 0 ? 'men' : 'women';

  // Use randomuser.me with a consistent seed for reproducibility
  event.target.src = `https://randomuser.me/api/portraits/${gender}/${seed}.jpg`;

  console.log('Student image failed to load, using randomuser.me fallback');
}
</script>

<template>
  <tr>
    <td class="px-2 sm:px-4 py-2 sm:py-3 text-center">
      <!-- Rank Badge -->
      <div class="flex justify-center">
        <div
          class="w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center"
          :class="{
            'bg-yellow-500 text-white': !isSearchActive && rank === 1,
            'bg-gray-500 text-white': !isSearchActive && rank === 2,
            'bg-amber-600 text-white': !isSearchActive && rank === 3,
            'text-black': isSearchActive && rank > 3,
          }"
        >
          <span class="text-sm sm:text-base font-bold">{{ rank }}</span>
        </div>
      </div>
    </td>
    <td class="px-2 sm:px-4 py-2 sm:py-3">
      <div class="flex items-center">
        <img
          :src="student.profilePicture"
          :alt="student.name"
          @error="handleImageError"
          class="w-8 h-8 sm:w-10 sm:h-10 rounded-full object-cover mr-2 sm:mr-3 border border-gray-200"
        />
        <div>
          <div class="text-sm sm:text-base font-medium text-gray-900 truncate max-w-[100px] sm:max-w-none">{{ student.name }}</div>
          <div class="text-xs sm:text-sm text-gray-500">Student</div>
        </div>
      </div>
    </td>
    <td class="px-2 sm:px-4 py-2 sm:py-3 text-center">
      <span
        class="inline-flex items-center px-1.5 sm:px-2.5 py-0.5 rounded-full text-xs sm:text-sm font-medium"
          :class="{
            'bg-yellow-500 text-white': !isSearchActive && rank === 1,
            'bg-gray-500 text-white': !isSearchActive && rank === 2,
            'bg-amber-600 text-white': !isSearchActive && rank === 3,
            'text-black': isSearchActive && rank > 3,
          }"
      >
        {{ student.mark }}
      </span>
    </td>
  </tr>
</template>
