<template>
  <div class="relative w-full h-full min-h-[450px] flex items-center justify-center overflow-visible">
    <div class="absolute w-4/5 h-4/5 max-w-[350px] max-h-[350px] bg-orange-500 rounded-full z-[1]"></div>
    <img :src="studentImage" :alt="imageAlt" class="relative z-[2] max-h-[95%] max-w-[95%]" />
    
    <!-- Circular Outlines -->
    <div class="absolute w-[450px] h-[450px] border border-yellow-400 rounded-full z-0 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"></div>
    <div class="absolute w-[350px] h-[350px] border border-yellow-400 rounded-full z-0 -bottom-[2%] right-0 pointer-events-none"></div>
    
    <!-- Additional Empty Circles (unfilled) -->
    <div class="absolute w-[150px] h-[150px] border-2 border-yellow-400 rounded-full z-0 top-[60%] left-[10%]"></div>
    <div class="absolute w-[200px] h-[200px] border-2 border-yellow-400 rounded-full z-0 top-[50%] left-[5%]"></div>
    <div class="absolute w-[250px] h-[250px] border-2 border-yellow-400 rounded-full z-0 top-[45%] left-[-5%]"></div>
    <div class="absolute w-[300px] h-[300px] border-2 border-yellow-400 rounded-full z-0 top-[35%] left-[-10%]"></div>
    
    <!-- Badges -->
    <div class="absolute top-[10%] left-[5%] z-10">
      <div class="border-2 border-cyan-700 rounded-lg shadow-md bg-white overflow-hidden">
        <div class="flex items-center bg-white p-2 px-3 rounded-md">
          <div class="w-9 h-9 mr-2 bg-[#006D77] rounded flex items-center justify-center">
            <img src="/laptop.png" alt="Laptop Icon" class="w-6 h-6" />
          </div>
          <div>
            <h6 class="mb-0 font-bold">{{ videoBadgeCount }}</h6>
            <small class="text-gray-500">{{ videoBadgeText }}</small>
          </div>
        </div>
      </div>
    </div>
    
    <div class="absolute bottom-[30%] -right-[5%] z-10">
      <div class="border-2 border-blue-600 rounded-lg shadow-md bg-white overflow-hidden">
        <div class="flex items-center bg-white p-2 px-3 rounded-md">
          <!-- Correct User Icon for Assisted Students -->
          <div class="w-9 h-9 mr-2 bg-[#006D77] rounded flex items-center justify-center">
            <img src="/assistedbadge.png" alt="Assisted Badge" class="w-6 h-6" />
          </div>
          <div>
            <h6 class="mb-0 font-bold">{{ studentBadgeCount }}</h6>
            <small class="text-gray-500">{{ studentBadgeText }}</small>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  studentImage: {
    type: String,
    required: true
  },
  imageAlt: {
    type: String,
    required: true
  },
  videoBadgeCount: {
    type: String,
    required: true
  },
  videoBadgeText: {
    type: String,
    required: true
  },
  studentBadgeCount: {
    type: String,
    required: true
  },
  studentBadgeText: {
    type: String,
    required: true
  }
});
</script> 