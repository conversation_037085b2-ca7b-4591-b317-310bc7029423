<script>
import NavbarHome from '@/components/NavbarHome.vue';
import Footer from '@/components/Footer.vue';
import { watch } from 'vue';
import { useRoute } from 'vue-router';

export default {
  name: 'App',
  components: {
    NavbarHome,
    Footer
  },
  setup() {
    const route = useRoute();

    // Watch for route changes to debug routing issues
    watch(() => route.fullPath, (newPath) => {
      console.log('=== APP ROUTE DEBUG ===');
      console.log('App route changed to:', newPath);
      console.log('Route name:', route.name);
      console.log('Route meta:', route.meta);
      console.log('Route params:', route.params);
      console.log('Route query:', route.query);
      console.log('hideNavbarAndFooter:', route.meta.hideNavbarAndFooter);
    }, { immediate: true });

    return {};
  }
}
</script>

<template>
  <div class="app">
    <!-- Only show NavbarHome and Footer on pages that aren't excluded -->
    <NavbarHome v-if="!$route.meta.hideNavbarAndFooter" />

    <main>
      <router-view />
    </main>

    <Footer v-if="!$route.meta.hideNavbarAndFooter" />
  </div>
</template>
