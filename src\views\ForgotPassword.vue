<template>
  <div class="flex flex-col items-center justify-center min-h-screen bg-gray-50">
    <div class="w-full max-w-md px-4 pt-12 pb-8 bg-white rounded-xl shadow-md border border-gray-200">
      <div class="flex justify-center mb-6">
        <div class="bg-gray-100 rounded-full p-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 11c1.104 0 2-.896 2-2V7a2 2 0 10-4 0v2c0 1.104.896 2 2 2zm6 2v5a2 2 0 01-2 2H8a2 2 0 01-2-2v-5a2 2 0 012-2h8a2 2 0 012 2z" />
          </svg>
        </div>
      </div>
      <h2 class="text-2xl font-bold text-center mb-2">Reset Your Password</h2>
      <p class="text-center text-gray-500 mb-6">Please enter a new password for your account</p>
      <div v-if="formError" class="mb-4 p-3 bg-red-100 text-red-700 rounded-md text-sm">
        {{ formError }}
      </div>
      <form @submit.prevent="submitForm">
        <div class="mb-4">
          <input
            type="password"
            placeholder="Password"
            v-model="password"
            :class="['w-full p-3 border rounded-md focus:outline-none focus:ring-2', errors.password ? 'border-red-500' : 'border-gray-300']"
          />
          <p v-if="errors.password" class="text-red-500 text-xs mt-1">{{ errors.password }}</p>
        </div>
        <div class="mb-6">
          <input
            type="password"
            placeholder="Confirm password"
            v-model="confirmPassword"
            :class="['w-full p-3 border rounded-md focus:outline-none focus:ring-2', errors.confirmPassword ? 'border-red-500' : 'border-gray-300']"
          />
          <p v-if="errors.confirmPassword" class="text-red-500 text-xs mt-1">{{ errors.confirmPassword }}</p>
        </div>
        <button
          type="submit"
          class="w-full bg-gradient-to-r from-orange-400 to-orange-600 text-white font-semibold py-3 rounded-md hover:from-orange-500 hover:to-orange-700 transition mb-2"
        >
          Reset Password
        </button>
      </form>
      <div class="text-center mt-6">
        <span class="text-sm text-gray-500">
          Need help?
          <a href="#" class="font-medium text-orange-600 hover:underline">Contact Us</a>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "SetPassword",
  data() {
    return {
      password: "",
      confirmPassword: "",
      formError: "",
      errors: {
        password: "",
        confirmPassword: "",
      },
    };
  },
  methods: {
    validateForm() {
      this.errors.password = "";
      this.errors.confirmPassword = "";
      this.formError = "";
      let valid = true;
      if (!this.password) {
        this.errors.password = "Password is required";
        valid = false;
      } else if (this.password.length < 6) {
        this.errors.password = "Password must be at least 6 characters";
        valid = false;
      }
      if (!this.confirmPassword) {
        this.errors.confirmPassword = "Please confirm your password";
        valid = false;
      } else if (this.password !== this.confirmPassword) {
        this.errors.confirmPassword = "Passwords do not match";
        valid = false;
      }
      if (!valid) {
        this.formError = "Please correct the errors before submitting";
      }
      return valid;
    },
    submitForm() {
      if (this.validateForm()) {
        // Submit password to API or handle success
        alert("Password set successfully!");
        // Redirect to login page
        this.$router.push('/login');
      }
    },
  },
};
</script>

<style scoped>
input::placeholder {
  color: #bdbdbd;
  opacity: 1;
}
</style>
