<template>
  <div v-if="show" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
    <div class="bg-white rounded-xl shadow-xl w-full max-w-2xl p-8 relative">
      <button @click="$emit('close')" class="absolute top-4 right-4 text-gray-400 hover:text-gray-700">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
      <div class="flex items-center gap-4 mb-6">
        <img :src="student.image" class="w-16 h-16 rounded-full object-cover border-2" />
        <div>
          <div class="font-semibold text-lg">{{ student.name }}</div>
        </div>
        <div class="ml-auto text-right">
          <span v-if="student.status && student.status.toLowerCase().includes('overdue')" class="text-red-500 font-semibold">Overdue</span>
          <span v-else class="text-green-500 font-semibold">On Time</span>
          <div class="text-xs text-gray-500 mt-1">Submitted on {{ student.submissionDate }}{{ student.submissionTime ? ', at ' + student.submissionTime : '' }}</div>
        </div>
      </div>
      <!-- Keterangan Penilaian -->
      <div class="mb-4">
        <div class="bg-orange-100 border-l-4 border-[#F2720C] rounded px-4 py-2 text-sm text-gray-700">
          <div class="font-semibold mb-1">Review Criteria:</div>
          <ul class="list-disc pl-5">
            <li><span class="font-bold">Highly Accurate</span> &rarr; Exceeds expectations by meeting all standards exceptionally well in both steps and output.</li>
            <li><span class="font-bold">Accurate</span> &rarr; Fully meets all standards in both steps and output.</li>
            <li><span class="font-bold">Fairly Accurate</span> &rarr; Meets the standards, though there are some errors in steps or output.</li>
            <li><span class="font-bold">Slightly Accurate</span> &rarr; Many errors in steps, input, and output, but some standards are met.</li>
            <li><span class="font-bold">Inaccurate</span> &rarr; Steps, input, and output are completely incorrect.</li>
          </ul>
        </div>
      </div>
      <!-- Link Submission -->
      <div class="mb-4">
        <a :href="student.link || '#'" target="_blank" class="block border px-4 py-2 rounded text-blue-600 hover:underline">{{ student.link || 'No link submitted' }}</a>
      </div>
      <!-- Penilaian -->
      <div class="mb-4">
        <div class="font-semibold mb-2">Submission Assessment</div>
        <div class="flex gap-2">
          <button
            v-for="option in reviewOptions"
            :key="option.value"
            @click="selectedReview = option.value"
            :class="[selectedReview === option.value ? 'border-[#F2720C] bg-[#F2720C] text-white' : 'border-gray-300 bg-white text-gray-700', 'border px-4 py-2 rounded font-semibold transition']"
          >
            {{ option.label }}
          </button>
        </div>
      </div>
      <!-- Grades -->
      <div class="mb-4">
        <label class="block font-semibold mb-1">Grades</label>
        <input type="number" v-model.number="grades" min="0" max="100" class="border px-4 py-2 rounded w-32" />
      </div>
      <!-- Submit -->
      <button
        @click="submitReview"
        class="w-full border border-[#F2720C] text-[#F2720C] font-semibold py-2 rounded transition hover:bg-[#F2720C] hover:text-white"
      >
        Submit
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted } from 'vue'
import { useClassStore } from '@/data/availableClasses'

const props = defineProps({
  student: { type: Object, required: true },
  show: { type: Boolean, default: false }
})
const emit = defineEmits(['close', 'submit'])

// Initialize class store
const classStore = useClassStore()

const reviewOptions = [
  { label: 'Highly Accurate', value: 'highly_accurate' },
  { label: 'Accurate', value: 'accurate' },
  { label: 'Fairly Accurate', value: 'fairly_accurate' },
  { label: 'Slightly Accurate', value: 'slightly_accurate' },
  { label: 'Inaccurate', value: 'inaccurate' },
]
const selectedReview = ref('')
const grades = ref(props.student.grade ?? 0)
const isReviewing = ref(false)

// When the component is mounted, update the task status to 'under_review'
onMounted(() => {
  if (props.student && props.student.classId && props.student.materialId && props.student.taskStatus === 'submitted') {
    // Update task status to 'under_review' when a mentor starts reviewing
    isReviewing.value = true
    classStore.updateTaskStatus(props.student.classId, props.student.materialId, 'under_review')
  }
})

watch(() => props.student, (val) => {
  grades.value = val.grade ?? 0
  selectedReview.value = ''

  // Check if we need to update the task status to 'under_review'
  if (val && val.classId && val.materialId && val.taskStatus === 'submitted') {
    isReviewing.value = true
    classStore.updateTaskStatus(val.classId, val.materialId, 'under_review')
  }
})

function submitReview() {
  if (props.student && props.student.classId && props.student.materialId) {
    // Update task status to 'reviewed' when a mentor completes the review
    classStore.updateTaskStatus(props.student.classId, props.student.materialId, 'reviewed')

    // Add the grade to the material
    const material = classStore.findMaterial(props.student.classId, props.student.materialId)
    if (material) {
      material.taskScore = grades.value
      material.taskReview = selectedReview.value
    }
  }

  // Emit the review data to the parent component
  emit('submit', {
    review: selectedReview.value,
    grades: grades.value,
    taskStatus: 'reviewed'
  })

  emit('close')
}
</script>