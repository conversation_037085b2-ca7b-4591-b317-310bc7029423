<template>
  <div class="max-w-6xl mx-auto px-6 py-8 space-y-6">
    <!-- Breadcrumb -->
    <div class="text-sm text-gray-500 flex items-center gap-2">
      <router-link to="/AllClasses" class="text-orange hover:underline font-medium">Class Camp</router-link>
      <span>›</span>
      <router-link :to="{ name: 'DetailClassCompleted', params: { title: classTitle } }" class="text-orange hover:underline font-medium">
        {{ classTitle }}
      </router-link>
      <span>›</span>
      <span class="text-gray-700 font-medium">Modul</span>
    </div>

    <!-- Add Resource Dropdown -->
    <div class="relative w-fit">
      <button
        @click="toggleAddResourceDropdown"
        class="bg-orange hover:bg-orange-dark text-white px-4 py-2 rounded-md flex items-center gap-2 transition-colors duration-200"
      >
        add resources
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.085l3.71-3.855a.75.75 0 111.08 1.04l-4.25 4.417a.75.75 0 01-1.08 0L5.25 8.27a.75.75 0 01-.02-1.06z" clip-rule="evenodd" />
        </svg>
      </button>

      <div v-if="showAddResource" class="absolute mt-2 bg-white border rounded-md shadow-md w-48 z-20">
        <button class="w-full flex items-center px-4 py-2 text-sm hover:bg-gray-100" @click="openPopup('material')">
          <img src="/materialicon.png" class="w-4 h-4 mr-2" />
          material
        </button>
        <button class="w-full flex items-center px-4 py-2 text-sm hover:bg-gray-100" @click="openPopup('assignment')">
          <img src="/taskicon.png" class="w-4 h-4 mr-2" />
          assignment
        </button>
        <button class="w-full flex items-center px-4 py-2 text-sm hover:bg-gray-100" @click="openPopup('recording')">
          <img src="/videoicon.png" class="w-4 h-4 mr-2" />
          recording
        </button>
      </div>
    </div>

    <!-- Modul List -->
    <div class="space-y-2">
      <div v-for="(modul, idx) in modules" :key="idx" class="border-b">
        <!-- Modul header -->
        <div
          class="flex items-center justify-between py-3 cursor-pointer hover:bg-gray-50 transition-all duration-200"
          @click="toggleDropdown(idx)"
        >
          <div class="flex items-center gap-3">
            <img src="/folder.png" class="w-5 h-5" />
            <span class="text-gray-800 font-medium">{{ modul.title }}</span>
          </div>
          <svg
            class="h-4 w-4 text-gray-600 transition-transform duration-200"
            :class="{ 'transform rotate-180': openDropdown[idx] }"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </div>

        <!-- Modul Content -->
        <div v-if="openDropdown[idx]" class="space-y-3 pl-8 pt-2 pb-4">
          <div
            v-for="(item, itemIdx) in modul.items"
            :key="itemIdx"
            class="bg-white border rounded-md shadow-sm px-4 py-3 relative"
          >
            <div class="flex items-start gap-3">
              <img :src="item.icon" class="w-5 h-5 mt-1" />
              <div class="flex-1">
                <p class="font-medium text-sm text-gray-700">{{ item.title }}</p>
                <p class="text-xs text-gray-500 mt-1">{{ item.description }}</p>
              </div>
              <!-- Dots menu -->
              <div class="relative">
                <button @click.stop="toggleItemMenu(idx, itemIdx)">
                  <svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M6 10a2 2 0 11-4 0 2 2 0 014 0zm6-2a2 2 0 100 4 2 2 0 000-4zm6 2a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </button>
                <div
                  v-if="showMenu[idx] && showMenu[idx][itemIdx]"
                  class="absolute right-0 mt-2 w-28 bg-white border rounded-md shadow-lg z-10"
                >
                  <button
                    class="w-full px-4 py-2 text-left text-sm hover:bg-gray-100"
                    @click="openEditModal(modul.title, item)"
                  >
                    Edit
                  </button>
                  <button
                    class="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 text-red-600"
                    @click="handleDelete(idx, itemIdx)"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modals & Popups -->
    <EditModul
      :visible="isEditModalOpen"
      :modulTitle="selectedModulTitle"
      :initialData="selectedItem"
      :moduleOptions="modules.map(m => m.title)"
      @close="isEditModalOpen = false"
      @save="handleSaveEdit"
    />

    <EditSaved :show="showEditSaved" @close="showEditSaved = false" />
    <DeleteModul v-if="showDeleteModal" @close="showDeleteModal = false" @confirm="confirmDelete" />
    <PopUpDelete v-if="showDeletedPopup" @close="showDeletedPopup = false" />
    <!-- Add this at the bottom inside your template -->
<PopUpPost v-if="showPostPopup" @close="showPostPopup = false" />

<!-- Update each popup to emit "posted" -->
<PopUpMaterial
  v-if="activePopup === 'material'"
  @close="activePopup = ''"
  @posted="triggerPostPopup"
/>
<PopUpAssignment
  v-if="activePopup === 'assignment'"
  @close="activePopup = ''"
  @posted="triggerPostPopup"
/>
<PopUpVideo
  v-if="activePopup === 'recording'"
  @close="activePopup = ''"
  @posted="triggerPostPopup"
/>

  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRoute } from 'vue-router'

import EditModul from '@/components/EditModul.vue'
import EditSaved from '@/components/EditSaved.vue'
import DeleteModul from '@/components/DeleteModul.vue'
import PopUpDelete from '@/components/PopUpDelete.vue'

import PopUpMaterial from '@/components/PopUpMaterial.vue'
import PopUpAssignment from '@/components/PopUpAssignment.vue'
import PopUpVideo from '@/components/PopUpVideo.vue'
import PopUpPost from '@/components/PopUpPost.vue'


const route = useRoute()
const classTitle = route.params.title || 'Unknown Class'

const modules = ref([
  {
    title: 'Pengenalan Golang & Setup',
    items: [
      { icon: '/ppticon.png', title: 'pengenalan struktur', description: 'Deskripsi materi' },
      { icon: '/videoicon.png', title: 'recording pengenalan struktur', description: '' },
      { icon: '/taskicon.png', title: 'Task 2', description: 'Deadline 23 Jan 2024' }
    ]
  },
  { title: 'Struktur Data dalam Golang', items: [] }
])

const openDropdown = ref(Array(modules.value.length).fill(false))
const showMenu = ref({})
const selectedItem = ref({})
const selectedModulTitle = ref('')
const isEditModalOpen = ref(false)
const selectedModulIndex = ref(null)
const selectedItemIndex = ref(null)
const showEditSaved = ref(false)
const showDeleteModal = ref(false)
const showDeletedPopup = ref(false)
const showAddResource = ref(false)
const activePopup = ref('')

const toggleDropdown = index => (openDropdown.value[index] = !openDropdown.value[index])
const toggleItemMenu = (modulIdx, itemIdx) => {
  if (!showMenu.value[modulIdx]) showMenu.value[modulIdx] = {}
  showMenu.value[modulIdx][itemIdx] = !showMenu.value[modulIdx][itemIdx]
}

const openEditModal = (modulTitle, item) => {
  selectedModulTitle.value = modulTitle
  selectedItem.value = { title: modulTitle, description: item.description, itemTitle: item.title }
  isEditModalOpen.value = true
}

const handleSaveEdit = () => {
  isEditModalOpen.value = false
  showEditSaved.value = true
}

const handleDelete = (modulIdx, itemIdx) => {
  selectedModulIndex.value = modulIdx
  selectedItemIndex.value = itemIdx
  showDeleteModal.value = true
}

const confirmDelete = () => {
  const { value: m } = modules
  if (selectedModulIndex.value !== null && selectedItemIndex.value !== null) {
    m[selectedModulIndex.value].items.splice(selectedItemIndex.value, 1)
  }
  showDeleteModal.value = false
  showDeletedPopup.value = true
}

const toggleAddResourceDropdown = () => {
  showAddResource.value = !showAddResource.value
}

const openPopup = (type) => {
  activePopup.value = type
  showAddResource.value = false
}

const showPostPopup = ref(false)

const triggerPostPopup = () => {
  showPostPopup.value = true
  setTimeout(() => {
    showPostPopup.value = false
  }, 2000)
}

</script>


