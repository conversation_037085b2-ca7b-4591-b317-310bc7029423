<template>
  <div class="w-full mb-4 flex items-center justify-center sm:justify-start">
    <nav class="overflow-hidden" aria-label="Breadcrumb">
      <div class="overflow-x-auto [scrollbar-width:none] [-ms-overflow-style:none] [&::-webkit-scrollbar]:hidden">
        <ol class="flex flex-nowrap items-center space-x-1 sm:space-x-2 pb-1">
          <li v-for="(item, index) in items" :key="index" class="flex items-center whitespace-nowrap">
            <!-- Separator between items -->
            <div v-if="index > 0" class="mx-1 sm:mx-2 flex-shrink-0">
              <svg class="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
            </div>

            <!-- Breadcrumb Item -->
            <div class="flex items-center">
              <template v-if="item.active">
                <span class="inline-block text-orange font-medium text-sm sm:text-[15px] truncate max-w-[100px] xs:max-w-[150px] sm:max-w-none">{{ item.title }}</span>
              </template>
              <template v-else>
                <a
                  v-if="item.onClick"
                  @click="item.onClick"
                  class="inline-block text-gray-500 hover:text-orange transition-colors duration-200 cursor-pointer text-sm sm:text-[15px] truncate max-w-[100px] xs:max-w-[150px] sm:max-w-none"
                >
                  {{ item.title }}
                </a>
                <router-link
                  v-else
                  :to="item.path"
                  class="inline-block text-gray-500 hover:text-orange transition-colors duration-200 text-sm sm:text-[15px] truncate max-w-[100px] xs:max-w-[150px] sm:max-w-none"
                >
                  {{ item.title }}
                </router-link>
              </template>
            </div>
          </li>
        </ol>
      </div>
    </nav>
  </div>
</template>

<script setup>
defineProps({
  items: {
    type: Array,
    required: true,
    validator: (items) => {
      return items.every(item =>
        'title' in item &&
        ('active' in item || 'path' in item)
      );
    }
  }
});
</script>