<template> 
  <div>
    <NavbarAcademy />

    <div class="pl-10 pr-6 sm:pl-14 sm:pr-8 md:pl-20 py-10">
      <!-- FILTER TABS -->
      <div class="flex gap-2 mb-8">
        <button
          v-for="tab in tabs"
          :key="tab"
          @click="activeTab = tab"
          class="px-6 py-2 rounded-lg font-medium transition-colors duration-200"
          :class="activeTab === tab
            ? 'bg-[#F2720C] text-white shadow'
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'"
        >
          {{ tab === 'all' ? 'All Classes' : tab === 'studied' ? 'Classes Studied' : 'Classes Completed' }}
        </button>
      </div>

      <!-- CLASS CARD LIST -->
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        <CardClassMentor
          v-for="(cls, idx) in filteredClasses"
          :key="idx"
          :data="cls"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import NavbarAcademy from '@/components/NavbarAcademy.vue'
import CardClassMentor from '@/components/CardClassMentor.vue'

const tabs = ['all', 'studied', 'completed']
const activeTab = ref('all')

const defaultImage = "/mentorCard1.png"

const classList = [
  {
    title: "Mastery In Golang: From Zero to Hero",
    image: "/mentorCard1.png",
    modules: 12,
    students: 21342,
    rating: 4.5,
    status: "completed"
  },
  {
    title: "Design UI/UX dengan menggunakan Figma",
    image: "/mentorCard2.png",
    modules: 12,
    students: 21342,
    status: "studied"
  },
  {
    title: "Mastering UI/UX: Principles, Best Practices, Applications for Designing Intuitive and...",
    image: "/mentorCard3.png",
    modules: 12,
    students: 21342,
    status: "completed"
  },
  {
    title: "Mastering UI/UX: Principles, Best Practices, Applications for Designing Intuitive and...",
    image: "/mentorCard4.png",
    modules: 12,
    students: 21342,
    status: "studied"
  },
  {
    title: "Backend Simpel, Hasil Maksimal!",
    image: "/mentorCard5.png",
    modules: 12,
    students: 21342,
    status: "all"
  },
  {
    title: "Bikin WebSocket & SSR dengan Express.js!",
    image: "",
    modules: 12,
    students: 21342,
    status: "all"
  }
]

const filteredClasses = computed(() => {
  let list = classList.map(cls => ({
    ...cls,
    image: cls.image && cls.image.length > 0 ? cls.image : defaultImage
  }))
  if (activeTab.value === 'all') return list
  return list.filter(cls => cls.status === activeTab.value)
})
</script>
