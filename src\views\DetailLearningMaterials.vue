<template>
  <StudentLayoutWrapper>
    <StudentBreadcrumb :items="breadcrumbItems" />

    <div class="max-w-5xl mx-auto">
        <!-- Title & Posted Date -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6">
          <h1 class="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-2 sm:mb-0">{{ currentMaterial?.title || 'Loading...' }}</h1>
          <span class="text-gray-500 text-xs sm:text-sm">Posted {{ formatDate(currentMaterial?.postedDate || currentClass?.postedDate) }} {{ currentMaterial?.postedTime || currentClass?.postedTime }}</span>
        </div>

        <div class="prose max-w-none">
          <!-- Material Content -->
          <div class="bg-white rounded-lg mb-6 sm:mb-8">
            <div class="space-y-3 sm:space-y-4">
              <p class="text-sm sm:text-base text-gray-700 leading-relaxed">{{ currentMaterial?.description || 'Loading course content...' }}</p>

              <!-- Learning Objectives -->
              <div v-if="currentMaterial" class="mt-6 sm:mt-8">
                <h3 class="text-base sm:text-lg font-medium text-gray-800 mb-2 sm:mb-3">Learning Objectives</h3>
                <ul class="list-disc pl-4 sm:pl-5 space-y-1 sm:space-y-2">
                  <li v-for="(objective, index) in currentMaterial.objectives"
                      :key="index"
                      class="text-sm sm:text-base text-gray-700">
                    {{ objective }}
                  </li>
                </ul>

                <h3 class="text-base sm:text-lg font-medium text-gray-800 mt-4 sm:mt-6 mb-2 sm:mb-3">Key Topics</h3>
                <ul class="list-disc pl-4 sm:pl-5 space-y-1 sm:space-y-2">
                  <li v-for="(topic, index) in currentMaterial.topics"
                      :key="index"
                      class="text-sm sm:text-base text-gray-700">
                    {{ topic }}
                  </li>
                </ul>

                <!-- Presentation Section -->
                <div class="mt-6 sm:mt-8">
                  <!-- PPT Viewer -->
                  <div class="mb-4 sm:mb-6">
                    <div class="flex items-center">
                      <img src="/ppt.png" alt="PPT" class="w-5 h-5 sm:w-6 sm:h-6 mr-2" />
                      <h3 @click="handlePptClick"
                          class="text-sm sm:text-base font-medium text-orange underline cursor-pointer hover:text-orange-dark">
                        {{ currentMaterial?.title }}
                      </h3>
                    </div>
                  </div>

                  <!-- Video Section -->
                  <div>
                    <div class="flex items-center">
                      <img src="/video.png" alt="Video" class="w-5 h-5 sm:w-6 sm:h-6 mr-2" />
                      <h3 @click="handleVideoClick"
                          class="text-sm sm:text-base font-medium text-orange underline cursor-pointer hover:text-orange-dark">
                        Recording {{ currentMaterial?.title }}
                      </h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Task Information (if material has task) -->
          <div v-if="currentMaterial?.hasTask" class="mt-4 sm:mt-6 p-3 sm:p-4 bg-orange-50 rounded-lg border border-orange">
            <div class="flex items-center mb-2 sm:mb-3">
              <img src="/task.png" alt="Task" class="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
              <h3 class="text-base sm:text-lg font-semibold text-gray-800">Task</h3>
            </div>
            <p class="text-sm sm:text-base text-gray-700 mb-2 sm:mb-3">{{ currentMaterial.taskDescription }}</p>

            <!--
            <div class="flex justify-between items-center">
              <span class="text-xs sm:text-sm font-medium px-2 py-1 rounded-full border"
                    :class="getStatusClass(currentMaterial.taskStatus)">
                {{ formatTaskStatus(currentMaterial.taskStatus) }}
              </span>
            </div>
            -->

          </div>

          <!-- Mark as Read Button -->
          <div class="flex justify-end mt-6 sm:mt-8">
            <button
              @click="handleMarkAsRead"
              class="px-4 sm:px-6 py-1.5 sm:py-2 rounded-md transition-colors text-sm sm:text-base"
              :class="[
                currentMaterial?.isRead
                  ? 'bg-gray-400 text-white cursor-not-allowed'
                  : 'bg-orange text-white hover:bg-orange-dark'
              ]"
              :disabled="currentMaterial?.isRead"
            >
              <span v-if="currentMaterial?.isRead">Has been marked as read</span>
              <span v-else>Mark as read</span>
            </button>
          </div>

        </div>
      </div>

    <!-- Dialog Box -->
    <DialogBox
      :show="showSuccessDialog"
      type="markAsRead"
      :show-success="true"
      @close="closeSuccessDialog"
    />

    <DialogBox
      :show="showPptDialog"
      type="ppt"
      :has-file="!!materialFiles?.presentation"
      @close="closePptDialog"
      @confirm="downloadPresentation"
    />

    <DialogBox
      :show="showVideoDialog"
      type="video"
      :has-file="!!materialFiles?.video"
      @close="closeVideoDialog"
      @confirm="watchOnYoutube"
    />
  </StudentLayoutWrapper>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import StudentLayoutWrapper from '@/components/@student/StudentLayoutWrapper.vue';
import StudentBreadcrumb from '@/components/@student/StudentBreadcrumb.vue';
import DialogBox from '@/components/@student/DialogBox.vue';
import { useClassStore, STORAGE_KEYS } from '@/data/availableClasses';
import { formatDate, formatTaskStatus, getStatusClass } from '@/utils/studentUtils';

export default {
  name: 'DetailLearningMaterials',
  components: {
    StudentLayoutWrapper,
    StudentBreadcrumb,
    DialogBox
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    const classStore = useClassStore();
    const materialFiles = ref(null);

    const showSuccessDialog = ref(false);
    const showPptDialog = ref(false);
    const showVideoDialog = ref(false);

    // Close dialog functions

    const closePptDialog = () => {
      showPptDialog.value = false;
    };

    const closeVideoDialog = () => {
      showVideoDialog.value = false;
    };

    // Handle clicks
    const handlePptClick = () => {
      showPptDialog.value = true;
    };

    const handleVideoClick = () => {
      showVideoDialog.value = true;
    };

    const loadMaterialFiles = async () => {
      if (route.params.classId && route.params.materialId) {
        materialFiles.value = await classStore.getMaterialFiles(
          parseInt(route.params.classId),
          route.params.materialId
        );
      }
    };

    const downloadPresentation = () => {
      if (materialFiles.value?.presentation?.url) {
        window.open(materialFiles.value.presentation.url, '_blank');
      }
      showPptDialog.value = false;
    };

    const watchOnYoutube = () => {
      if (materialFiles.value?.video?.url) {
        window.open(materialFiles.value.video.url, '_blank');
      }
      showVideoDialog.value = false;
    };

    // Initialize data
    onMounted(async () => {
      try {
        classStore.setCurrentClass(parseInt(route.params.classId));

        classStore.setCurrentMaterial(route.params.materialId);

        await loadMaterialFiles();

        if (!classStore.currentMaterial.value) {
          router.push({
            name: 'LearningMaterials',
            params: { classId: route.params.classId }
          });
        }
      } catch (error) {
        console.error('Error loading material:', error);
      }
    });

    const currentClass = computed(() => classStore.currentClass.value);
    const currentMaterial = computed(() => classStore.currentMaterial.value);

    // We'll read the class source directly in the computed property
    // This ensures we always have the latest value

    const breadcrumbItems = computed(() => {
      // Always check localStorage directly to ensure we have the latest value
      const currentSource = localStorage.getItem(STORAGE_KEYS.CLASS_SOURCE) || 'studied';

      let firstBreadcrumbTitle = 'Classes Studied';
      let firstBreadcrumbPath = '/student/academy';

      // If class is completed or source is 'completed', show "Completed Classes"
      if (currentClass.value?.status === 'completed' || currentSource === 'completed') {
        firstBreadcrumbTitle = 'Completed Classes';
        firstBreadcrumbPath = {
          path: '/student/academy',
          query: { tab: 'completed' }
        };
      }

      return [
        {
          title: firstBreadcrumbTitle,
          path: firstBreadcrumbPath
        },
        {
          title: currentClass.value?.title || 'Class',
          path: `/student/class/${route.params.classId}`
        },
        {
          title: 'Learning Materials',
          path: `/student/class/${route.params.classId}/materials`
        },
        {
          title: currentMaterial.value?.title || 'Loading...',
          active: true
        }
      ];
    });

    // formatTaskStatus, getStatusClass, and formatDate are now imported from studentUtils.js

    const handleMarkAsRead = () => {
      if (!currentMaterial.value || currentMaterial.value.isRead) {
        return;
      }

      const success = classStore.markMaterialAsRead(
        parseInt(route.params.classId),
        route.params.materialId
      );

      if (success) {
        // Also update the current material in memory to ensure UI consistency
        currentMaterial.value.isRead = true;

        // Sync the material data with the class to ensure data consistency
        classStore.syncMaterialWithClass(
          parseInt(route.params.classId),
          route.params.materialId,
          { isRead: true }
        );

        showSuccessDialog.value = true;
        // The DialogBox component will auto-close after 1 second
      }
    };

    // Handle dialog close event with navigation
    const closeSuccessDialog = () => {
      showSuccessDialog.value = false;
      router.push({
        name: 'LearningMaterials',
        params: { classId: route.params.classId }
      });
    };

    return {
      currentClass,
      currentMaterial,
      materialFiles,
      breadcrumbItems,
      showSuccessDialog,
      showPptDialog,
      showVideoDialog,
      handlePptClick,
      handleVideoClick,
      closeSuccessDialog,
      closePptDialog,
      closeVideoDialog,
      downloadPresentation,
      watchOnYoutube,
      handleMarkAsRead,
      formatTaskStatus,
      getStatusClass,
      formatDate
    };
  }
};
</script>
