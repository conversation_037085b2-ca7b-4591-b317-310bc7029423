<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white rounded-lg p-4 sm:p-6 max-w-md w-full" @click.stop>
      <!-- Mark as Read Success Dialog -->
      <template v-if="type === 'markAsRead'">
        <div class="text-center py-4 sm:py-8">
          <div class="mb-4 sm:mb-6">
            <div class="rounded-full bg-orange w-12 h-12 sm:w-16 sm:h-16 mx-auto flex items-center justify-center">
              <svg class="w-6 h-6 sm:w-8 sm:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
          </div>
          <h3 class="text-lg sm:text-xl font-semibold mb-2">Materials Read</h3>
          <p class="text-sm sm:text-base text-gray-600">Materials marked as read</p>
        </div>
      </template>

      <!-- PPT Dialog -->
      <template v-if="type === 'ppt'">
        <div v-if="hasFile" class="space-y-3 sm:space-y-4">
          <div class="flex justify-between items-center mb-2 sm:mb-4">
            <h3 class="text-base sm:text-lg font-semibold">View Presentation</h3>
            <button @click="$emit('close')" class="text-gray-500 hover:text-gray-700"></button>
          </div>
          <p class="text-sm sm:text-base text-gray-600">Would you like to view the presentation?</p>
          <div class="flex justify-end space-x-3 sm:space-x-4">
            <button @click="$emit('close')" class="px-3 sm:px-4 py-1.5 sm:py-2 text-sm sm:text-base text-gray-600 hover:text-gray-800">
              Cancel
            </button>
            <button @click="$emit('confirm')" class="bg-orange text-white px-3 sm:px-4 py-1.5 sm:py-2 text-sm sm:text-base rounded-md hover:bg-orange-dark">
              Download
            </button>
          </div>
        </div>
        <div v-else class="text-center py-4 sm:py-8" v-show="showFailedDialog">
          <div class="rounded-full bg-orange w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 flex items-center justify-center">
            <svg class="w-6 h-6 sm:w-8 sm:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <h3 class="text-lg sm:text-xl font-semibold mb-2">Failed</h3>
          <p class="text-sm sm:text-base text-gray-600">PowerPoint unavailable</p>
        </div>
      </template>

      <!-- Video Dialog -->
      <template v-if="type === 'video'">
        <div v-if="hasFile" class="space-y-3 sm:space-y-4">
          <div class="flex justify-between items-center mb-2 sm:mb-4">
            <h3 class="text-base sm:text-lg font-semibold">Watch Video</h3>
            <button @click="$emit('close')" class="text-gray-500 hover:text-gray-700"></button>
          </div>
          <p class="text-sm sm:text-base text-gray-600">Would you like to watch the video?</p>
          <div class="flex justify-end space-x-3 sm:space-x-4">
            <button @click="$emit('close')" class="px-3 sm:px-4 py-1.5 sm:py-2 text-sm sm:text-base text-gray-600 hover:text-gray-800">
              Cancel
            </button>
            <button @click="$emit('confirm')" class="bg-orange text-white px-3 sm:px-4 py-1.5 sm:py-2 text-sm sm:text-base rounded-md hover:bg-orange-dark">
              Watch the video
            </button>
          </div>
        </div>
        <div v-else class="text-center py-4 sm:py-8" v-show="showFailedDialog">
          <div class="rounded-full bg-orange w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 flex items-center justify-center">
            <svg class="w-6 h-6 sm:w-8 sm:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <h3 class="text-lg sm:text-xl font-semibold mb-2">Failed</h3>
          <p class="text-sm sm:text-base text-gray-600">Recording unavailable</p>
        </div>
      </template>

      <!-- Task Complete Dialog -->
      <template v-if="type === 'taskComplete'">
        <div class="text-center py-4 sm:py-8">
          <div class="mb-4 sm:mb-6">
            <div class="rounded-full bg-orange w-12 h-12 sm:w-16 sm:h-16 mx-auto flex items-center justify-center">
              <svg class="w-6 h-6 sm:w-8 sm:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
          </div>
          <h3 class="text-lg sm:text-xl font-semibold mb-2">Task Complete</h3>
          <p class="text-sm sm:text-base text-gray-600">Task submitted successfully!</p>
        </div>
      </template>

      <!-- Feedback Sent Dialog -->
      <template v-if="type === 'feedbackSent'">
        <div class="text-center py-4 sm:py-8">
          <div class="mb-4 sm:mb-6">
            <div class="rounded-full bg-orange w-12 h-12 sm:w-16 sm:h-16 mx-auto flex items-center justify-center">
              <svg class="w-6 h-6 sm:w-8 sm:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
          </div>
          <h3 class="text-lg sm:text-xl font-semibold mb-2">Feedback Sent</h3>
          <p class="text-sm sm:text-base text-gray-600">Feedback received. Thank you!</p>
        </div>
      </template>

      <!-- Certificate Downloaded Dialog -->
      <template v-if="type === 'certificateDownloaded'">
        <div class="text-center py-4 sm:py-8">
          <div class="mb-4 sm:mb-6">
            <div class="rounded-full bg-orange w-12 h-12 sm:w-16 sm:h-16 mx-auto flex items-center justify-center">
              <svg class="w-6 h-6 sm:w-8 sm:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
          </div>
          <h3 class="text-lg sm:text-xl font-semibold mb-2">Certificate Downloaded</h3>
          <p class="text-sm sm:text-base text-gray-600">Certificate complete downloaded</p>
        </div>
      </template>

      <!-- View Grades Dialog -->
      <template v-if="type === 'viewGrades'">
        <div class="py-1">
          <div class="flex justify-between items-center mb-5 sm:mb-6">
            <h3 class="text-lg sm:text-xl font-semibold text-gray-800">Task Assessment</h3>
            <button @click="$emit('close')" class="text-gray-500 hover:text-gray-700 transition-colors duration-200 p-1 rounded-full hover:bg-gray-100">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <!-- Grade Score Circle -->
          <div class="mb-6 sm:mb-8 flex flex-col items-center">
            <div class="relative mb-3">
              <div :class="['w-28 h-28 sm:w-36 sm:h-36 rounded-full flex items-center justify-center bg-gradient-to-br border-4 shadow-sm', gradeColorClass]">
                <div class="text-center">
                  <div :class="['text-3xl sm:text-4xl font-bold', gradeScoreColorClass]">
                    {{ gradeData.score }}
                  </div>
                  <div :class="['text-sm font-medium', gradeScoreColorClass]">
                    /{{ gradeData.maxScore }}
                  </div>
                </div>
              </div>
            </div>
            <div :class="['text-sm sm:text-base font-medium px-4 py-1 rounded-full border', gradeTextColorClass]">
              {{ gradeText }}
            </div>
          </div>

          <!-- Submission Details -->
          <div class="bg-gray-50 rounded-lg border border-gray-200 p-4 sm:p-5">
            <h4 class="text-sm sm:text-base font-medium text-gray-700 mb-3">Submission Details</h4>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              <div class="flex items-center p-3 bg-white rounded-md border border-gray-100 shadow-sm">
                <div class="mr-3 bg-blue-100 p-2 rounded-full">
                  <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <div class="text-xs text-gray-500">Submitted on</div>
                  <div class="text-sm font-medium">{{ gradeData.submittedOn }}</div>
                </div>
              </div>
              <div class="flex items-center p-3 bg-white rounded-md border border-gray-100 shadow-sm">
                <div class="mr-3 bg-green-100 p-2 rounded-full">
                  <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <div class="text-xs text-gray-500">Reviewed on</div>
                  <div class="text-sm font-medium">{{ gradeData.reviewedOn }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DialogBox',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      required: true,
      validator: function(value) {
        return ['markAsRead', 'ppt', 'video', 'taskComplete', 'viewGrades', 'feedbackSent', 'certificateDownloaded'].indexOf(value) !== -1
      }
    },
    hasFile: {
      type: Boolean,
      default: true
    },
    gradeData: {
      type: Object,
      default: () => ({
        score: 0,
        maxScore: 100,
        submittedOn: '',
        reviewedOn: ''
      })
    }
  },
  computed: {
    gradeText() {
      if (!this.gradeData || !this.gradeData.score) return 'Not Graded';

      const percentage = (this.gradeData.score / this.gradeData.maxScore) * 100;

      if (percentage >= 90) return 'Excellent!';
      if (percentage >= 80) return 'Great Work!';
      if (percentage >= 70) return 'Good Job!';
      if (percentage >= 60) return 'Satisfactory';
      return 'Needs Improvement';
    },
    gradeColorClass() {
      if (!this.gradeData || !this.gradeData.score) return 'border-gray-200 from-gray-50 to-gray-100';

      const percentage = (this.gradeData.score / this.gradeData.maxScore) * 100;

      if (percentage >= 90) return 'border-green-200 from-green-50 to-green-100';
      if (percentage >= 80) return 'border-blue-200 from-blue-50 to-blue-100';
      if (percentage >= 70) return 'border-teal-200 from-teal-50 to-teal-100';
      if (percentage >= 60) return 'border-yellow-200 from-yellow-50 to-yellow-100';
      return 'border-red-200 from-red-50 to-red-100';
    },
    gradeTextColorClass() {
      if (!this.gradeData || !this.gradeData.score) return 'text-gray-700 bg-gray-50 border-gray-200';

      const percentage = (this.gradeData.score / this.gradeData.maxScore) * 100;

      if (percentage >= 90) return 'text-green-700 bg-green-50 border-green-200';
      if (percentage >= 80) return 'text-blue-700 bg-blue-50 border-blue-200';
      if (percentage >= 70) return 'text-teal-700 bg-teal-50 border-teal-200';
      if (percentage >= 60) return 'text-yellow-700 bg-yellow-50 border-yellow-200';
      return 'text-red-700 bg-red-50 border-red-200';
    },
    gradeScoreColorClass() {
      if (!this.gradeData || !this.gradeData.score) return 'text-gray-600';

      const percentage = (this.gradeData.score / this.gradeData.maxScore) * 100;

      if (percentage >= 90) return 'text-green-600';
      if (percentage >= 80) return 'text-blue-600';
      if (percentage >= 70) return 'text-teal-600';
      if (percentage >= 60) return 'text-yellow-600';
      return 'text-red-600';
    }
  },
  emits: ['close', 'confirm'],
  data() {
    return {
      showFailedDialog: true,
      failedDialogTimer: null
    }
  },
  watch: {
    show(newValue) {
      if (newValue) {
        if (!this.hasFile && (this.type === 'ppt' || this.type === 'video')) {
          this.showFailedDialog = true
          this.startFailedDialogTimer()
        } else if (this.type === 'taskComplete' || this.type === 'markAsRead' || this.type === 'feedbackSent' || this.type === 'certificateDownloaded') {
          // Auto close task complete, mark as read, feedback sent, and certificate downloaded dialogs after 1 second
          this.startAutoCloseTimer()
        }
        // viewGrades dialog should stay open until user closes it
      }
    }
  },
  methods: {
    startFailedDialogTimer() {
      this.failedDialogTimer = setTimeout(() => {
        if (!this.hasFile) {
          this.$emit('close')
        }
      }, 1000)
    },
    startAutoCloseTimer() {
      this.failedDialogTimer = setTimeout(() => {
        this.$emit('close')
      }, 1000)
    }
  },
  beforeUnmount() {
    if (this.failedDialogTimer) {
      clearTimeout(this.failedDialogTimer)
    }
  }
}
</script>